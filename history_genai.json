[{"role": "model", "parts": ["```yaml\nbrowser_component:\n  tab_title: *********** - Guidewire PolicyCenter\n  url: ai.iscs.com/innovation\n  address_bar_focused: false\n  tab_state: idle\nwebpage:\n  header:\n  - component_type: container\n    id: main_header\n    bounds:\n      x: 0\n      y: 69\n      width: 1920\n      height: 61\n    subcomponents:\n    - component_type: image\n      id: logo_american_integrity\n      label: AMERICAN INTEGRITY\n      bounds:\n        x: 16\n        y: 116\n        width: 128\n        height: 40\n    - component_type: navigation_menu\n      id: top_navigation\n      bounds:\n        x: 1290\n        y: 116\n        width: 600\n        height: 36\n      subcomponents:\n      - component_type: link\n        id: link_home\n        label: Home\n        bounds:\n          x: 1298\n          y: 126\n          width: 40\n          height: 20\n      - component_type: link\n        id: link_quote_policy\n        label: Quote/Policy\n        state: active\n        bounds:\n          x: 1362\n          y: 126\n          width: 80\n          height: 20\n      - component_type: link\n        id: link_claims\n        label: Claims\n        bounds:\n          x: 1466\n          y: 126\n          width: 48\n          height: 20\n      - component_type: link\n        id: link_cabinets\n        label: Cabinets\n        bounds:\n          x: 1538\n          y: 126\n          width: 58\n          height: 20\n      - component_type: link\n        id: link_support\n        label: Support\n        bounds:\n          x: 1620\n          y: 126\n          width: 54\n          height: 20\n  sidebar:\n  - component_type: navigation_menu\n    id: left_navigation\n    bounds:\n      x: 0\n      y: 169\n      width: 256\n      height: 809\n    subcomponents:\n    - component_type: search_input\n      id: search_sidebar\n      label: Search\n      bounds:\n        x: 16\n        y: 180\n        width: 188\n        height: 36\n    - component_type: button_icon\n      id: btn_search\n      label: Search\n      bounds:\n        x: 204\n        y: 180\n        width: 36\n        height: 36\n    - component_type: text\n      id: text_advanced_search\n      label: 'ADVANCED SEARCH:'\n      bounds:\n        x: 16\n        y: 224\n        width: 100\n        height: 16\n    - component_type: link\n      id: link_policy_search\n      label: POLICY\n      bounds:\n        x: 120\n        y: 224\n        width: 40\n        height: 16\n    - component_type: link\n      id: link_claims_search\n      label: CLAIMS\n      bounds:\n        x: 170\n        y: 224\n        width: 45\n        height: 16\n    - component_type: dropdown\n      id: dropdown_quote\n      label: Quote\n      state: expanded\n      bounds:\n        x: 16\n        y: 252\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy\n      label: Policy\n      state: active\n      bounds:\n        x: 32\n        y: 284\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_dwelling\n      label: Dwelling\n      bounds:\n        x: 32\n        y: 316\n        width: 208\n        height: 32\n      subcomponents:\n      - component_type: badge\n        id: badge_dwelling_count\n        label: '2'\n        bounds:\n          x: 210\n          y: 322\n          width: 16\n          height: 20\n    - component_type: link\n      id: link_review\n      label: Review\n      bounds:\n        x: 32\n        y: 348\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_attachments\n      label: Attachments\n      bounds:\n        x: 16\n        y: 380\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_correspondence\n      label: Correspondence\n      bounds:\n        x: 16\n        y: 412\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_tasks\n      label: Tasks\n      bounds:\n        x: 16\n        y: 444\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_notes\n      label: Notes\n      bounds:\n        x: 16\n        y: 476\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy_file\n      label: Policy File\n      bounds:\n        x: 16\n        y: 508\n        width: 224\n        height: 32\n  - component_type: action_bar\n    id: right_action_bar\n    bounds:\n      x: 1888\n      y: 169\n      width: 32\n      height: 809\n    subcomponents:\n    - component_type: button_icon\n      id: btn_summary\n      label: SUMMARY\n      bounds:\n        x: 1888\n        y: 180\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_quick_qt\n      label: WTCROFT QUICK QT\n      bounds:\n        x: 1888\n        y: 230\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_quote\n      label: NEW QUOTE\n      bounds:\n        x: 1888\n        y: 280\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_note\n      label: NEW NOTE\n      bounds:\n        x: 1888\n        y: 330\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_attach\n      label: NEW ATTACH...\n      bounds:\n        x: 1888\n        y: 380\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_task\n      label: NEW TASK\n      bounds:\n        x: 1888\n        y: 430\n        width: 32\n        height: 50\n  main_content:\n  - component_type: container\n    id: quote_summary_header\n    bounds:\n      x: 256\n      y: 169\n      width: 1632\n      height: 80\n    subcomponents:\n    - component_type: button\n      id: btn_quote\n      label: QUOTE\n      bounds:\n        x: 280\n        y: 180\n        width: 80\n        height: 36\n    - component_type: text\n      id: text_quote_number_label\n      label: Quote Number\n      bounds:\n        x: 376\n        y: 180\n        width: 80\n        height: 16\n    - component_type: text\n      id: text_quote_number_value\n      label: ***********\n      bounds:\n        x: 376\n        y: 198\n        width: 80\n        height: 16\n    - component_type: text\n      id: text_insured_label\n      label: Insured\n      bounds:\n        x: 472\n        y: 180\n        width: 80\n        height: 16\n    - component_type: link\n      id: link_insured_name\n      label: Landon Cassidy\n      bounds:\n        x: 472\n        y: 198\n        width: 90\n        height: 16\n    - component_type: text\n      id: text_product_label\n      label: Product\n      bounds:\n        x: 578\n        y: 180\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_product_value\n      label: Voluntary Homeowners (HO3)\n      bounds:\n        x: 578\n        y: 198\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_sub_type_label\n      label: Sub Type\n      bounds:\n        x: 744\n        y: 180\n        width: 50\n        height: 16\n    - component_type: text\n      id: text_sub_type_value\n      label: HO3\n      bounds:\n        x: 744\n        y: 198\n        width: 30\n        height: 16\n    - component_type: text\n      id: text_policy_term_label\n      label: Policy Term\n      bounds:\n        x: 810\n        y: 180\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_policy_term_value\n      label: 06/20/2025 - 06/20/2026\n      bounds:\n        x: 810\n        y: 198\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_producer_label\n      label: Producer\n      bounds:\n        x: 976\n        y: 180\n        width: 120\n        height: 16\n    - component_type: link\n      id: link_producer_name\n      label: HH Insurance Group, LLC\n      bounds:\n        x: 976\n        y: 198\n        width: 120\n        height: 16\n    - component_type: text\n      id: text_status_label\n      label: Status\n      bounds:\n        x: 1112\n        y: 180\n        width: 60\n        height: 16\n    - component_type: text\n      id: text_status_value\n      label: In Process\n      bounds:\n        x: 1112\n        y: 198\n        width: 60\n        height: 16\n    - component_type: text\n      id: text_premium_fees_label\n      label: Premium + Fees\n      bounds:\n        x: 1188\n        y: 180\n        width: 90\n        height: 16\n    - component_type: text\n      id: text_premium_fees_value\n      label: $17,776.90\n      bounds:\n        x: 1188\n        y: 198\n        width: 70\n        height: 16\n  - component_type: container\n    id: policy_general_form\n    bounds:\n      x: 256\n      y: 249\n      width: 1632\n      height: 729\n    subcomponents:\n    - component_type: link\n      id: link_return_to_home\n      label: < Return to Home\n      bounds:\n        x: 280\n        y: 260\n        width: 100\n        height: 20\n    - component_type: button\n      id: btn_next_page\n      label: NEXT PAGE\n      bounds:\n        x: 1290\n        y: 220\n        width: 90\n        height: 28\n    - component_type: button\n      id: btn_save\n      label: SAVE\n      bounds:\n        x: 1390\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_copy\n      label: COPY\n      bounds:\n        x: 1460\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_print\n      label: PRINT\n      bounds:\n        x: 1530\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_create_application\n      label: CREATE APPLICATION\n      bounds:\n        x: 1600\n        y: 220\n        width: 150\n        height: 28\n    - component_type: button\n      id: btn_discard_changes\n      label: DISCARD CHANGES\n      bounds:\n        x: 1760\n        y: 220\n        width: 130\n        height: 28\n    - component_type: button\n      id: btn_view_notes\n      label: VIEW NOTES\n      bounds:\n        x: 1290\n        y: 250\n        width: 100\n        height: 28\n    - component_type: button\n      id: btn_delete\n      label: DELETE\n      bounds:\n        x: 1400\n        y: 250\n        width: 70\n        height: 28\n    - component_type: button\n      id: btn_more\n      label: '... MORE'\n      bounds:\n        x: 1480\n        y: 250\n        width: 70\n        height: 28\n    - component_type: text\n      id: title_prior_carrier\n      label: Prior Carrier Details\n      bounds:\n        x: 280\n        y: 292\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_prior_carrier\n      label: Prior Carrier*\n      value: New Purchase\n      bounds:\n        x: 280\n        y: 324\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_prior_policy_expiration\n      label: Prior Policy Expiration Date\n      bounds:\n        x: 700\n        y: 324\n        width: 200\n        height: 36\n    - component_type: text\n      id: title_insured_info\n      label: Insured Information\n      bounds:\n        x: 280\n        y: 376\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_entity_type\n      label: Entity Type*\n      value: Individual\n      bounds:\n        x: 280\n        y: 408\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_first_name\n      label: First*\n      value: Landon\n      bounds:\n        x: 500\n        y: 408\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_middle_name\n      label: Middle\n      bounds:\n        x: 670\n        y: 408\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_last_name\n      label: Last*\n      value: Cassidy\n      bounds:\n        x: 840\n        y: 408\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_suffix\n      label: Suffix\n      bounds:\n        x: 1010\n        y: 408\n        width: 100\n        height: 36\n    - component_type: input\n      id: input_dob\n      label: DOB*\n      value: 05/20/1988\n      bounds:\n        x: 280\n        y: 460\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_insurance_score\n      label: Insurance Score*\n      value: Excellent (850-999)\n      bounds:\n        x: 500\n        y: 460\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_search_name\n      label: Search Name*\n      value: Landon Cassidy\n      bounds:\n        x: 280\n        y: 512\n        width: 200\n        height: 36\n    - component_type: link\n      id: link_reset\n      label: Reset\n      state: hovered\n      bounds:\n        x: 490\n        y: 520\n        width: 40\n        height: 20\n    - component_type: dropdown\n      id: dropdown_primary_phone\n      label: Primary Phone\n      value: Select...\n      bounds:\n        x: 280\n        y: 564\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_email\n      label: Email\n      bounds:\n        x: 500\n        y: 564\n        width: 200\n        height: 36\n    - component_type: checkbox\n      id: checkbox_no_email\n      label: No Email\n      bounds:\n        x: 720\n        y: 572\n        width: 80\n        height: 20\n    - component_type: text\n      id: title_dwelling_info\n      label: Dwelling Information\n      bounds:\n        x: 280\n        y: 616\n        width: 150\n        height: 20\n    - component_type: input\n      id: input_lookup_address\n      label: Lookup Address\n      bounds:\n        x: 280\n        y: 648\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_number\n      label: Number*\n      value: '4227'\n      bounds:\n        x: 500\n        y: 648\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_direction\n      label: Direction\n      bounds:\n        x: 600\n        y: 648\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_street\n      label: Street*\n      value: 5th\n      bounds:\n        x: 700\n        y: 648\n        width: 100\n        height: 36\n    - component_type: dropdown\n      id: dropdown_suffix\n      label: Suffix\n      value: Ave\n      bounds:\n        x: 820\n        y: 648\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_post_dir\n      label: Post Dir\n      value: S\n      bounds:\n        x: 920\n        y: 648\n        width: 80\n        height: 36\n    - component_type: dropdown\n      id: dropdown_type\n      label: Type\n      bounds:\n        x: 1020\n        y: 648\n        width: 80\n        height: 36\n    - component_type: checkbox\n      id: checkbox_ignore_address_validation\n      label: Ignore Address Validation\n      bounds:\n        x: 1120\n        y: 632\n        width: 150\n        height: 20\n    - component_type: input\n      id: input_number_2\n      label: Number\n      bounds:\n        x: 1120\n        y: 648\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_city\n      label: City*\n      value: St Petersburg\n      bounds:\n        x: 280\n        y: 700\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_county\n      label: County*\n      value: Pinellas\n      bounds:\n        x: 450\n        y: 700\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_state\n      label: State*\n      value: Florida\n      bounds:\n        x: 620\n        y: 700\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_zip\n      label: Zip*\n      value: 33711-1522\n      bounds:\n        x: 790\n        y: 700\n        width: 100\n        height: 36\n    - component_type: text\n      id: text_address_verified\n      label: Address Verified\n      bounds:\n        x: 910\n        y: 708\n        width: 100\n        height: 20\n    - component_type: link\n      id: link_view_map\n      label: View Map\n      bounds:\n        x: 1020\n        y: 708\n        width: 60\n        height: 20\n    - component_type: input\n      id: input_latitude\n      label: Latitude*\n      value: '27.766685'\n      bounds:\n        x: 280\n        y: 752\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_longitude\n      label: Longitude*\n      value: \"-82.690887\"\n      bounds:\n        x: 450\n        y: 752\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_construction_type\n      label: Construction Type*\n      value: Masonry\n      bounds:\n        x: 280\n        y: 804\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_occupancy\n      label: Occupancy*\n      value: Owner Occupied\n      bounds:\n        x: 450\n        y: 804\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_months_occupied\n      label: Months Occupied*\n      value: 0 to 12 Months\n      bounds:\n        x: 620\n        y: 804\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_resided_less_than_2_years\n      label: Has the Insured resided at the risk address for less than 2 years?*\n      value: 'Yes'\n      bounds:\n        x: 280\n        y: 856\n        width: 150\n        height: 36\n    - component_type: text\n      id: title_prior_address\n      label: Prior Address\n      bounds:\n        x: 280\n        y: 908\n        width: 100\n        height: 20\n    - component_type: input\n      id: input_prior_address_number\n      label: Number*\n      value: '18001'\n      bounds:\n        x: 280\n        y: 940\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_direction\n      label: Direction\n      bounds:\n        x: 380\n        y: 940\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_street\n      label: Street*\n      value: Avalon\n      bounds:\n        x: 480\n        y: 940\n        width: 100\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_address_suffix\n      label: Suffix\n      value: Ln\n      bounds:\n        x: 600\n        y: 940\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_post_dir\n      label: Post Dir\n      bounds:\n        x: 700\n        y: 940\n        width: 80\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_address_type\n      label: Type\n      bounds:\n        x: 800\n        y: 940\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_number_2\n      label: Number\n      bounds:\n        x: 900\n        y: 940\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_city\n      label: City*\n      value: Tampa\n      bounds:\n        x: 280\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_county\n      label: County*\n      value: Hillsborough\n      bounds:\n        x: 450\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_state\n      label: State*\n      value: Florida\n      bounds:\n        x: 620\n        y: 992\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_prior_zip\n      label: Zip*\n      value: 33647-3102\n      bounds:\n        x: 790\n        y: 992\n        width: 100\n        height: 36\n    - component_type: link\n      id: link_verify_address\n      label: Verify Address\n      bounds:\n        x: 910\n        y: 1000\n        width: 80\n        height: 20\n    - component_type: text\n      id: text_prior_address_verified\n      label: Address Verified\n      bounds:\n        x: 1000\n        y: 1000\n        width: 100\n        height: 20\n    - component_type: link\n      id: link_view_map_prior\n      label: View Map\n      bounds:\n        x: 1110\n        y: 1000\n        width: 60\n        height: 20\n    - component_type: button\n      id: btn_next_page_bottom\n      label: Next Page\n      bounds:\n        x: 280\n        y: 1044\n        width: 80\n        height: 36\n  footer:\n  - component_type: container\n    id: main_footer\n    bounds:\n      x: 0\n      y: 1050\n      width: 1920\n      height: 30\n    subcomponents:\n    - component_type: text\n      id: text_powered_by\n      label: Powered by GUIDEWIRE\n      bounds:\n        x: 280\n        y: 1058\n        width: 150\n        height: 20\n    - component_type: text\n      id: text_environment\n      label: 'Environment : PROD AIIG'\n      bounds:\n        x: 1200\n        y: 1058\n        width: 150\n        height: 20\n    - component_type: text\n      id: text_current_logon\n      label: 'Current Logon : AG8529A (Sign Out)'\n      bounds:\n        x: 1400\n        y: 1058\n        width: 200\n        height: 20\n    - component_type: text\n      id: text_posting_date\n      label: 'Posting Date : 05/29/2025'\n      bounds:\n        x: 1650\n        y: 1058\n        width: 150\n        height: 20\n```"]}, {"role": "model", "parts": ["```yaml\nbrowser_component:\n  tab_title: *********** - Guidewire PolicyCenter\n  url: ai.iscs.com/innovation\n  address_bar_focused: false\n  tab_state: idle\nwebpage:\n  header:\n  - component_type: container\n    id: main_header\n    bounds:\n      x: 0\n      y: 69\n      width: 1920\n      height: 61\n    subcomponents:\n    - component_type: image\n      id: logo_american_integrity\n      label: AMERICAN INTEGRITY\n      bounds:\n        x: 16\n        y: 116\n        width: 128\n        height: 40\n    - component_type: navigation_menu\n      id: top_navigation\n      bounds:\n        x: 1290\n        y: 116\n        width: 600\n        height: 36\n      subcomponents:\n      - component_type: link\n        id: link_home\n        label: Home\n        bounds:\n          x: 1298\n          y: 126\n          width: 40\n          height: 20\n      - component_type: link\n        id: link_quote_policy\n        label: Quote/Policy\n        state: active\n        bounds:\n          x: 1362\n          y: 126\n          width: 80\n          height: 20\n      - component_type: link\n        id: link_claims\n        label: Claims\n        bounds:\n          x: 1466\n          y: 126\n          width: 48\n          height: 20\n      - component_type: link\n        id: link_cabinets\n        label: Cabinets\n        bounds:\n          x: 1538\n          y: 126\n          width: 58\n          height: 20\n      - component_type: link\n        id: link_support\n        label: Support\n        bounds:\n          x: 1620\n          y: 126\n          width: 54\n          height: 20\n  sidebar:\n  - component_type: navigation_menu\n    id: left_navigation\n    bounds:\n      x: 0\n      y: 169\n      width: 256\n      height: 809\n    subcomponents:\n    - component_type: search_input\n      id: search_sidebar\n      label: Search\n      bounds:\n        x: 16\n        y: 180\n        width: 188\n        height: 36\n    - component_type: button_icon\n      id: btn_search\n      label: Search\n      bounds:\n        x: 204\n        y: 180\n        width: 36\n        height: 36\n    - component_type: text\n      id: text_advanced_search\n      label: 'ADVANCED SEARCH:'\n      bounds:\n        x: 16\n        y: 224\n        width: 100\n        height: 16\n    - component_type: link\n      id: link_policy_search\n      label: POLICY\n      bounds:\n        x: 120\n        y: 224\n        width: 40\n        height: 16\n    - component_type: link\n      id: link_claims_search\n      label: CLAIMS\n      bounds:\n        x: 170\n        y: 224\n        width: 45\n        height: 16\n    - component_type: dropdown\n      id: dropdown_quote\n      label: Quote\n      state: expanded\n      bounds:\n        x: 16\n        y: 252\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy\n      label: Policy\n      state: active\n      bounds:\n        x: 32\n        y: 284\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_dwelling\n      label: Dwelling\n      bounds:\n        x: 32\n        y: 316\n        width: 208\n        height: 32\n      subcomponents:\n      - component_type: badge\n        id: badge_dwelling_count\n        label: '2'\n        bounds:\n          x: 210\n          y: 322\n          width: 16\n          height: 20\n    - component_type: link\n      id: link_review\n      label: Review\n      bounds:\n        x: 32\n        y: 348\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_attachments\n      label: Attachments\n      bounds:\n        x: 16\n        y: 380\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_correspondence\n      label: Correspondence\n      bounds:\n        x: 16\n        y: 412\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_tasks\n      label: Tasks\n      bounds:\n        x: 16\n        y: 444\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_notes\n      label: Notes\n      bounds:\n        x: 16\n        y: 476\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy_file\n      label: Policy File\n      bounds:\n        x: 16\n        y: 508\n        width: 224\n        height: 32\n  - component_type: action_bar\n    id: right_action_bar\n    bounds:\n      x: 1888\n      y: 169\n      width: 32\n      height: 809\n    subcomponents:\n    - component_type: button_icon\n      id: btn_summary\n      label: SUMMARY\n      bounds:\n        x: 1888\n        y: 180\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_quick_qt\n      label: WTCROFT QUICK QT\n      bounds:\n        x: 1888\n        y: 230\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_quote\n      label: NEW QUOTE\n      bounds:\n        x: 1888\n        y: 280\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_note\n      label: NEW NOTE\n      bounds:\n        x: 1888\n        y: 330\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_attach\n      label: NEW ATTACH...\n      bounds:\n        x: 1888\n        y: 380\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_task\n      label: NEW TASK\n      bounds:\n        x: 1888\n        y: 430\n        width: 32\n        height: 50\nmain_content:\n- component_type: container\n  id: quote_summary_header\n  bounds:\n    x: 256\n    y: 169\n    width: 1632\n    height: 80\n  subcomponents:\n  - component_type: button\n    id: btn_quote\n    label: QUOTE\n    bounds:\n      x: 280\n      y: 180\n      width: 80\n      height: 36\n  - component_type: text\n    id: text_quote_number_label\n    label: Quote Number\n    bounds:\n      x: 376\n      y: 180\n      width: 80\n      height: 16\n  - component_type: text\n    id: text_quote_number_value\n    label: ***********\n    bounds:\n      x: 376\n      y: 198\n      width: 80\n      height: 16\n  - component_type: text\n    id: text_insured_label\n    label: Insured\n    bounds:\n      x: 472\n      y: 180\n      width: 80\n      height: 16\n  - component_type: link\n    id: link_insured_name\n    label: Landon Cassidy\n    bounds:\n      x: 472\n      y: 198\n      width: 90\n      height: 16\n  - component_type: text\n    id: text_product_label\n    label: Product\n    bounds:\n      x: 578\n      y: 180\n      width: 150\n      height: 16\n  - component_type: text\n    id: text_product_value\n    label: Voluntary Homeowners (HO3)\n    bounds:\n      x: 578\n      y: 198\n      width: 150\n      height: 16\n  - component_type: text\n    id: text_sub_type_label\n    label: Sub Type\n    bounds:\n      x: 744\n      y: 180\n      width: 50\n      height: 16\n  - component_type: text\n    id: text_sub_type_value\n    label: HO3\n    bounds:\n      x: 744\n      y: 198\n      width: 30\n      height: 16\n  - component_type: text\n    id: text_policy_term_label\n    label: Policy Term\n    bounds:\n      x: 810\n      y: 180\n      width: 150\n      height: 16\n  - component_type: text\n    id: text_policy_term_value\n    label: 06/20/2025 - 06/20/2026\n    bounds:\n      x: 810\n      y: 198\n      width: 150\n      height: 16\n  - component_type: text\n    id: text_producer_label\n    label: Producer\n    bounds:\n      x: 976\n      y: 180\n      width: 120\n      height: 16\n  - component_type: link\n    id: link_producer_name\n    label: HH Insurance Group, LLC\n    bounds:\n      x: 976\n      y: 198\n      width: 120\n      height: 16\n  - component_type: text\n    id: text_status_label\n    label: Status\n    bounds:\n      x: 1112\n      y: 180\n      width: 60\n      height: 16\n  - component_type: text\n    id: text_status_value\n    label: In Process\n    bounds:\n      x: 1112\n      y: 198\n      width: 60\n      height: 16\n  - component_type: text\n    id: text_premium_fees_label\n    label: Premium + Fees\n    bounds:\n      x: 1188\n      y: 180\n      width: 90\n      height: 16\n  - component_type: text\n    id: text_premium_fees_value\n    label: $17,776.90\n    bounds:\n      x: 1188\n      y: 198\n      width: 70\n      height: 16\n- component_type: container\n  id: policy_general_form\n  bounds:\n    x: 256\n    y: 249\n    width: 1632\n    height: 729\n  subcomponents:\n  - component_type: link\n    id: link_return_to_home\n    label: < Return to Home\n    bounds:\n      x: 280\n      y: 260\n      width: 100\n      height: 20\n  - component_type: button\n    id: btn_next_page\n    label: NEXT PAGE\n    bounds:\n      x: 1290\n      y: 220\n      width: 90\n      height: 28\n  - component_type: button\n    id: btn_save\n    label: SAVE\n    bounds:\n      x: 1390\n      y: 220\n      width: 60\n      height: 28\n  - component_type: button\n    id: btn_copy\n    label: COPY\n    bounds:\n      x: 1460\n      y: 220\n      width: 60\n      height: 28\n  - component_type: button\n    id: btn_print\n    label: PRINT\n    bounds:\n      x: 1530\n      y: 220\n      width: 60\n      height: 28\n  - component_type: button\n    id: btn_create_application\n    label: CREATE APPLICATION\n    bounds:\n      x: 1600\n      y: 220\n      width: 150\n      height: 28\n  - component_type: button\n    id: btn_discard_changes\n    label: DISCARD CHANGES\n    bounds:\n      x: 1760\n      y: 220\n      width: 130\n      height: 28\n  - component_type: button\n    id: btn_view_notes\n    label: VIEW NOTES\n    bounds:\n      x: 1290\n      y: 250\n      width: 100\n      height: 28\n  - component_type: button\n    id: btn_delete\n    label: DELETE\n    bounds:\n      x: 1400\n      y: 250\n      width: 70\n      height: 28\n  - component_type: button\n    id: btn_more\n    label: '... MORE'\n    bounds:\n      x: 1480\n      y: 250\n      width: 70\n      height: 28\n  - component_type: text\n    id: title_prior_carrier\n    label: Prior Carrier Details\n    bounds:\n      x: 280\n      y: 292\n      width: 150\n      height: 20\n  - component_type: dropdown\n    id: dropdown_prior_carrier\n    label: Prior Carrier*\n    value: New Purchase\n    bounds:\n      x: 280\n      y: 324\n      width: 200\n      height: 36\n  - component_type: input\n    id: input_prior_policy_expiration\n    label: Prior Policy Expiration Date\n    bounds:\n      x: 700\n      y: 324\n      width: 200\n      height: 36\n  - component_type: text\n    id: title_insured_info\n    label: Insured Information\n    bounds:\n      x: 280\n      y: 376\n      width: 150\n      height: 20\n  - component_type: dropdown\n    id: dropdown_entity_type\n    label: Entity Type*\n    value: Individual\n    bounds:\n      x: 280\n      y: 408\n      width: 200\n      height: 36\n  - component_type: input\n    id: input_first_name\n    label: First*\n    value: Landon\n    bounds:\n      x: 500\n      y: 408\n      width: 150\n      height: 36\n  - component_type: input\n    id: input_middle_name\n    label: Middle\n    bounds:\n      x: 670\n      y: 408\n      width: 150\n      height: 36\n  - component_type: input\n    id: input_last_name\n    label: Last*\n    value: Cassidy\n    bounds:\n      x: 840\n      y: 408\n      width: 150\n      height: 36\n  - component_type: input\n    id: input_suffix\n    label: Suffix\n    bounds:\n      x: 1010\n      y: 408\n      width: 100\n      height: 36\n  - component_type: input\n    id: input_dob\n    label: DOB*\n    value: 05/20/1988\n    bounds:\n      x: 280\n      y: 460\n      width: 150\n      height: 36\n  - component_type: dropdown\n    id: dropdown_insurance_score\n    label: Insurance Score*\n    value: Excellent (850-999)\n    bounds:\n      x: 500\n      y: 460\n      width: 200\n      height: 36\n  - component_type: input\n    id: input_search_name\n    label: Search Name*\n    value: Landon Cassidy\n    bounds:\n      x: 280\n      y: 512\n      width: 200\n      height: 36\n  - component_type: link\n    id: link_reset\n    label: Reset\n    state: hovered\n    bounds:\n      x: 490\n      y: 520\n      width: 40\n      height: 20\n  - component_type: dropdown\n    id: dropdown_primary_phone\n    label: Primary Phone\n    value: Select...\n    bounds:\n      x: 280\n      y: 564\n      width: 200\n      height: 36\n  - component_type: input\n    id: input_email\n    label: Email\n    bounds:\n      x: 500\n      y: 564\n      width: 200\n      height: 36\n  - component_type: checkbox\n    id: checkbox_no_email\n    label: No Email\n    bounds:\n      x: 720\n      y: 572\n      width: 80\n      height: 20\n  - component_type: text\n    id: title_dwelling_info\n    label: Dwelling Information\n    bounds:\n      x: 280\n      y: 616\n      width: 150\n      height: 20\n  - component_type: input\n    id: input_lookup_address\n    label: Lookup Address\n    bounds:\n      x: 280\n      y: 648\n      width: 200\n      height: 36\n  - component_type: input\n    id: input_number\n    label: Number*\n    value: '4227'\n    bounds:\n      x: 500\n      y: 648\n      width: 80\n      height: 36\n  - component_type: input\n    id: input_direction\n    label: Direction\n    bounds:\n      x: 600\n      y: 648\n      width: 80\n      height: 36\n  - component_type: input\n    id: input_street\n    label: Street*\n    value: 5th\n    bounds:\n      x: 700\n      y: 648\n      width: 100\n      height: 36\n  - component_type: dropdown\n    id: dropdown_suffix\n    label: Suffix\n    value: Ave\n    bounds:\n      x: 820\n      y: 648\n      width: 80\n      height: 36\n  - component_type: input\n    id: input_post_dir\n    label: Post Dir\n    value: S\n    bounds:\n      x: 920\n      y: 648\n      width: 80\n      height: 36\n  - component_type: dropdown\n    id: dropdown_type\n    label: Type\n    bounds:\n      x: 1020\n      y: 648\n      width: 80\n      height: 36\n  - component_type: checkbox\n    id: checkbox_ignore_address_validation\n    label: Ignore Address Validation\n    bounds:\n      x: 1120\n      y: 632\n      width: 150\n      height: 20\n  - component_type: input\n    id: input_number_2\n    label: Number\n    bounds:\n      x: 1120\n      y: 648\n      width: 80\n      height: 36\n  - component_type: input\n    id: input_city\n    label: City*\n    value: St Petersburg\n    bounds:\n      x: 280\n      y: 700\n      width: 150\n      height: 36\n  - component_type: dropdown\n    id: dropdown_county\n    label: County*\n    value: Pinellas\n    bounds:\n      x: 450\n      y: 700\n      width: 150\n      height: 36\n  - component_type: dropdown\n    id: dropdown_state\n    label: State*\n    value: Florida\n    bounds:\n      x: 620\n      y: 700\n      width: 150\n      height: 36\n  - component_type: input\n    id: input_zip\n    label: Zip*\n    value: 33711-1522\n    bounds:\n      x: 790\n      y: 700\n      width: 100\n      height: 36\n  - component_type: text\n    id: text_address_verified\n    label: Address Verified\n    bounds:\n      x: 910\n      y: 708\n      width: 100\n      height: 20\n  - component_type: link\n    id: link_view_map\n    label: View Map\n    bounds:\n      x: 1020\n      y: 708\n      width: 60\n      height: 20\n  - component_type: input\n    id: input_latitude\n    label: Latitude*\n    value: '27.766685'\n    bounds:\n      x: 280\n      y: 752\n      width: 150\n      height: 36\n  - component_type: input\n    id: input_longitude\n    label: Longitude*\n    value: \"-82.690887\"\n    bounds:\n      x: 450\n      y: 752\n      width: 150\n      height: 36\n  - component_type: dropdown\n    id: dropdown_construction_type\n    label: Construction Type*\n    value: Masonry\n    bounds:\n      x: 280\n      y: 804\n      width: 150\n      height: 36\n  - component_type: dropdown\n    id: dropdown_occupancy\n    label: Occupancy*\n    value: Owner Occupied\n    bounds:\n      x: 450\n      y: 804\n      width: 150\n      height: 36\n  - component_type: dropdown\n    id: dropdown_months_occupied\n    label: Months Occupied*\n    value: 0 to 12 Months\n    bounds:\n      x: 620\n      y: 804\n      width: 150\n      height: 36\n  - component_type: dropdown\n    id: dropdown_resided_less_than_2_years\n    label: Has the Insured resided at the risk address for less than 2 years?*\n    value: 'Yes'\n    bounds:\n      x: 280\n      y: 856\n      width: 150\n      height: 36\n  - component_type: text\n    id: title_prior_address\n    label: Prior Address\n    bounds:\n      x: 280\n      y: 908\n      width: 100\n      height: 20\n  - component_type: input\n    id: input_prior_address_number\n    label: Number*\n    value: '18001'\n    bounds:\n      x: 280\n      y: 940\n      width: 80\n      height: 36\n  - component_type: input\n    id: input_prior_address_direction\n    label: Direction\n    bounds:\n      x: 380\n      y: 940\n      width: 80\n      height: 36\n  - component_type: input\n    id: input_prior_address_street\n    label: Street*\n    value: Avalon\n    bounds:\n      x: 480\n      y: 940\n      width: 100\n      height: 36\n  - component_type: dropdown\n    id: dropdown_prior_address_suffix\n    label: Suffix\n    value: Ln\n    bounds:\n      x: 600\n      y: 940\n      width: 80\n      height: 36\n  - component_type: input\n    id: input_prior_address_post_dir\n    label: Post Dir\n    bounds:\n      x: 700\n      y: 940\n      width: 80\n      height: 36\n  - component_type: dropdown\n    id: dropdown_prior_address_type\n    label: Type\n    bounds:\n      x: 800\n      y: 940\n      width: 80\n      height: 36\n  - component_type: input\n    id: input_prior_address_number_2\n    label: Number\n    bounds:\n      x: 900\n      y: 940\n      width: 80\n      height: 36\n  - component_type: input\n    id: input_prior_city\n    label: City*\n    value: Tampa\n    bounds:\n      x: 280\n      y: 992\n      width: 150\n      height: 36\n  - component_type: dropdown\n    id: dropdown_prior_county\n    label: County*\n    value: Hillsborough\n    bounds:\n      x: 450\n      y: 992\n      width: 150\n      height: 36\n  - component_type: dropdown\n    id: dropdown_prior_state\n    label: State*\n    value: Florida\n    bounds:\n      x: 620\n      y: 992\n      width: 150\n      height: 36\n  - component_type: input\n    id: input_prior_zip\n    label: Zip*\n    value: 33647-3102\n    bounds:\n      x: 790\n      y: 992\n      width: 100\n      height: 36\n  - component_type: link\n    id: link_verify_address\n    label: Verify Address\n    bounds:\n      x: 910\n      y: 1000\n      width: 80\n      height: 20\n  - component_type: text\n    id: text_prior_address_verified\n    label: Address Verified\n    bounds:\n      x: 1000\n      y: 1000\n      width: 100\n      height: 20\n  - component_type: link\n    id: link_view_map_prior\n    label: View Map\n    bounds:\n      x: 1110\n      y: 1000\n      width: 60\n      height: 20\n  - component_type: button\n    id: btn_next_page_bottom\n    label: Next Page\n    bounds:\n      x: 280\n      y: 1044\n      width: 80\n      height: 36\nfooter:\n- component_type: container\n  id: main_footer\n  bounds:\n    x: 0\n    y: 1050\n    width: 1920\n    height: 30\n  subcomponents:\n  - component_type: text\n    id: text_powered_by\n    label: Powered by GUIDEWIRE\n    bounds:\n      x: 280\n      y: 1058\n      width: 150\n      height: 20\n  - component_type: text\n    id: text_environment\n    label: 'Environment : PROD AIIG'\n    bounds:\n      x: 1200\n      y: 1058\n      width: 150\n      height: 20\n  - component_type: text\n    id: text_current_logon\n    label: 'Current Logon : AG8529A (Sign Out)'\n    bounds:\n      x: 1400\n      y: 1058\n      width: 200\n      height: 20\n  - component_type: text\n    id: text_posting_date\n    label: 'Posting Date : 05/29/2025'\n    bounds:\n      x: 1650\n      y: 1058\n      width: 150\n      height: 20\n```"]}, {"role": "model", "parts": ["```yaml\nbrowser_component:\n  tab_title: *********** - Guidewire PolicyCenter\n  url: ai.iscs.com/innovation\n  address_bar_focused: false\n  tab_state: idle\nwebpage:\n  header:\n  - component_type: container\n    id: main_header\n    bounds:\n      x: 0\n      y: 69\n      width: 1920\n      height: 61\n    subcomponents:\n    - component_type: image\n      id: logo_american_integrity\n      label: AMERICAN INTEGRITY\n      bounds:\n        x: 16\n        y: 116\n        width: 128\n        height: 40\n    - component_type: navigation_menu\n      id: top_navigation\n      bounds:\n        x: 1290\n        y: 116\n        width: 600\n        height: 36\n      subcomponents:\n      - component_type: link\n        id: link_home\n        label: Home\n        bounds:\n          x: 1298\n          y: 126\n          width: 40\n          height: 20\n      - component_type: link\n        id: link_quote_policy\n        label: Quote/Policy\n        state: active\n        bounds:\n          x: 1362\n          y: 126\n          width: 80\n          height: 20\n      - component_type: link\n        id: link_claims\n        label: Claims\n        bounds:\n          x: 1466\n          y: 126\n          width: 48\n          height: 20\n      - component_type: link\n        id: link_cabinets\n        label: Cabinets\n        bounds:\n          x: 1538\n          y: 126\n          width: 58\n          height: 20\n      - component_type: link\n        id: link_support\n        label: Support\n        bounds:\n          x: 1620\n          y: 126\n          width: 54\n          height: 20\n  sidebar:\n  - component_type: navigation_menu\n    id: left_navigation\n    bounds:\n      x: 0\n      y: 169\n      width: 256\n      height: 809\n    subcomponents:\n    - component_type: search_input\n      id: search_sidebar\n      label: Search\n      bounds:\n        x: 16\n        y: 180\n        width: 188\n        height: 36\n    - component_type: button_icon\n      id: btn_search\n      label: Search\n      bounds:\n        x: 204\n        y: 180\n        width: 36\n        height: 36\n    - component_type: text\n      id: text_advanced_search\n      label: 'ADVANCED SEARCH:'\n      bounds:\n        x: 16\n        y: 224\n        width: 100\n        height: 16\n    - component_type: link\n      id: link_policy_search\n      label: POLICY\n      bounds:\n        x: 120\n        y: 224\n        width: 40\n        height: 16\n    - component_type: link\n      id: link_claims_search\n      label: CLAIMS\n      bounds:\n        x: 170\n        y: 224\n        width: 45\n        height: 16\n    - component_type: dropdown\n      id: dropdown_quote\n      label: Quote\n      state: expanded\n      bounds:\n        x: 16\n        y: 252\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy\n      label: Policy\n      state: active\n      bounds:\n        x: 32\n        y: 284\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_dwelling\n      label: Dwelling\n      bounds:\n        x: 32\n        y: 316\n        width: 208\n        height: 32\n      subcomponents:\n      - component_type: badge\n        id: badge_dwelling_count\n        label: '2'\n        bounds:\n          x: 210\n          y: 322\n          width: 16\n          height: 20\n    - component_type: link\n      id: link_review\n      label: Review\n      bounds:\n        x: 32\n        y: 348\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_attachments\n      label: Attachments\n      bounds:\n        x: 16\n        y: 380\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_correspondence\n      label: Correspondence\n      bounds:\n        x: 16\n        y: 412\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_tasks\n      label: Tasks\n      bounds:\n        x: 16\n        y: 444\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_notes\n      label: Notes\n      bounds:\n        x: 16\n        y: 476\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy_file\n      label: Policy File\n      bounds:\n        x: 16\n        y: 508\n        width: 224\n        height: 32\n  - component_type: action_bar\n    id: right_action_bar\n    bounds:\n      x: 1888\n      y: 169\n      width: 32\n      height: 809\n    subcomponents:\n    - component_type: button_icon\n      id: btn_summary\n      label: SUMMARY\n      bounds:\n        x: 1888\n        y: 180\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_quick_qt\n      label: WTCROFT QUICK QT\n      bounds:\n        x: 1888\n        y: 230\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_quote\n      label: NEW QUOTE\n      bounds:\n        x: 1888\n        y: 280\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_note\n      label: NEW NOTE\n      bounds:\n        x: 1888\n        y: 330\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_attach\n      label: NEW ATTACH...\n      bounds:\n        x: 1888\n        y: 380\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_task\n      label: NEW TASK\n      bounds:\n        x: 1888\n        y: 430\n        width: 32\n        height: 50\n  main_content:\n  - component_type: container\n    id: quote_summary_header\n    bounds:\n      x: 256\n      y: 169\n      width: 1632\n      height: 80\n    subcomponents:\n    - component_type: button\n      id: btn_quote\n      label: QUOTE\n      bounds:\n        x: 280\n        y: 180\n        width: 80\n        height: 36\n    - component_type: text\n      id: text_quote_number_label\n      label: Quote Number\n      bounds:\n        x: 376\n        y: 180\n        width: 80\n        height: 16\n    - component_type: text\n      id: text_quote_number_value\n      label: ***********\n      bounds:\n        x: 376\n        y: 198\n        width: 80\n        height: 16\n    - component_type: text\n      id: text_insured_label\n      label: Insured\n      bounds:\n        x: 472\n        y: 180\n        width: 80\n        height: 16\n    - component_type: link\n      id: link_insured_name\n      label: Landon Cassidy\n      bounds:\n        x: 472\n        y: 198\n        width: 90\n        height: 16\n    - component_type: text\n      id: text_product_label\n      label: Product\n      bounds:\n        x: 578\n        y: 180\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_product_value\n      label: Voluntary Homeowners (HO3)\n      bounds:\n        x: 578\n        y: 198\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_sub_type_label\n      label: Sub Type\n      bounds:\n        x: 744\n        y: 180\n        width: 50\n        height: 16\n    - component_type: text\n      id: text_sub_type_value\n      label: HO3\n      bounds:\n        x: 744\n        y: 198\n        width: 30\n        height: 16\n    - component_type: text\n      id: text_policy_term_label\n      label: Policy Term\n      bounds:\n        x: 810\n        y: 180\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_policy_term_value\n      label: 06/20/2025 - 06/20/2026\n      bounds:\n        x: 810\n        y: 198\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_producer_label\n      label: Producer\n      bounds:\n        x: 976\n        y: 180\n        width: 120\n        height: 16\n    - component_type: link\n      id: link_producer_name\n      label: HH Insurance Group, LLC\n      bounds:\n        x: 976\n        y: 198\n        width: 120\n        height: 16\n    - component_type: text\n      id: text_status_label\n      label: Status\n      bounds:\n        x: 1112\n        y: 180\n        width: 60\n        height: 16\n    - component_type: text\n      id: text_status_value\n      label: In Process\n      bounds:\n        x: 1112\n        y: 198\n        width: 60\n        height: 16\n    - component_type: text\n      id: text_premium_fees_label\n      label: Premium + Fees\n      bounds:\n        x: 1188\n        y: 180\n        width: 90\n        height: 16\n    - component_type: text\n      id: text_premium_fees_value\n      label: $17,776.90\n      bounds:\n        x: 1188\n        y: 198\n        width: 70\n        height: 16\n  - component_type: container\n    id: policy_general_form\n    bounds:\n      x: 256\n      y: 249\n      width: 1632\n      height: 729\n    subcomponents:\n    - component_type: link\n      id: link_return_to_home\n      label: < Return to Home\n      bounds:\n        x: 280\n        y: 260\n        width: 100\n        height: 20\n    - component_type: button\n      id: btn_next_page\n      label: NEXT PAGE\n      bounds:\n        x: 1290\n        y: 220\n        width: 90\n        height: 28\n    - component_type: button\n      id: btn_save\n      label: SAVE\n      bounds:\n        x: 1390\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_copy\n      label: COPY\n      bounds:\n        x: 1460\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_print\n      label: PRINT\n      bounds:\n        x: 1530\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_create_application\n      label: CREATE APPLICATION\n      bounds:\n        x: 1600\n        y: 220\n        width: 150\n        height: 28\n    - component_type: button\n      id: btn_discard_changes\n      label: DISCARD CHANGES\n      bounds:\n        x: 1760\n        y: 220\n        width: 130\n        height: 28\n    - component_type: button\n      id: btn_view_notes\n      label: VIEW NOTES\n      bounds:\n        x: 1290\n        y: 250\n        width: 100\n        height: 28\n    - component_type: button\n      id: btn_delete\n      label: DELETE\n      bounds:\n        x: 1400\n        y: 250\n        width: 70\n        height: 28\n    - component_type: button\n      id: btn_more\n      label: '... MORE'\n      bounds:\n        x: 1480\n        y: 250\n        width: 70\n        height: 28\n    - component_type: text\n      id: title_prior_carrier\n      label: Prior Carrier Details\n      bounds:\n        x: 280\n        y: 292\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_prior_carrier\n      label: Prior Carrier*\n      value: New Purchase\n      bounds:\n        x: 280\n        y: 324\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_prior_policy_expiration\n      label: Prior Policy Expiration Date\n      bounds:\n        x: 700\n        y: 324\n        width: 200\n        height: 36\n    - component_type: text\n      id: title_insured_info\n      label: Insured Information\n      bounds:\n        x: 280\n        y: 376\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_entity_type\n      label: Entity Type*\n      value: Individual\n      bounds:\n        x: 280\n        y: 408\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_first_name\n      label: First*\n      value: Landon\n      bounds:\n        x: 500\n        y: 408\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_middle_name\n      label: Middle\n      bounds:\n        x: 670\n        y: 408\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_last_name\n      label: Last*\n      value: Cassidy\n      bounds:\n        x: 840\n        y: 408\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_suffix\n      label: Suffix\n      bounds:\n        x: 1010\n        y: 408\n        width: 100\n        height: 36\n    - component_type: input\n      id: input_dob\n      label: DOB*\n      value: 05/20/1988\n      bounds:\n        x: 280\n        y: 460\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_insurance_score\n      label: Insurance Score*\n      value: Excellent (850-999)\n      bounds:\n        x: 500\n        y: 460\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_search_name\n      label: Search Name*\n      value: Landon Cassidy\n      bounds:\n        x: 280\n        y: 512\n        width: 200\n        height: 36\n    - component_type: link\n      id: link_reset\n      label: Reset\n      state: hovered\n      bounds:\n        x: 490\n        y: 520\n        width: 40\n        height: 20\n    - component_type: dropdown\n      id: dropdown_primary_phone\n      label: Primary Phone\n      value: Select...\n      bounds:\n        x: 280\n        y: 564\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_email\n      label: Email\n      bounds:\n        x: 500\n        y: 564\n        width: 200\n        height: 36\n    - component_type: checkbox\n      id: checkbox_no_email\n      label: No Email\n      bounds:\n        x: 720\n        y: 572\n        width: 80\n        height: 20\n    - component_type: text\n      id: title_dwelling_info\n      label: Dwelling Information\n      bounds:\n        x: 280\n        y: 616\n        width: 150\n        height: 20\n    - component_type: input\n      id: input_lookup_address\n      label: Lookup Address\n      bounds:\n        x: 280\n        y: 648\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_number\n      label: Number*\n      value: '4227'\n      bounds:\n        x: 500\n        y: 648\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_direction\n      label: Direction\n      bounds:\n        x: 600\n        y: 648\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_street\n      label: Street*\n      value: 5th\n      bounds:\n        x: 700\n        y: 648\n        width: 100\n        height: 36\n    - component_type: dropdown\n      id: dropdown_suffix\n      label: Suffix\n      value: Ave\n      bounds:\n        x: 820\n        y: 648\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_post_dir\n      label: Post Dir\n      value: S\n      bounds:\n        x: 920\n        y: 648\n        width: 80\n        height: 36\n    - component_type: dropdown\n      id: dropdown_type\n      label: Type\n      bounds:\n        x: 1020\n        y: 648\n        width: 80\n        height: 36\n    - component_type: checkbox\n      id: checkbox_ignore_address_validation\n      label: Ignore Address Validation\n      bounds:\n        x: 1120\n        y: 632\n        width: 150\n        height: 20\n    - component_type: input\n      id: input_number_2\n      label: Number\n      bounds:\n        x: 1120\n        y: 648\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_city\n      label: City*\n      value: St Petersburg\n      bounds:\n        x: 280\n        y: 700\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_county\n      label: County*\n      value: Pinellas\n      bounds:\n        x: 450\n        y: 700\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_state\n      label: State*\n      value: Florida\n      bounds:\n        x: 620\n        y: 700\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_zip\n      label: Zip*\n      value: 33711-1522\n      bounds:\n        x: 790\n        y: 700\n        width: 100\n        height: 36\n    - component_type: text\n      id: text_address_verified\n      label: Address Verified\n      bounds:\n        x: 910\n        y: 708\n        width: 100\n        height: 20\n    - component_type: link\n      id: link_view_map\n      label: View Map\n      bounds:\n        x: 1020\n        y: 708\n        width: 60\n        height: 20\n    - component_type: input\n      id: input_latitude\n      label: Latitude*\n      value: '27.766685'\n      bounds:\n        x: 280\n        y: 752\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_longitude\n      label: Longitude*\n      value: \"-82.690887\"\n      bounds:\n        x: 450\n        y: 752\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_construction_type\n      label: Construction Type*\n      value: Masonry\n      bounds:\n        x: 280\n        y: 804\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_occupancy\n      label: Occupancy*\n      value: Owner Occupied\n      bounds:\n        x: 450\n        y: 804\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_months_occupied\n      label: Months Occupied*\n      value: 0 to 12 Months\n      bounds:\n        x: 620\n        y: 804\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_resided_less_than_2_years\n      label: Has the Insured resided at the risk address for less than 2 years?*\n      value: 'Yes'\n      bounds:\n        x: 280\n        y: 856\n        width: 150\n        height: 36\n    - component_type: text\n      id: title_prior_address\n      label: Prior Address\n      bounds:\n        x: 280\n        y: 908\n        width: 100\n        height: 20\n    - component_type: input\n      id: input_prior_address_number\n      label: Number*\n      value: '18001'\n      bounds:\n        x: 280\n        y: 940\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_direction\n      label: Direction\n      bounds:\n        x: 380\n        y: 940\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_street\n      label: Street*\n      value: Avalon\n      bounds:\n        x: 480\n        y: 940\n        width: 100\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_address_suffix\n      label: Suffix\n      value: Ln\n      bounds:\n        x: 600\n        y: 940\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_post_dir\n      label: Post Dir\n      bounds:\n        x: 700\n        y: 940\n        width: 80\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_address_type\n      label: Type\n      bounds:\n        x: 800\n        y: 940\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_number_2\n      label: Number\n      bounds:\n        x: 900\n        y: 940\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_city\n      label: City*\n      value: Tampa\n      bounds:\n        x: 280\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_county\n      label: County*\n      value: Hillsborough\n      bounds:\n        x: 450\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_state\n      label: State*\n      value: Florida\n      bounds:\n        x: 620\n        y: 992\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_prior_zip\n      label: Zip*\n      value: 33647-3102\n      bounds:\n        x: 790\n        y: 992\n        width: 100\n        height: 36\n    - component_type: link\n      id: link_verify_address\n      label: Verify Address\n      bounds:\n        x: 910\n        y: 1000\n        width: 80\n        height: 20\n    - component_type: text\n      id: text_prior_address_verified\n      label: Address Verified\n      bounds:\n        x: 1000\n        y: 1000\n        width: 100\n        height: 20\n    - component_type: link\n      id: link_view_map_prior\n      label: View Map\n      bounds:\n        x: 1110\n        y: 1000\n        width: 60\n        height: 20\n    - component_type: button\n      id: btn_next_page_bottom\n      label: Next Page\n      bounds:\n        x: 280\n        y: 1044\n        width: 80\n        height: 36\n  footer:\n  - component_type: container\n    id: main_footer\n    bounds:\n      x: 0\n      y: 1050\n      width: 1920\n      height: 30\n    subcomponents:\n    - component_type: text\n      id: text_powered_by\n      label: Powered by GUIDEWIRE\n      bounds:\n        x: 280\n        y: 1058\n        width: 150\n        height: 20\n    - component_type: text\n      id: text_environment\n      label: 'Environment : PROD AIIG'\n      bounds:\n        x: 1200\n        y: 1058\n        width: 150\n        height: 20\n    - component_type: text\n      id: text_current_logon\n      label: 'Current Logon : AG8529A (Sign Out)'\n      bounds:\n        x: 1400\n        y: 1058\n        width: 200\n        height: 20\n    - component_type: text\n      id: text_posting_date\n      label: 'Posting Date : 05/29/2025'\n      bounds:\n        x: 1650\n        y: 1058\n        width: 150\n        height: 20\n```"]}, {"role": "model", "parts": ["```yaml\nbrowser_component:\n  tab_title: *********** - Guidewire PolicyCenter\n  url: ai.iscs.com/innovation\n  address_bar_focused: false\n  tab_state: idle\nwebpage:\n  header:\n  - component_type: container\n    id: main_header\n    bounds:\n      x: 0\n      y: 69\n      width: 1920\n      height: 61\n    subcomponents:\n    - component_type: image\n      id: logo_american_integrity\n      label: AMERICAN INTEGRITY\n      bounds:\n        x: 16\n        y: 116\n        width: 128\n        height: 40\n    - component_type: navigation_menu\n      id: top_navigation\n      bounds:\n        x: 1008\n        y: 116\n        width: 882\n        height: 36\n      subcomponents:\n      - component_type: link\n        id: link_home\n        label: Home\n        bounds:\n          x: 1298\n          y: 126\n          width: 40\n          height: 20\n      - component_type: link\n        id: link_quote_policy\n        label: Quote/Policy\n        state: active\n        bounds:\n          x: 1362\n          y: 126\n          width: 80\n          height: 20\n      - component_type: link\n        id: link_claims\n        label: Claims\n        bounds:\n          x: 1466\n          y: 126\n          width: 48\n          height: 20\n      - component_type: link\n        id: link_cabinets\n        label: Cabinets\n        bounds:\n          x: 1538\n          y: 126\n          width: 58\n          height: 20\n      - component_type: link\n        id: link_support\n        label: Support\n        bounds:\n          x: 1620\n          y: 126\n          width: 54\n          height: 20\n  sidebar:\n  - component_type: navigation_menu\n    id: left_navigation\n    bounds:\n      x: 0\n      y: 169\n      width: 256\n      height: 809\n    subcomponents:\n    - component_type: search_input\n      id: search_sidebar\n      label: Search\n      bounds:\n        x: 16\n        y: 180\n        width: 188\n        height: 36\n    - component_type: button_icon\n      id: btn_search\n      label: Search\n      bounds:\n        x: 204\n        y: 180\n        width: 36\n        height: 36\n    - component_type: text\n      id: text_advanced_search\n      label: 'ADVANCED SEARCH:'\n      bounds:\n        x: 16\n        y: 224\n        width: 100\n        height: 16\n    - component_type: link\n      id: link_policy_search\n      label: POLICY\n      bounds:\n        x: 120\n        y: 224\n        width: 40\n        height: 16\n    - component_type: link\n      id: link_claims_search\n      label: CLAIMS\n      bounds:\n        x: 170\n        y: 224\n        width: 45\n        height: 16\n    - component_type: dropdown\n      id: dropdown_quote\n      label: Quote\n      state: expanded\n      bounds:\n        x: 16\n        y: 252\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy\n      label: Policy\n      state: active\n      bounds:\n        x: 32\n        y: 284\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_dwelling\n      label: Dwelling\n      bounds:\n        x: 32\n        y: 316\n        width: 208\n        height: 32\n      subcomponents:\n      - component_type: badge\n        id: badge_dwelling_count\n        label: '2'\n        bounds:\n          x: 210\n          y: 322\n          width: 16\n          height: 20\n    - component_type: link\n      id: link_review\n      label: Review\n      bounds:\n        x: 32\n        y: 348\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_attachments\n      label: Attachments\n      bounds:\n        x: 16\n        y: 380\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_correspondence\n      label: Correspondence\n      bounds:\n        x: 16\n        y: 412\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_tasks\n      label: Tasks\n      bounds:\n        x: 16\n        y: 444\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_notes\n      label: Notes\n      bounds:\n        x: 16\n        y: 476\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy_file\n      label: Policy File\n      bounds:\n        x: 16\n        y: 508\n        width: 224\n        height: 32\n  - component_type: action_bar\n    id: right_action_bar\n    bounds:\n      x: 1888\n      y: 169\n      width: 32\n      height: 809\n    subcomponents:\n    - component_type: button_icon\n      id: btn_summary\n      label: SUMMARY\n      bounds:\n        x: 1888\n        y: 180\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_quick_qt\n      label: WTCROFT QUICK QT\n      bounds:\n        x: 1888\n        y: 230\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_quote\n      label: NEW QUOTE\n      bounds:\n        x: 1888\n        y: 280\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_note\n      label: NEW NOTE\n      bounds:\n        x: 1888\n        y: 330\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_attach\n      label: NEW ATTACH...\n      bounds:\n        x: 1888\n        y: 380\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_task\n      label: NEW TASK\n      bounds:\n        x: 1888\n        y: 430\n        width: 32\n        height: 50\n  main_content:\n  - component_type: container\n    id: quote_summary_header\n    bounds:\n      x: 256\n      y: 169\n      width: 1632\n      height: 80\n    subcomponents:\n    - component_type: button\n      id: btn_quote\n      label: QUOTE\n      bounds:\n        x: 280\n        y: 180\n        width: 80\n        height: 36\n    - component_type: text\n      id: text_quote_number_label\n      label: Quote Number\n      bounds:\n        x: 376\n        y: 180\n        width: 80\n        height: 16\n    - component_type: text\n      id: text_quote_number_value\n      label: ***********\n      bounds:\n        x: 376\n        y: 198\n        width: 80\n        height: 16\n    - component_type: text\n      id: text_insured_label\n      label: Insured\n      bounds:\n        x: 472\n        y: 180\n        width: 80\n        height: 16\n    - component_type: link\n      id: link_insured_name\n      label: Landon Cassidy\n      bounds:\n        x: 472\n        y: 198\n        width: 90\n        height: 16\n    - component_type: text\n      id: text_product_label\n      label: Product\n      bounds:\n        x: 578\n        y: 180\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_product_value\n      label: Voluntary Homeowners (HO3)\n      bounds:\n        x: 578\n        y: 198\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_sub_type_label\n      label: Sub Type\n      bounds:\n        x: 744\n        y: 180\n        width: 50\n        height: 16\n    - component_type: text\n      id: text_sub_type_value\n      label: HO3\n      bounds:\n        x: 744\n        y: 198\n        width: 30\n        height: 16\n    - component_type: text\n      id: text_policy_term_label\n      label: Policy Term\n      bounds:\n        x: 810\n        y: 180\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_policy_term_value\n      label: 06/20/2025 - 06/20/2026\n      bounds:\n        x: 810\n        y: 198\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_producer_label\n      label: Producer\n      bounds:\n        x: 976\n        y: 180\n        width: 120\n        height: 16\n    - component_type: link\n      id: link_producer_name\n      label: HH Insurance Group, LLC\n      bounds:\n        x: 976\n        y: 198\n        width: 120\n        height: 16\n    - component_type: text\n      id: text_status_label\n      label: Status\n      bounds:\n        x: 1112\n        y: 180\n        width: 60\n        height: 16\n    - component_type: text\n      id: text_status_value\n      label: In Process\n      bounds:\n        x: 1112\n        y: 198\n        width: 60\n        height: 16\n    - component_type: text\n      id: text_premium_fees_label\n      label: Premium + Fees\n      bounds:\n        x: 1188\n        y: 180\n        width: 90\n        height: 16\n    - component_type: text\n      id: text_premium_fees_value\n      label: $17,776.90\n      bounds:\n        x: 1188\n        y: 198\n        width: 70\n        height: 16\n  - component_type: container\n    id: policy_general_form\n    bounds:\n      x: 256\n      y: 249\n      width: 1632\n      height: 831\n    subcomponents:\n    - component_type: link\n      id: link_return_to_home\n      label: < Return to Home\n      bounds:\n        x: 280\n        y: 260\n        width: 100\n        height: 20\n    - component_type: button\n      id: btn_next_page\n      label: NEXT PAGE\n      bounds:\n        x: 1290\n        y: 220\n        width: 90\n        height: 28\n    - component_type: button\n      id: btn_save\n      label: SAVE\n      bounds:\n        x: 1390\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_copy\n      label: COPY\n      bounds:\n        x: 1460\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_print\n      label: PRINT\n      bounds:\n        x: 1530\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_create_application\n      label: CREATE APPLICATION\n      bounds:\n        x: 1600\n        y: 220\n        width: 150\n        height: 28\n    - component_type: button\n      id: btn_discard_changes\n      label: DISCARD CHANGES\n      bounds:\n        x: 1760\n        y: 220\n        width: 130\n        height: 28\n    - component_type: button\n      id: btn_view_notes\n      label: VIEW NOTES\n      bounds:\n        x: 1290\n        y: 250\n        width: 100\n        height: 28\n    - component_type: button\n      id: btn_delete\n      label: DELETE\n      bounds:\n        x: 1400\n        y: 250\n        width: 70\n        height: 28\n    - component_type: button\n      id: btn_more\n      label: '... MORE'\n      bounds:\n        x: 1480\n        y: 250\n        width: 70\n        height: 28\n    - component_type: text\n      id: title_prior_carrier\n      label: Prior Carrier Details\n      bounds:\n        x: 280\n        y: 292\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_prior_carrier\n      label: Prior Carrier*\n      value: New Purchase\n      bounds:\n        x: 280\n        y: 324\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_prior_policy_expiration\n      label: Prior Policy Expiration Date\n      bounds:\n        x: 700\n        y: 324\n        width: 200\n        height: 36\n    - component_type: text\n      id: title_insured_info\n      label: Insured Information\n      bounds:\n        x: 280\n        y: 376\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_entity_type\n      label: Entity Type*\n      value: Individual\n      bounds:\n        x: 280\n        y: 408\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_first_name\n      label: First*\n      value: Landon\n      bounds:\n        x: 500\n        y: 408\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_middle_name\n      label: Middle\n      bounds:\n        x: 670\n        y: 408\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_last_name\n      label: Last*\n      value: Cassidy\n      bounds:\n        x: 840\n        y: 408\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_suffix\n      label: Suffix\n      bounds:\n        x: 1010\n        y: 408\n        width: 100\n        height: 36\n    - component_type: input\n      id: input_dob\n      label: DOB*\n      value: 05/20/1988\n      bounds:\n        x: 280\n        y: 460\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_insurance_score\n      label: Insurance Score*\n      value: Excellent (850-999)\n      bounds:\n        x: 500\n        y: 460\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_search_name\n      label: Search Name*\n      value: Landon Cassidy\n      bounds:\n        x: 280\n        y: 512\n        width: 200\n        height: 36\n    - component_type: link\n      id: link_reset\n      label: Reset\n      bounds:\n        x: 490\n        y: 520\n        width: 40\n        height: 20\n    - component_type: dropdown\n      id: dropdown_primary_phone\n      label: Primary Phone\n      value: Select...\n      bounds:\n        x: 280\n        y: 564\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_email\n      label: Email\n      bounds:\n        x: 500\n        y: 564\n        width: 200\n        height: 36\n    - component_type: checkbox\n      id: checkbox_no_email\n      label: No Email\n      bounds:\n        x: 720\n        y: 572\n        width: 80\n        height: 20\n    - component_type: text\n      id: title_dwelling_info\n      label: Dwelling Information\n      bounds:\n        x: 280\n        y: 616\n        width: 150\n        height: 20\n    - component_type: input\n      id: input_lookup_address\n      label: Lookup Address\n      bounds:\n        x: 280\n        y: 648\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_number\n      label: Number*\n      value: '4227'\n      bounds:\n        x: 500\n        y: 648\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_direction\n      label: Direction\n      bounds:\n        x: 600\n        y: 648\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_street\n      label: Street*\n      value: 5th\n      bounds:\n        x: 700\n        y: 648\n        width: 100\n        height: 36\n    - component_type: dropdown\n      id: dropdown_suffix\n      label: Suffix\n      value: Ave\n      bounds:\n        x: 820\n        y: 648\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_post_dir\n      label: Post Dir\n      value: S\n      bounds:\n        x: 920\n        y: 648\n        width: 80\n        height: 36\n    - component_type: dropdown\n      id: dropdown_type\n      label: Type\n      bounds:\n        x: 1020\n        y: 648\n        width: 80\n        height: 36\n    - component_type: checkbox\n      id: checkbox_ignore_address_validation\n      label: Ignore Address Validation\n      bounds:\n        x: 1120\n        y: 632\n        width: 150\n        height: 20\n    - component_type: input\n      id: input_number_2\n      label: Number\n      bounds:\n        x: 1120\n        y: 648\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_city\n      label: City*\n      value: St Petersburg\n      bounds:\n        x: 280\n        y: 700\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_county\n      label: County*\n      value: Pinellas\n      bounds:\n        x: 450\n        y: 700\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_state\n      label: State*\n      value: Florida\n      bounds:\n        x: 620\n        y: 700\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_zip\n      label: Zip*\n      value: 33711-1522\n      bounds:\n        x: 790\n        y: 700\n        width: 100\n        height: 36\n    - component_type: text\n      id: text_address_verified\n      label: Address Verified\n      bounds:\n        x: 910\n        y: 708\n        width: 100\n        height: 20\n    - component_type: link\n      id: link_view_map\n      label: View Map\n      bounds:\n        x: 1020\n        y: 708\n        width: 60\n        height: 20\n    - component_type: input\n      id: input_latitude\n      label: Latitude*\n      value: '27.766685'\n      bounds:\n        x: 280\n        y: 752\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_longitude\n      label: Longitude*\n      value: \"-82.690887\"\n      bounds:\n        x: 450\n        y: 752\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_construction_type\n      label: Construction Type*\n      value: Masonry\n      bounds:\n        x: 280\n        y: 804\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_occupancy\n      label: Occupancy*\n      value: Owner Occupied\n      bounds:\n        x: 450\n        y: 804\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_months_occupied\n      label: Months Occupied*\n      value: 0 to 12 Months\n      bounds:\n        x: 620\n        y: 804\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_resided_less_than_2_years\n      label: Has the Insured resided at the risk address for less than 2 years?*\n      value: 'Yes'\n      bounds:\n        x: 280\n        y: 856\n        width: 150\n        height: 36\n    - component_type: text\n      id: title_prior_address\n      label: Prior Address\n      bounds:\n        x: 280\n        y: 908\n        width: 100\n        height: 20\n    - component_type: input\n      id: input_prior_address_number\n      label: Number*\n      value: '18001'\n      bounds:\n        x: 280\n        y: 940\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_direction\n      label: Direction\n      bounds:\n        x: 380\n        y: 940\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_street\n      label: Street*\n      value: Avalon\n      state: hovered\n      bounds:\n        x: 480\n        y: 940\n        width: 100\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_address_suffix\n      label: Suffix\n      value: Ln\n      bounds:\n        x: 600\n        y: 940\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_post_dir\n      label: Post Dir\n      bounds:\n        x: 700\n        y: 940\n        width: 80\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_address_type\n      label: Type\n      bounds:\n        x: 800\n        y: 940\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_number_2\n      label: Number\n      bounds:\n        x: 900\n        y: 940\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_city\n      label: City*\n      value: Tampa\n      bounds:\n        x: 280\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_county\n      label: County*\n      value: Hillsborough\n      bounds:\n        x: 450\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_state\n      label: State*\n      value: Florida\n      bounds:\n        x: 620\n        y: 992\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_prior_zip\n      label: Zip*\n      value: 33647-3102\n      bounds:\n        x: 790\n        y: 992\n        width: 100\n        height: 36\n    - component_type: link\n      id: link_verify_address\n      label: Verify Address\n      bounds:\n        x: 910\n        y: 1000\n        width: 80\n        height: 20\n    - component_type: text\n      id: text_prior_address_verified\n      label: Address Verified\n      bounds:\n        x: 1000\n        y: 1000\n        width: 100\n        height: 20\n    - component_type: link\n      id: link_view_map_prior\n      label: View Map\n      bounds:\n        x: 1110\n        y: 1000\n        width: 60\n        height: 20\n    - component_type: button\n      id: btn_next_page_bottom\n      label: Next Page\n      bounds:\n        x: 280\n        y: 1044\n        width: 80\n        height: 36\n  footer:\n  - component_type: container\n    id: main_footer\n    bounds:\n      x: 0\n      y: 1050\n      width: 1920\n      height: 30\n    subcomponents:\n    - component_type: text\n      id: text_powered_by\n      label: Powered by GUIDEWIRE\n      bounds:\n        x: 280\n        y: 1058\n        width: 150\n        height: 20\n    - component_type: text\n      id: text_environment\n      label: 'Environment : PROD AIIG'\n      bounds:\n        x: 1200\n        y: 1058\n        width: 150\n        height: 20\n    - component_type: text\n      id: text_current_logon\n      label: 'Current Logon : AG8529A (Sign Out)'\n      bounds:\n        x: 1400\n        y: 1058\n        width: 200\n        height: 20\n    - component_type: text\n      id: text_posting_date\n      label: 'Posting Date : 05/29/2025'\n      bounds:\n        x: 1650\n        y: 1058\n        width: 150\n        height: 20\n```"]}, {"role": "model", "parts": ["```yaml\nbrowser_component:\n  tab_title: *********** - Guidewire PolicyCenter\n  url: ai.iscs.com/innovation\n  address_bar_focused: false\n  tab_state: idle\nwebpage:\n  header:\n  - component_type: container\n    id: main_header\n    bounds:\n      x: 0\n      y: 69\n      width: 1920\n      height: 61\n    subcomponents:\n    - component_type: image\n      id: logo_american_integrity\n      label: AMERICAN INTEGRITY\n      bounds:\n        x: 16\n        y: 116\n        width: 128\n        height: 40\n    - component_type: navigation_menu\n      id: top_navigation\n      bounds:\n        x: 1008\n        y: 116\n        width: 882\n        height: 36\n      subcomponents:\n      - component_type: link\n        id: link_home\n        label: Home\n        bounds:\n          x: 1298\n          y: 126\n          width: 40\n          height: 20\n      - component_type: link\n        id: link_quote_policy\n        label: Quote/Policy\n        state: active\n        bounds:\n          x: 1362\n          y: 126\n          width: 80\n          height: 20\n      - component_type: link\n        id: link_claims\n        label: Claims\n        bounds:\n          x: 1466\n          y: 126\n          width: 48\n          height: 20\n      - component_type: link\n        id: link_cabinets\n        label: Cabinets\n        bounds:\n          x: 1538\n          y: 126\n          width: 58\n          height: 20\n      - component_type: link\n        id: link_support\n        label: Support\n        bounds:\n          x: 1620\n          y: 126\n          width: 54\n          height: 20\n  sidebar:\n  - component_type: navigation_menu\n    id: left_navigation\n    bounds:\n      x: 0\n      y: 169\n      width: 256\n      height: 809\n    subcomponents:\n    - component_type: search_input\n      id: search_sidebar\n      label: Search\n      bounds:\n        x: 16\n        y: 180\n        width: 188\n        height: 36\n    - component_type: button_icon\n      id: btn_search\n      label: Search\n      bounds:\n        x: 204\n        y: 180\n        width: 36\n        height: 36\n    - component_type: text\n      id: text_advanced_search\n      label: 'ADVANCED SEARCH:'\n      bounds:\n        x: 16\n        y: 224\n        width: 100\n        height: 16\n    - component_type: link\n      id: link_policy_search\n      label: POLICY\n      bounds:\n        x: 120\n        y: 224\n        width: 40\n        height: 16\n    - component_type: link\n      id: link_claims_search\n      label: CLAIMS\n      bounds:\n        x: 170\n        y: 224\n        width: 45\n        height: 16\n    - component_type: dropdown\n      id: dropdown_quote\n      label: Quote\n      state: expanded\n      bounds:\n        x: 16\n        y: 252\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy\n      label: Policy\n      state: active\n      bounds:\n        x: 32\n        y: 284\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_dwelling\n      label: Dwelling\n      bounds:\n        x: 32\n        y: 316\n        width: 208\n        height: 32\n      subcomponents:\n      - component_type: badge\n        id: badge_dwelling_count\n        label: '2'\n        bounds:\n          x: 210\n          y: 322\n          width: 16\n          height: 20\n    - component_type: link\n      id: link_review\n      label: Review\n      bounds:\n        x: 32\n        y: 348\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_attachments\n      label: Attachments\n      bounds:\n        x: 16\n        y: 380\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_correspondence\n      label: Correspondence\n      bounds:\n        x: 16\n        y: 412\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_tasks\n      label: Tasks\n      bounds:\n        x: 16\n        y: 444\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_notes\n      label: Notes\n      bounds:\n        x: 16\n        y: 476\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy_file\n      label: Policy File\n      bounds:\n        x: 16\n        y: 508\n        width: 224\n        height: 32\n  - component_type: action_bar\n    id: right_action_bar\n    bounds:\n      x: 1888\n      y: 169\n      width: 32\n      height: 809\n    subcomponents:\n    - component_type: button_icon\n      id: btn_summary\n      label: SUMMARY\n      bounds:\n        x: 1888\n        y: 180\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_quick_qt\n      label: WTCROFT QUICK QT\n      bounds:\n        x: 1888\n        y: 230\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_quote\n      label: NEW QUOTE\n      bounds:\n        x: 1888\n        y: 280\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_note\n      label: NEW NOTE\n      bounds:\n        x: 1888\n        y: 330\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_attach\n      label: NEW ATTACH...\n      bounds:\n        x: 1888\n        y: 380\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_task\n      label: NEW TASK\n      bounds:\n        x: 1888\n        y: 430\n        width: 32\n        height: 50\n  main_content:\n  - component_type: container\n    id: quote_summary_header\n    bounds:\n      x: 256\n      y: 169\n      width: 1632\n      height: 80\n    subcomponents:\n    - component_type: button\n      id: btn_quote\n      label: QUOTE\n      bounds:\n        x: 280\n        y: 180\n        width: 80\n        height: 36\n    - component_type: text\n      id: text_quote_number_label\n      label: Quote Number\n      bounds:\n        x: 376\n        y: 180\n        width: 80\n        height: 16\n    - component_type: text\n      id: text_quote_number_value\n      label: ***********\n      bounds:\n        x: 376\n        y: 198\n        width: 80\n        height: 16\n    - component_type: text\n      id: text_insured_label\n      label: Insured\n      bounds:\n        x: 472\n        y: 180\n        width: 80\n        height: 16\n    - component_type: link\n      id: link_insured_name\n      label: Landon Cassidy\n      bounds:\n        x: 472\n        y: 198\n        width: 90\n        height: 16\n    - component_type: text\n      id: text_product_label\n      label: Product\n      bounds:\n        x: 578\n        y: 180\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_product_value\n      label: Voluntary Homeowners (HO3)\n      bounds:\n        x: 578\n        y: 198\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_sub_type_label\n      label: Sub Type\n      bounds:\n        x: 744\n        y: 180\n        width: 50\n        height: 16\n    - component_type: text\n      id: text_sub_type_value\n      label: HO3\n      bounds:\n        x: 744\n        y: 198\n        width: 30\n        height: 16\n    - component_type: text\n      id: text_policy_term_label\n      label: Policy Term\n      bounds:\n        x: 810\n        y: 180\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_policy_term_value\n      label: 06/20/2025 - 06/20/2026\n      bounds:\n        x: 810\n        y: 198\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_producer_label\n      label: Producer\n      bounds:\n        x: 976\n        y: 180\n        width: 120\n        height: 16\n    - component_type: link\n      id: link_producer_name\n      label: HH Insurance Group, LLC\n      bounds:\n        x: 976\n        y: 198\n        width: 120\n        height: 16\n    - component_type: text\n      id: text_status_label\n      label: Status\n      bounds:\n        x: 1112\n        y: 180\n        width: 60\n        height: 16\n    - component_type: text\n      id: text_status_value\n      label: In Process\n      bounds:\n        x: 1112\n        y: 198\n        width: 60\n        height: 16\n    - component_type: text\n      id: text_premium_fees_label\n      label: Premium + Fees\n      bounds:\n        x: 1188\n        y: 180\n        width: 90\n        height: 16\n    - component_type: text\n      id: text_premium_fees_value\n      label: $17,776.90\n      bounds:\n        x: 1188\n        y: 198\n        width: 70\n        height: 16\n  - component_type: container\n    id: policy_general_form\n    bounds:\n      x: 256\n      y: 249\n      width: 1632\n      height: 831\n    subcomponents:\n    - component_type: link\n      id: link_return_to_home\n      label: < Return to Home\n      bounds:\n        x: 280\n        y: 260\n        width: 100\n        height: 20\n    - component_type: button\n      id: btn_next_page\n      label: NEXT PAGE\n      bounds:\n        x: 1290\n        y: 220\n        width: 90\n        height: 28\n    - component_type: button\n      id: btn_save\n      label: SAVE\n      bounds:\n        x: 1390\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_copy\n      label: COPY\n      bounds:\n        x: 1460\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_print\n      label: PRINT\n      bounds:\n        x: 1530\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_create_application\n      label: CREATE APPLICATION\n      bounds:\n        x: 1600\n        y: 220\n        width: 150\n        height: 28\n    - component_type: button\n      id: btn_discard_changes\n      label: DISCARD CHANGES\n      bounds:\n        x: 1760\n        y: 220\n        width: 130\n        height: 28\n    - component_type: button\n      id: btn_view_notes\n      label: VIEW NOTES\n      bounds:\n        x: 1290\n        y: 250\n        width: 100\n        height: 28\n    - component_type: button\n      id: btn_delete\n      label: DELETE\n      bounds:\n        x: 1400\n        y: 250\n        width: 70\n        height: 28\n    - component_type: button\n      id: btn_more\n      label: '... MORE'\n      bounds:\n        x: 1480\n        y: 250\n        width: 70\n        height: 28\n    - component_type: text\n      id: title_prior_carrier\n      label: Prior Carrier Details\n      bounds:\n        x: 280\n        y: 292\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_prior_carrier\n      label: Prior Carrier*\n      value: New Purchase\n      bounds:\n        x: 280\n        y: 324\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_prior_policy_expiration\n      label: Prior Policy Expiration Date\n      bounds:\n        x: 700\n        y: 324\n        width: 200\n        height: 36\n    - component_type: text\n      id: title_insured_info\n      label: Insured Information\n      bounds:\n        x: 280\n        y: 376\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_entity_type\n      label: Entity Type*\n      value: Individual\n      bounds:\n        x: 280\n        y: 408\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_first_name\n      label: First*\n      value: Landon\n      bounds:\n        x: 500\n        y: 408\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_middle_name\n      label: Middle\n      bounds:\n        x: 670\n        y: 408\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_last_name\n      label: Last*\n      value: Cassidy\n      bounds:\n        x: 840\n        y: 408\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_suffix\n      label: Suffix\n      bounds:\n        x: 1010\n        y: 408\n        width: 100\n        height: 36\n    - component_type: input\n      id: input_dob\n      label: DOB*\n      value: 05/20/1988\n      bounds:\n        x: 280\n        y: 460\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_insurance_score\n      label: Insurance Score*\n      value: Excellent (850-999)\n      bounds:\n        x: 500\n        y: 460\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_search_name\n      label: Search Name*\n      value: Landon Cassidy\n      bounds:\n        x: 280\n        y: 512\n        width: 200\n        height: 36\n    - component_type: link\n      id: link_reset\n      label: Reset\n      bounds:\n        x: 490\n        y: 520\n        width: 40\n        height: 20\n    - component_type: dropdown\n      id: dropdown_primary_phone\n      label: Primary Phone\n      value: Select...\n      bounds:\n        x: 280\n        y: 564\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_email\n      label: Email\n      bounds:\n        x: 500\n        y: 564\n        width: 200\n        height: 36\n    - component_type: checkbox\n      id: checkbox_no_email\n      label: No Email\n      bounds:\n        x: 720\n        y: 572\n        width: 80\n        height: 20\n    - component_type: text\n      id: title_dwelling_info\n      label: Dwelling Information\n      bounds:\n        x: 280\n        y: 616\n        width: 150\n        height: 20\n    - component_type: input\n      id: input_lookup_address\n      label: Lookup Address\n      bounds:\n        x: 280\n        y: 648\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_number\n      label: Number*\n      value: '4227'\n      bounds:\n        x: 500\n        y: 648\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_direction\n      label: Direction\n      bounds:\n        x: 600\n        y: 648\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_street\n      label: Street*\n      value: 5th\n      bounds:\n        x: 700\n        y: 648\n        width: 100\n        height: 36\n    - component_type: dropdown\n      id: dropdown_suffix\n      label: Suffix\n      value: Ave\n      bounds:\n        x: 820\n        y: 648\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_post_dir\n      label: Post Dir\n      value: S\n      bounds:\n        x: 920\n        y: 648\n        width: 80\n        height: 36\n    - component_type: dropdown\n      id: dropdown_type\n      label: Type\n      bounds:\n        x: 1020\n        y: 648\n        width: 80\n        height: 36\n    - component_type: checkbox\n      id: checkbox_ignore_address_validation\n      label: Ignore Address Validation\n      bounds:\n        x: 1120\n        y: 632\n        width: 150\n        height: 20\n    - component_type: input\n      id: input_number_2\n      label: Number\n      bounds:\n        x: 1120\n        y: 648\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_city\n      label: City*\n      value: St Petersburg\n      bounds:\n        x: 280\n        y: 700\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_county\n      label: County*\n      value: Pinellas\n      bounds:\n        x: 450\n        y: 700\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_state\n      label: State*\n      value: Florida\n      bounds:\n        x: 620\n        y: 700\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_zip\n      label: Zip*\n      value: 33711-1522\n      bounds:\n        x: 790\n        y: 700\n        width: 100\n        height: 36\n    - component_type: text\n      id: text_address_verified\n      label: Address Verified\n      bounds:\n        x: 910\n        y: 708\n        width: 100\n        height: 20\n    - component_type: link\n      id: link_view_map\n      label: View Map\n      bounds:\n        x: 1020\n        y: 708\n        width: 60\n        height: 20\n    - component_type: input\n      id: input_latitude\n      label: Latitude*\n      value: '27.766685'\n      bounds:\n        x: 280\n        y: 752\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_longitude\n      label: Longitude*\n      value: \"-82.690887\"\n      bounds:\n        x: 450\n        y: 752\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_construction_type\n      label: Construction Type*\n      value: Masonry\n      bounds:\n        x: 280\n        y: 804\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_occupancy\n      label: Occupancy*\n      value: Owner Occupied\n      bounds:\n        x: 450\n        y: 804\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_months_occupied\n      label: Months Occupied*\n      value: 0 to 12 Months\n      bounds:\n        x: 620\n        y: 804\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_resided_less_than_2_years\n      label: Has the Insured resided at the risk address for less than 2 years?*\n      value: 'Yes'\n      bounds:\n        x: 280\n        y: 856\n        width: 150\n        height: 36\n    - component_type: text\n      id: title_prior_address\n      label: Prior Address\n      bounds:\n        x: 280\n        y: 908\n        width: 100\n        height: 20\n    - component_type: input\n      id: input_prior_address_number\n      label: Number*\n      value: '18001'\n      bounds:\n        x: 280\n        y: 940\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_direction\n      label: Direction\n      bounds:\n        x: 380\n        y: 940\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_street\n      label: Street*\n      value: Avalon\n      bounds:\n        x: 480\n        y: 940\n        width: 100\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_address_suffix\n      label: Suffix\n      value: Ln\n      bounds:\n        x: 600\n        y: 940\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_post_dir\n      label: Post Dir\n      bounds:\n        x: 700\n        y: 940\n        width: 80\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_address_type\n      label: Type\n      bounds:\n        x: 800\n        y: 940\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_number_2\n      label: Number\n      bounds:\n        x: 900\n        y: 940\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_city\n      label: City*\n      value: Tampa\n      bounds:\n        x: 280\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_county\n      label: County*\n      value: Hillsborough\n      bounds:\n        x: 450\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_state\n      label: State*\n      value: Florida\n      bounds:\n        x: 620\n        y: 992\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_prior_zip\n      label: Zip*\n      value: 33647-3102\n      bounds:\n        x: 790\n        y: 992\n        width: 100\n        height: 36\n    - component_type: link\n      id: link_verify_address\n      label: Verify Address\n      bounds:\n        x: 910\n        y: 1000\n        width: 80\n        height: 20\n    - component_type: text\n      id: text_prior_address_verified\n      label: Address Verified\n      bounds:\n        x: 1000\n        y: 1000\n        width: 100\n        height: 20\n    - component_type: link\n      id: link_view_map_prior\n      label: View Map\n      bounds:\n        x: 1110\n        y: 1000\n        width: 60\n        height: 20\n    - component_type: button\n      id: btn_next_page_bottom\n      label: Next Page\n      bounds:\n        x: 280\n        y: 1044\n        width: 80\n        height: 36\n  footer:\n  - component_type: container\n    id: main_footer\n    bounds:\n      x: 0\n      y: 1050\n      width: 1920\n      height: 30\n    subcomponents:\n    - component_type: text\n      id: text_powered_by\n      label: Powered by GUIDEWIRE\n      bounds:\n        x: 280\n        y: 1058\n        width: 150\n        height: 20\n    - component_type: text\n      id: text_environment\n      label: 'Environment : PROD AIIG'\n      bounds:\n        x: 1200\n        y: 1058\n        width: 150\n        height: 20\n    - component_type: text\n      id: text_current_logon\n      label: 'Current Logon : AG8529A (Sign Out)'\n      bounds:\n        x: 1400\n        y: 1058\n        width: 200\n        height: 20\n    - component_type: text\n      id: text_posting_date\n      label: 'Posting Date : 05/29/2025'\n      bounds:\n        x: 1650\n        y: 1058\n        width: 150\n        height: 20\n```"]}, {"role": "model", "parts": ["```yaml\nbrowser_component:\n  tab_title: *********** - Guidewire PolicyCenter\n  url: ai.iscs.com/innovation\n  address_bar_focused: false\n  tab_state: idle\nwebpage:\n  header:\n  - component_type: container\n    id: main_header\n    bounds:\n      x: 0\n      y: 69\n      width: 1920\n      height: 61\n    subcomponents:\n    - component_type: image\n      id: logo_american_integrity\n      label: AMERICAN INTEGRITY\n      bounds:\n        x: 16\n        y: 116\n        width: 128\n        height: 40\n    - component_type: navigation_menu\n      id: top_navigation\n      bounds:\n        x: 1290\n        y: 116\n        width: 600\n        height: 36\n      subcomponents:\n      - component_type: link\n        id: link_home\n        label: Home\n        bounds:\n          x: 1298\n          y: 126\n          width: 40\n          height: 20\n      - component_type: link\n        id: link_quote_policy\n        label: Quote/Policy\n        state: active\n        bounds:\n          x: 1362\n          y: 126\n          width: 80\n          height: 20\n      - component_type: link\n        id: link_claims\n        label: Claims\n        bounds:\n          x: 1466\n          y: 126\n          width: 48\n          height: 20\n      - component_type: link\n        id: link_cabinets\n        label: Cabinets\n        bounds:\n          x: 1538\n          y: 126\n          width: 58\n          height: 20\n      - component_type: link\n        id: link_support\n        label: Support\n        bounds:\n          x: 1620\n          y: 126\n          width: 54\n          height: 20\n  sidebar:\n  - component_type: navigation_menu\n    id: left_navigation\n    bounds:\n      x: 0\n      y: 169\n      width: 256\n      height: 809\n    subcomponents:\n    - component_type: search_input\n      id: search_sidebar\n      label: Search\n      bounds:\n        x: 16\n        y: 180\n        width: 188\n        height: 36\n    - component_type: button_icon\n      id: btn_search\n      label: Search\n      bounds:\n        x: 204\n        y: 180\n        width: 36\n        height: 36\n    - component_type: text\n      id: text_advanced_search\n      label: 'ADVANCED SEARCH:'\n      bounds:\n        x: 16\n        y: 224\n        width: 100\n        height: 16\n    - component_type: link\n      id: link_policy_search\n      label: POLICY\n      bounds:\n        x: 120\n        y: 224\n        width: 40\n        height: 16\n    - component_type: link\n      id: link_claims_search\n      label: CLAIMS\n      bounds:\n        x: 170\n        y: 224\n        width: 45\n        height: 16\n    - component_type: dropdown\n      id: dropdown_quote\n      label: Quote\n      state: expanded\n      bounds:\n        x: 16\n        y: 252\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy\n      label: Policy\n      state: active\n      bounds:\n        x: 32\n        y: 284\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_dwelling\n      label: Dwelling\n      bounds:\n        x: 32\n        y: 316\n        width: 208\n        height: 32\n      subcomponents:\n      - component_type: badge\n        id: badge_dwelling_count\n        label: '2'\n        bounds:\n          x: 210\n          y: 322\n          width: 16\n          height: 20\n    - component_type: link\n      id: link_review\n      label: Review\n      bounds:\n        x: 32\n        y: 348\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_attachments\n      label: Attachments\n      bounds:\n        x: 16\n        y: 380\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_correspondence\n      label: Correspondence\n      bounds:\n        x: 16\n        y: 412\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_tasks\n      label: Tasks\n      bounds:\n        x: 16\n        y: 444\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_notes\n      label: Notes\n      bounds:\n        x: 16\n        y: 476\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy_file\n      label: Policy File\n      bounds:\n        x: 16\n        y: 508\n        width: 224\n        height: 32\n  - component_type: action_bar\n    id: right_action_bar\n    bounds:\n      x: 1888\n      y: 169\n      width: 32\n      height: 809\n    subcomponents:\n    - component_type: button_icon\n      id: btn_summary\n      label: SUMMARY\n      bounds:\n        x: 1888\n        y: 180\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_quick_qt\n      label: WTCROFT QUICK QT\n      bounds:\n        x: 1888\n        y: 230\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_quote\n      label: NEW QUOTE\n      bounds:\n        x: 1888\n        y: 280\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_note\n      label: NEW NOTE\n      bounds:\n        x: 1888\n        y: 330\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_attach\n      label: NEW ATTACH...\n      bounds:\n        x: 1888\n        y: 380\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_task\n      label: NEW TASK\n      bounds:\n        x: 1888\n        y: 430\n        width: 32\n        height: 50\n  main_content:\n  - component_type: container\n    id: quote_summary_header\n    bounds:\n      x: 256\n      y: 169\n      width: 1632\n      height: 80\n    subcomponents:\n    - component_type: button\n      id: btn_quote\n      label: QUOTE\n      bounds:\n        x: 280\n        y: 180\n        width: 80\n        height: 36\n    - component_type: text\n      id: text_quote_number_label\n      label: Quote Number\n      bounds:\n        x: 376\n        y: 180\n        width: 80\n        height: 16\n    - component_type: text\n      id: text_quote_number_value\n      label: ***********\n      bounds:\n        x: 376\n        y: 198\n        width: 80\n        height: 16\n    - component_type: text\n      id: text_insured_label\n      label: Insured\n      bounds:\n        x: 472\n        y: 180\n        width: 80\n        height: 16\n    - component_type: link\n      id: link_insured_name\n      label: Landon Cassidy\n      bounds:\n        x: 472\n        y: 198\n        width: 90\n        height: 16\n    - component_type: text\n      id: text_product_label\n      label: Product\n      bounds:\n        x: 578\n        y: 180\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_product_value\n      label: Voluntary Homeowners (HO3)\n      bounds:\n        x: 578\n        y: 198\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_sub_type_label\n      label: Sub Type\n      bounds:\n        x: 744\n        y: 180\n        width: 50\n        height: 16\n    - component_type: text\n      id: text_sub_type_value\n      label: HO3\n      bounds:\n        x: 744\n        y: 198\n        width: 30\n        height: 16\n    - component_type: text\n      id: text_policy_term_label\n      label: Policy Term\n      bounds:\n        x: 810\n        y: 180\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_policy_term_value\n      label: 06/20/2025 - 06/20/2026\n      bounds:\n        x: 810\n        y: 198\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_producer_label\n      label: Producer\n      bounds:\n        x: 976\n        y: 180\n        width: 120\n        height: 16\n    - component_type: link\n      id: link_producer_name\n      label: HH Insurance Group, LLC\n      bounds:\n        x: 976\n        y: 198\n        width: 120\n        height: 16\n    - component_type: text\n      id: text_status_label\n      label: Status\n      bounds:\n        x: 1112\n        y: 180\n        width: 60\n        height: 16\n    - component_type: text\n      id: text_status_value\n      label: In Process\n      bounds:\n        x: 1112\n        y: 198\n        width: 60\n        height: 16\n    - component_type: text\n      id: text_premium_fees_label\n      label: Premium + Fees\n      bounds:\n        x: 1188\n        y: 180\n        width: 90\n        height: 16\n    - component_type: text\n      id: text_premium_fees_value\n      label: $17,776.90\n      bounds:\n        x: 1188\n        y: 198\n        width: 70\n        height: 16\n  - component_type: container\n    id: policy_general_form\n    bounds:\n      x: 256\n      y: 249\n      width: 1632\n      height: 831\n    subcomponents:\n    - component_type: link\n      id: link_return_to_home\n      label: < Return to Home\n      bounds:\n        x: 280\n        y: 260\n        width: 100\n        height: 20\n    - component_type: button\n      id: btn_next_page\n      label: NEXT PAGE\n      bounds:\n        x: 1290\n        y: 220\n        width: 90\n        height: 28\n    - component_type: button\n      id: btn_save\n      label: SAVE\n      bounds:\n        x: 1390\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_copy\n      label: COPY\n      bounds:\n        x: 1460\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_print\n      label: PRINT\n      bounds:\n        x: 1530\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_create_application\n      label: CREATE APPLICATION\n      bounds:\n        x: 1600\n        y: 220\n        width: 150\n        height: 28\n    - component_type: button\n      id: btn_discard_changes\n      label: DISCARD CHANGES\n      bounds:\n        x: 1760\n        y: 220\n        width: 130\n        height: 28\n    - component_type: button\n      id: btn_view_notes\n      label: VIEW NOTES\n      bounds:\n        x: 1290\n        y: 250\n        width: 100\n        height: 28\n    - component_type: button\n      id: btn_delete\n      label: DELETE\n      bounds:\n        x: 1400\n        y: 250\n        width: 70\n        height: 28\n    - component_type: button\n      id: btn_more\n      label: '... MORE'\n      bounds:\n        x: 1480\n        y: 250\n        width: 70\n        height: 28\n    - component_type: text\n      id: title_prior_carrier\n      label: Prior Carrier Details\n      bounds:\n        x: 280\n        y: 292\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_prior_carrier\n      label: Prior Carrier*\n      value: New Purchase\n      bounds:\n        x: 280\n        y: 324\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_prior_policy_expiration\n      label: Prior Policy Expiration Date\n      bounds:\n        x: 700\n        y: 324\n        width: 200\n        height: 36\n    - component_type: text\n      id: title_insured_info\n      label: Insured Information\n      bounds:\n        x: 280\n        y: 376\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_entity_type\n      label: Entity Type*\n      value: Individual\n      bounds:\n        x: 280\n        y: 408\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_first_name\n      label: First*\n      value: Landon\n      bounds:\n        x: 500\n        y: 408\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_middle_name\n      label: Middle\n      bounds:\n        x: 670\n        y: 408\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_last_name\n      label: Last*\n      value: Cassidy\n      bounds:\n        x: 840\n        y: 408\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_suffix\n      label: Suffix\n      bounds:\n        x: 1010\n        y: 408\n        width: 100\n        height: 36\n    - component_type: input\n      id: input_dob\n      label: DOB*\n      value: 05/20/1998\n      bounds:\n        x: 280\n        y: 460\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_insurance_score\n      label: Insurance Score*\n      value: Excellent (850-999)\n      bounds:\n        x: 500\n        y: 460\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_search_name\n      label: Search Name*\n      value: Landon Cassidy\n      bounds:\n        x: 280\n        y: 512\n        width: 200\n        height: 36\n    - component_type: link\n      id: link_reset\n      label: Reset\n      bounds:\n        x: 490\n        y: 520\n        width: 40\n        height: 20\n    - component_type: dropdown\n      id: dropdown_primary_phone\n      label: Primary Phone\n      value: Select...\n      bounds:\n        x: 280\n        y: 564\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_email\n      label: Email\n      bounds:\n        x: 500\n        y: 564\n        width: 200\n        height: 36\n    - component_type: checkbox\n      id: checkbox_no_email\n      label: No Email\n      bounds:\n        x: 720\n        y: 572\n        width: 80\n        height: 20\n    - component_type: text\n      id: title_dwelling_info\n      label: Dwelling Information\n      bounds:\n        x: 280\n        y: 616\n        width: 150\n        height: 20\n    - component_type: input\n      id: input_lookup_address\n      label: Lookup Address\n      bounds:\n        x: 280\n        y: 648\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_number\n      label: Number*\n      value: '4227'\n      bounds:\n        x: 500\n        y: 648\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_direction\n      label: Direction\n      bounds:\n        x: 600\n        y: 648\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_street\n      label: Street*\n      value: 5th\n      bounds:\n        x: 700\n        y: 648\n        width: 100\n        height: 36\n    - component_type: dropdown\n      id: dropdown_suffix\n      label: Suffix\n      value: Ave\n      bounds:\n        x: 820\n        y: 648\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_post_dir\n      label: Post Dir\n      value: S\n      bounds:\n        x: 920\n        y: 648\n        width: 80\n        height: 36\n    - component_type: dropdown\n      id: dropdown_type\n      label: Type\n      bounds:\n        x: 1020\n        y: 648\n        width: 80\n        height: 36\n    - component_type: checkbox\n      id: checkbox_ignore_address_validation\n      label: Ignore Address Validation\n      bounds:\n        x: 1120\n        y: 632\n        width: 150\n        height: 20\n    - component_type: input\n      id: input_number_2\n      label: Number\n      bounds:\n        x: 1120\n        y: 648\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_city\n      label: City*\n      value: St Petersburg\n      bounds:\n        x: 280\n        y: 700\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_county\n      label: County*\n      value: Pinellas\n      bounds:\n        x: 450\n        y: 700\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_state\n      label: State*\n      value: Florida\n      bounds:\n        x: 620\n        y: 700\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_zip\n      label: Zip*\n      value: 33711-1522\n      bounds:\n        x: 790\n        y: 700\n        width: 100\n        height: 36\n    - component_type: text\n      id: text_address_verified\n      label: Address Verified\n      bounds:\n        x: 910\n        y: 708\n        width: 100\n        height: 20\n    - component_type: link\n      id: link_view_map\n      label: View Map\n      bounds:\n        x: 1020\n        y: 708\n        width: 60\n        height: 20\n    - component_type: input\n      id: input_latitude\n      label: Latitude*\n      value: '27.766685'\n      bounds:\n        x: 280\n        y: 752\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_longitude\n      label: Longitude*\n      value: \"-82.690887\"\n      bounds:\n        x: 450\n        y: 752\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_construction_type\n      label: Construction Type*\n      value: Masonry\n      state: hovered\n      bounds:\n        x: 280\n        y: 804\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_occupancy\n      label: Occupancy*\n      value: Owner Occupied\n      bounds:\n        x: 450\n        y: 804\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_months_occupied\n      label: Months Occupied*\n      value: 0 to 12 Months\n      bounds:\n        x: 620\n        y: 804\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_resided_less_than_2_years\n      label: Has the Insured resided at the risk address for less than 2 years?*\n      value: 'Yes'\n      bounds:\n        x: 280\n        y: 856\n        width: 150\n        height: 36\n    - component_type: text\n      id: title_prior_address\n      label: Prior Address\n      bounds:\n        x: 280\n        y: 908\n        width: 100\n        height: 20\n    - component_type: input\n      id: input_prior_address_number\n      label: Number*\n      value: '18001'\n      bounds:\n        x: 280\n        y: 940\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_direction\n      label: Direction\n      bounds:\n        x: 380\n        y: 940\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_street\n      label: Street*\n      value: Avalon\n      bounds:\n        x: 480\n        y: 940\n        width: 100\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_address_suffix\n      label: Suffix\n      value: Ln\n      bounds:\n        x: 600\n        y: 940\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_post_dir\n      label: Post Dir\n      bounds:\n        x: 700\n        y: 940\n        width: 80\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_address_type\n      label: Type\n      bounds:\n        x: 800\n        y: 940\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_number_2\n      label: Number\n      bounds:\n        x: 900\n        y: 940\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_city\n      label: City*\n      value: Tampa\n      bounds:\n        x: 280\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_county\n      label: County*\n      value: Hillsborough\n      bounds:\n        x: 450\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_state\n      label: State*\n      value: Florida\n      bounds:\n        x: 620\n        y: 992\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_prior_zip\n      label: Zip*\n      value: 33647-3102\n      bounds:\n        x: 790\n        y: 992\n        width: 100\n        height: 36\n    - component_type: link\n      id: link_verify_address\n      label: Verify Address\n      bounds:\n        x: 910\n        y: 1000\n        width: 80\n        height: 20\n    - component_type: text\n      id: text_prior_address_verified\n      label: Address Verified\n      bounds:\n        x: 1000\n        y: 1000\n        width: 100\n        height: 20\n    - component_type: link\n      id: link_view_map_prior\n      label: View Map\n      bounds:\n        x: 1110\n        y: 1000\n        width: 60\n        height: 20\n    - component_type: button\n      id: btn_next_page_bottom\n      label: Next Page\n      bounds:\n        x: 280\n        y: 1044\n        width: 80\n        height: 36\n  footer:\n  - component_type: container\n    id: main_footer\n    bounds:\n      x: 0\n      y: 1050\n      width: 1920\n      height: 30\n    subcomponents:\n    - component_type: text\n      id: text_powered_by\n      label: Powered by GUIDEWIRE\n      bounds:\n        x: 280\n        y: 1058\n        width: 150\n        height: 20\n    - component_type: text\n      id: text_environment\n      label: 'Environment : PROD AIIG'\n      bounds:\n        x: 1200\n        y: 1058\n        width: 150\n        height: 20\n    - component_type: text\n      id: text_current_logon\n      label: 'Current Logon : AG8529A (Sign Out)'\n      bounds:\n        x: 1400\n        y: 1058\n        width: 200\n        height: 20\n    - component_type: text\n      id: text_posting_date\n      label: 'Posting Date : 05/29/2025'\n      bounds:\n        x: 1650\n        y: 1058\n        width: 150\n        height: 20\n```"]}, {"role": "model", "parts": ["```yaml\nbrowser_component:\n  tab_title: *********** - Guidewire PolicyCenter\n  url: ai.iscs.com/innovation\n  address_bar_focused: false\n  tab_state: idle\nwebpage:\n  header:\n  - component_type: container\n    id: main_header\n    bounds:\n      x: 0\n      y: 69\n      width: 1920\n      height: 61\n    subcomponents:\n    - component_type: image\n      id: logo_american_integrity\n      label: AMERICAN INTEGRITY\n      bounds:\n        x: 16\n        y: 116\n        width: 128\n        height: 40\n    - component_type: navigation_menu\n      id: top_navigation\n      bounds:\n        x: 1008\n        y: 116\n        width: 882\n        height: 36\n      subcomponents:\n      - component_type: link\n        id: link_home\n        label: Home\n        bounds:\n          x: 1298\n          y: 126\n          width: 40\n          height: 20\n      - component_type: link\n        id: link_quote_policy\n        label: Quote/Policy\n        state: active\n        bounds:\n          x: 1362\n          y: 126\n          width: 80\n          height: 20\n      - component_type: link\n        id: link_claims\n        label: Claims\n        bounds:\n          x: 1466\n          y: 126\n          width: 48\n          height: 20\n      - component_type: link\n        id: link_cabinets\n        label: Cabinets\n        bounds:\n          x: 1538\n          y: 126\n          width: 58\n          height: 20\n      - component_type: link\n        id: link_support\n        label: Support\n        bounds:\n          x: 1620\n          y: 126\n          width: 54\n          height: 20\n  sidebar:\n  - component_type: navigation_menu\n    id: left_navigation\n    bounds:\n      x: 0\n      y: 169\n      width: 256\n      height: 809\n    subcomponents:\n    - component_type: search_input\n      id: search_sidebar\n      label: Search\n      bounds:\n        x: 16\n        y: 180\n        width: 188\n        height: 36\n    - component_type: button_icon\n      id: btn_search\n      label: Search\n      bounds:\n        x: 204\n        y: 180\n        width: 36\n        height: 36\n    - component_type: text\n      id: text_advanced_search\n      label: 'ADVANCED SEARCH:'\n      bounds:\n        x: 16\n        y: 224\n        width: 100\n        height: 16\n    - component_type: link\n      id: link_policy_search\n      label: POLICY\n      bounds:\n        x: 120\n        y: 224\n        width: 40\n        height: 16\n    - component_type: link\n      id: link_claims_search\n      label: CLAIMS\n      bounds:\n        x: 170\n        y: 224\n        width: 45\n        height: 16\n    - component_type: dropdown\n      id: dropdown_quote\n      label: Quote\n      state: expanded\n      bounds:\n        x: 16\n        y: 252\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy\n      label: Policy\n      state: active\n      bounds:\n        x: 32\n        y: 284\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_dwelling\n      label: Dwelling\n      bounds:\n        x: 32\n        y: 316\n        width: 208\n        height: 32\n      subcomponents:\n      - component_type: badge\n        id: badge_dwelling_count\n        label: '2'\n        bounds:\n          x: 210\n          y: 322\n          width: 16\n          height: 20\n    - component_type: link\n      id: link_review\n      label: Review\n      bounds:\n        x: 32\n        y: 348\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_attachments\n      label: Attachments\n      bounds:\n        x: 16\n        y: 380\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_correspondence\n      label: Correspondence\n      bounds:\n        x: 16\n        y: 412\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_tasks\n      label: Tasks\n      bounds:\n        x: 16\n        y: 444\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_notes\n      label: Notes\n      bounds:\n        x: 16\n        y: 476\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy_file\n      label: Policy File\n      bounds:\n        x: 16\n        y: 508\n        width: 224\n        height: 32\n  - component_type: action_bar\n    id: right_action_bar\n    bounds:\n      x: 1888\n      y: 169\n      width: 32\n      height: 809\n    subcomponents:\n    - component_type: button_icon\n      id: btn_summary\n      label: SUMMARY\n      bounds:\n        x: 1888\n        y: 180\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_quick_qt\n      label: WTCROFT QUICK QT\n      bounds:\n        x: 1888\n        y: 230\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_quote\n      label: NEW QUOTE\n      bounds:\n        x: 1888\n        y: 280\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_note\n      label: NEW NOTE\n      bounds:\n        x: 1888\n        y: 330\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_attach\n      label: NEW ATTACH...\n      bounds:\n        x: 1888\n        y: 380\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_task\n      label: NEW TASK\n      bounds:\n        x: 1888\n        y: 430\n        width: 32\n        height: 50\n  main_content:\n  - component_type: container\n    id: quote_summary_header\n    bounds:\n      x: 256\n      y: 169\n      width: 1632\n      height: 80\n    subcomponents:\n    - component_type: button\n      id: btn_quote\n      label: QUOTE\n      bounds:\n        x: 280\n        y: 180\n        width: 80\n        height: 36\n    - component_type: text\n      id: text_quote_number_label\n      label: Quote Number\n      bounds:\n        x: 376\n        y: 180\n        width: 80\n        height: 16\n    - component_type: text\n      id: text_quote_number_value\n      label: ***********\n      bounds:\n        x: 376\n        y: 198\n        width: 80\n        height: 16\n    - component_type: text\n      id: text_insured_label\n      label: Insured\n      bounds:\n        x: 472\n        y: 180\n        width: 80\n        height: 16\n    - component_type: link\n      id: link_insured_name\n      label: Landon Cassidy\n      bounds:\n        x: 472\n        y: 198\n        width: 90\n        height: 16\n    - component_type: text\n      id: text_product_label\n      label: Product\n      bounds:\n        x: 578\n        y: 180\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_product_value\n      label: Voluntary Homeowners (HO3)\n      bounds:\n        x: 578\n        y: 198\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_sub_type_label\n      label: Sub Type\n      bounds:\n        x: 744\n        y: 180\n        width: 50\n        height: 16\n    - component_type: text\n      id: text_sub_type_value\n      label: HO3\n      bounds:\n        x: 744\n        y: 198\n        width: 30\n        height: 16\n    - component_type: text\n      id: text_policy_term_label\n      label: Policy Term\n      bounds:\n        x: 810\n        y: 180\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_policy_term_value\n      label: 06/20/2025 - 06/20/2026\n      bounds:\n        x: 810\n        y: 198\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_producer_label\n      label: Producer\n      bounds:\n        x: 976\n        y: 180\n        width: 120\n        height: 16\n    - component_type: link\n      id: link_producer_name\n      label: HH Insurance Group, LLC\n      bounds:\n        x: 976\n        y: 198\n        width: 120\n        height: 16\n    - component_type: text\n      id: text_status_label\n      label: Status\n      bounds:\n        x: 1112\n        y: 180\n        width: 60\n        height: 16\n    - component_type: text\n      id: text_status_value\n      label: In Process\n      bounds:\n        x: 1112\n        y: 198\n        width: 60\n        height: 16\n    - component_type: text\n      id: text_premium_fees_label\n      label: Premium + Fees\n      bounds:\n        x: 1188\n        y: 180\n        width: 90\n        height: 16\n    - component_type: text\n      id: text_premium_fees_value\n      label: $17,776.90\n      bounds:\n        x: 1188\n        y: 198\n        width: 70\n        height: 16\n  - component_type: container\n    id: policy_general_form\n    bounds:\n      x: 256\n      y: 249\n      width: 1632\n      height: 831\n    subcomponents:\n    - component_type: link\n      id: link_return_to_home\n      label: < Return to Home\n      bounds:\n        x: 280\n        y: 260\n        width: 100\n        height: 20\n    - component_type: button\n      id: btn_next_page\n      label: NEXT PAGE\n      bounds:\n        x: 1290\n        y: 220\n        width: 90\n        height: 28\n    - component_type: button\n      id: btn_save\n      label: SAVE\n      bounds:\n        x: 1390\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_copy\n      label: COPY\n      bounds:\n        x: 1460\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_print\n      label: PRINT\n      bounds:\n        x: 1530\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_create_application\n      label: CREATE APPLICATION\n      bounds:\n        x: 1600\n        y: 220\n        width: 150\n        height: 28\n    - component_type: button\n      id: btn_discard_changes\n      label: DISCARD CHANGES\n      bounds:\n        x: 1760\n        y: 220\n        width: 130\n        height: 28\n    - component_type: button\n      id: btn_view_notes\n      label: VIEW NOTES\n      bounds:\n        x: 1290\n        y: 250\n        width: 100\n        height: 28\n    - component_type: button\n      id: btn_delete\n      label: DELETE\n      bounds:\n        x: 1400\n        y: 250\n        width: 70\n        height: 28\n    - component_type: button\n      id: btn_more\n      label: '... MORE'\n      bounds:\n        x: 1480\n        y: 250\n        width: 70\n        height: 28\n    - component_type: text\n      id: title_prior_carrier\n      label: Prior Carrier Details\n      bounds:\n        x: 280\n        y: 292\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_prior_carrier\n      label: Prior Carrier*\n      value: New Purchase\n      bounds:\n        x: 280\n        y: 324\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_prior_policy_expiration\n      label: Prior Policy Expiration Date\n      bounds:\n        x: 700\n        y: 324\n        width: 200\n        height: 36\n    - component_type: text\n      id: title_insured_info\n      label: Insured Information\n      bounds:\n        x: 280\n        y: 376\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_entity_type\n      label: Entity Type*\n      value: Individual\n      bounds:\n        x: 280\n        y: 408\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_first_name\n      label: First*\n      value: Landon\n      bounds:\n        x: 500\n        y: 408\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_middle_name\n      label: Middle\n      bounds:\n        x: 670\n        y: 408\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_last_name\n      label: Last*\n      value: Cassidy\n      bounds:\n        x: 840\n        y: 408\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_suffix\n      label: Suffix\n      bounds:\n        x: 1010\n        y: 408\n        width: 100\n        height: 36\n    - component_type: input\n      id: input_dob\n      label: DOB*\n      value: 05/20/1998\n      bounds:\n        x: 280\n        y: 460\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_insurance_score\n      label: Insurance Score*\n      value: Excellent (850-999)\n      bounds:\n        x: 500\n        y: 460\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_search_name\n      label: Search Name*\n      value: Landon Cassidy\n      bounds:\n        x: 280\n        y: 512\n        width: 200\n        height: 36\n    - component_type: link\n      id: link_reset\n      label: Reset\n      bounds:\n        x: 490\n        y: 520\n        width: 40\n        height: 20\n    - component_type: dropdown\n      id: dropdown_primary_phone\n      label: Primary Phone\n      value: Select...\n      bounds:\n        x: 280\n        y: 564\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_email\n      label: Email\n      bounds:\n        x: 500\n        y: 564\n        width: 200\n        height: 36\n    - component_type: checkbox\n      id: checkbox_no_email\n      label: No Email\n      bounds:\n        x: 720\n        y: 572\n        width: 80\n        height: 20\n    - component_type: text\n      id: title_dwelling_info\n      label: Dwelling Information\n      bounds:\n        x: 280\n        y: 616\n        width: 150\n        height: 20\n    - component_type: input\n      id: input_lookup_address\n      label: Lookup Address\n      bounds:\n        x: 280\n        y: 648\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_number\n      label: Number*\n      value: '4227'\n      bounds:\n        x: 500\n        y: 648\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_direction\n      label: Direction\n      bounds:\n        x: 600\n        y: 648\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_street\n      label: Street*\n      value: 5th\n      bounds:\n        x: 700\n        y: 648\n        width: 100\n        height: 36\n    - component_type: dropdown\n      id: dropdown_suffix\n      label: Suffix\n      value: Ave\n      bounds:\n        x: 820\n        y: 648\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_post_dir\n      label: Post Dir\n      value: S\n      bounds:\n        x: 920\n        y: 648\n        width: 80\n        height: 36\n    - component_type: dropdown\n      id: dropdown_type\n      label: Type\n      bounds:\n        x: 1020\n        y: 648\n        width: 80\n        height: 36\n    - component_type: checkbox\n      id: checkbox_ignore_address_validation\n      label: Ignore Address Validation\n      bounds:\n        x: 1120\n        y: 632\n        width: 150\n        height: 20\n    - component_type: input\n      id: input_number_2\n      label: Number\n      bounds:\n        x: 1120\n        y: 648\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_city\n      label: City*\n      value: St Petersburg\n      bounds:\n        x: 280\n        y: 700\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_county\n      label: County*\n      value: Pinellas\n      bounds:\n        x: 450\n        y: 700\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_state\n      label: State*\n      value: Florida\n      bounds:\n        x: 620\n        y: 700\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_zip\n      label: Zip*\n      value: 33711-1522\n      bounds:\n        x: 790\n        y: 700\n        width: 100\n        height: 36\n    - component_type: text\n      id: text_address_verified\n      label: Address Verified\n      bounds:\n        x: 910\n        y: 708\n        width: 100\n        height: 20\n    - component_type: link\n      id: link_view_map\n      label: View Map\n      bounds:\n        x: 1020\n        y: 708\n        width: 60\n        height: 20\n    - component_type: input\n      id: input_latitude\n      label: Latitude*\n      value: '27.766685'\n      bounds:\n        x: 280\n        y: 752\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_longitude\n      label: Longitude*\n      value: \"-82.690887\"\n      bounds:\n        x: 450\n        y: 752\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_construction_type\n      label: Construction Type*\n      value: Masonry\n      bounds:\n        x: 280\n        y: 804\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_occupancy\n      label: Occupancy*\n      value: Owner Occupied\n      bounds:\n        x: 450\n        y: 804\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_months_occupied\n      label: Months Occupied*\n      value: 0 to 12 Months\n      bounds:\n        x: 620\n        y: 804\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_resided_less_than_2_years\n      label: Has the Insured resided at the risk address for less than 2 years?*\n      value: 'Yes'\n      bounds:\n        x: 280\n        y: 856\n        width: 150\n        height: 36\n    - component_type: text\n      id: title_prior_address\n      label: Prior Address\n      bounds:\n        x: 280\n        y: 908\n        width: 100\n        height: 20\n    - component_type: input\n      id: input_prior_address_number\n      label: Number*\n      value: '18001'\n      bounds:\n        x: 280\n        y: 940\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_direction\n      label: Direction\n      bounds:\n        x: 380\n        y: 940\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_street\n      label: Street*\n      value: Avalon\n      bounds:\n        x: 480\n        y: 940\n        width: 100\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_address_suffix\n      label: Suffix\n      value: Ln\n      bounds:\n        x: 600\n        y: 940\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_post_dir\n      label: Post Dir\n      bounds:\n        x: 700\n        y: 940\n        width: 80\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_address_type\n      label: Type\n      bounds:\n        x: 800\n        y: 940\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_number_2\n      label: Number\n      bounds:\n        x: 900\n        y: 940\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_city\n      label: City*\n      value: Tampa\n      bounds:\n        x: 280\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_county\n      label: County*\n      value: Hillsborough\n      bounds:\n        x: 450\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_state\n      label: State*\n      value: Florida\n      bounds:\n        x: 620\n        y: 992\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_prior_zip\n      label: Zip*\n      value: 33647-3102\n      bounds:\n        x: 790\n        y: 992\n        width: 100\n        height: 36\n    - component_type: link\n      id: link_verify_address\n      label: Verify Address\n      bounds:\n        x: 910\n        y: 1000\n        width: 80\n        height: 20\n    - component_type: text\n      id: text_prior_address_verified\n      label: Address Verified\n      bounds:\n        x: 1000\n        y: 1000\n        width: 100\n        height: 20\n    - component_type: link\n      id: link_view_map_prior\n      label: View Map\n      bounds:\n        x: 1110\n        y: 1000\n        width: 60\n        height: 20\n    - component_type: button\n      id: btn_next_page_bottom\n      label: Next Page\n      bounds:\n        x: 280\n        y: 1044\n        width: 80\n        height: 36\n  footer:\n  - component_type: container\n    id: main_footer\n    bounds:\n      x: 0\n      y: 1050\n      width: 1920\n      height: 30\n    subcomponents:\n    - component_type: text\n      id: text_powered_by\n      label: Powered by GUIDEWIRE\n      bounds:\n        x: 280\n        y: 1058\n        width: 150\n        height: 20\n    - component_type: text\n      id: text_environment\n      label: 'Environment : PROD AIIG'\n      bounds:\n        x: 1200\n        y: 1058\n        width: 150\n        height: 20\n    - component_type: text\n      id: text_current_logon\n      label: 'Current Logon : AG8529A (Sign Out)'\n      bounds:\n        x: 1400\n        y: 1058\n        width: 200\n        height: 20\n    - component_type: text\n      id: text_posting_date\n      label: 'Posting Date : 05/29/2025'\n      bounds:\n        x: 1650\n        y: 1058\n        width: 150\n        height: 20\n```"]}, {"role": "model", "parts": ["```yaml\nbrowser_component:\n  tab_title: *********** - Guidewire PolicyCenter\n  url: ai.iscs.com/innovation\n  address_bar_focused: false\n  tab_state: idle\nwebpage:\n  header:\n  - component_type: container\n    id: main_header\n    bounds:\n      x: 0\n      y: 69\n      width: 1920\n      height: 61\n    subcomponents:\n    - component_type: image\n      id: logo_american_integrity\n      label: AMERICAN INTEGRITY\n      bounds:\n        x: 16\n        y: 116\n        width: 128\n        height: 40\n    - component_type: navigation_menu\n      id: top_navigation\n      bounds:\n        x: 1008\n        y: 116\n        width: 882\n        height: 36\n      subcomponents:\n      - component_type: link\n        id: link_home\n        label: Home\n        bounds:\n          x: 1298\n          y: 126\n          width: 40\n          height: 20\n      - component_type: link\n        id: link_quote_policy\n        label: Quote/Policy\n        state: active\n        bounds:\n          x: 1362\n          y: 126\n          width: 80\n          height: 20\n      - component_type: link\n        id: link_claims\n        label: Claims\n        bounds:\n          x: 1466\n          y: 126\n          width: 48\n          height: 20\n      - component_type: link\n        id: link_cabinets\n        label: Cabinets\n        bounds:\n          x: 1538\n          y: 126\n          width: 58\n          height: 20\n      - component_type: link\n        id: link_support\n        label: Support\n        bounds:\n          x: 1620\n          y: 126\n          width: 54\n          height: 20\n  sidebar:\n  - component_type: navigation_menu\n    id: left_navigation\n    bounds:\n      x: 0\n      y: 169\n      width: 256\n      height: 809\n    subcomponents:\n    - component_type: search_input\n      id: search_sidebar\n      label: Search\n      bounds:\n        x: 16\n        y: 180\n        width: 188\n        height: 36\n    - component_type: button_icon\n      id: btn_search\n      label: Search\n      bounds:\n        x: 204\n        y: 180\n        width: 36\n        height: 36\n    - component_type: text\n      id: text_advanced_search\n      label: 'ADVANCED SEARCH:'\n      bounds:\n        x: 16\n        y: 224\n        width: 100\n        height: 16\n    - component_type: link\n      id: link_policy_search\n      label: POLICY\n      bounds:\n        x: 120\n        y: 224\n        width: 40\n        height: 16\n    - component_type: link\n      id: link_claims_search\n      label: CLAIMS\n      bounds:\n        x: 170\n        y: 224\n        width: 45\n        height: 16\n    - component_type: dropdown\n      id: dropdown_quote\n      label: Quote\n      state: expanded\n      bounds:\n        x: 16\n        y: 252\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy\n      label: Policy\n      state: active\n      bounds:\n        x: 32\n        y: 284\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_dwelling\n      label: Dwelling\n      bounds:\n        x: 32\n        y: 316\n        width: 208\n        height: 32\n      subcomponents:\n      - component_type: badge\n        id: badge_dwelling_count\n        label: '2'\n        bounds:\n          x: 210\n          y: 322\n          width: 16\n          height: 20\n    - component_type: link\n      id: link_review\n      label: Review\n      bounds:\n        x: 32\n        y: 348\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_attachments\n      label: Attachments\n      bounds:\n        x: 16\n        y: 380\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_correspondence\n      label: Correspondence\n      bounds:\n        x: 16\n        y: 412\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_tasks\n      label: Tasks\n      bounds:\n        x: 16\n        y: 444\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_notes\n      label: Notes\n      bounds:\n        x: 16\n        y: 476\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy_file\n      label: Policy File\n      bounds:\n        x: 16\n        y: 508\n        width: 224\n        height: 32\n  - component_type: action_bar\n    id: right_action_bar\n    bounds:\n      x: 1888\n      y: 169\n      width: 32\n      height: 809\n    subcomponents:\n    - component_type: button_icon\n      id: btn_summary\n      label: SUMMARY\n      bounds:\n        x: 1888\n        y: 180\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_quick_qt\n      label: WTCROFT QUICK QT\n      bounds:\n        x: 1888\n        y: 230\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_quote\n      label: NEW QUOTE\n      bounds:\n        x: 1888\n        y: 280\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_note\n      label: NEW NOTE\n      bounds:\n        x: 1888\n        y: 330\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_attach\n      label: NEW ATTACH...\n      bounds:\n        x: 1888\n        y: 380\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_task\n      label: NEW TASK\n      bounds:\n        x: 1888\n        y: 430\n        width: 32\n        height: 50\n  main_content:\n  - component_type: container\n    id: quote_summary_header\n    bounds:\n      x: 256\n      y: 169\n      width: 1632\n      height: 80\n    subcomponents:\n    - component_type: button\n      id: btn_quote\n      label: QUOTE\n      bounds:\n        x: 280\n        y: 180\n        width: 80\n        height: 36\n    - component_type: text\n      id: text_quote_number_label\n      label: Quote Number\n      bounds:\n        x: 376\n        y: 180\n        width: 80\n        height: 16\n    - component_type: text\n      id: text_quote_number_value\n      label: ***********\n      bounds:\n        x: 376\n        y: 198\n        width: 80\n        height: 16\n    - component_type: text\n      id: text_insured_label\n      label: Insured\n      bounds:\n        x: 472\n        y: 180\n        width: 80\n        height: 16\n    - component_type: link\n      id: link_insured_name\n      label: Landon Cassidy\n      bounds:\n        x: 472\n        y: 198\n        width: 90\n        height: 16\n    - component_type: text\n      id: text_product_label\n      label: Product\n      bounds:\n        x: 578\n        y: 180\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_product_value\n      label: Voluntary Homeowners (HO3)\n      bounds:\n        x: 578\n        y: 198\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_sub_type_label\n      label: Sub Type\n      bounds:\n        x: 744\n        y: 180\n        width: 50\n        height: 16\n    - component_type: text\n      id: text_sub_type_value\n      label: HO3\n      bounds:\n        x: 744\n        y: 198\n        width: 30\n        height: 16\n    - component_type: text\n      id: text_policy_term_label\n      label: Policy Term\n      bounds:\n        x: 810\n        y: 180\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_policy_term_value\n      label: 06/20/2025 - 06/20/2026\n      bounds:\n        x: 810\n        y: 198\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_producer_label\n      label: Producer\n      bounds:\n        x: 976\n        y: 180\n        width: 120\n        height: 16\n    - component_type: link\n      id: link_producer_name\n      label: HH Insurance Group, LLC\n      bounds:\n        x: 976\n        y: 198\n        width: 120\n        height: 16\n    - component_type: text\n      id: text_status_label\n      label: Status\n      bounds:\n        x: 1112\n        y: 180\n        width: 60\n        height: 16\n    - component_type: text\n      id: text_status_value\n      label: In Process\n      bounds:\n        x: 1112\n        y: 198\n        width: 60\n        height: 16\n    - component_type: text\n      id: text_premium_fees_label\n      label: Premium + Fees\n      bounds:\n        x: 1188\n        y: 180\n        width: 90\n        height: 16\n    - component_type: text\n      id: text_premium_fees_value\n      label: $17,776.90\n      bounds:\n        x: 1188\n        y: 198\n        width: 70\n        height: 16\n  - component_type: container\n    id: policy_general_form\n    bounds:\n      x: 256\n      y: 249\n      width: 1632\n      height: 831\n    subcomponents:\n    - component_type: link\n      id: link_return_to_home\n      label: < Return to Home\n      bounds:\n        x: 280\n        y: 260\n        width: 100\n        height: 20\n    - component_type: button\n      id: btn_next_page\n      label: NEXT PAGE\n      bounds:\n        x: 1290\n        y: 220\n        width: 90\n        height: 28\n    - component_type: button\n      id: btn_save\n      label: SAVE\n      bounds:\n        x: 1390\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_copy\n      label: COPY\n      bounds:\n        x: 1460\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_print\n      label: PRINT\n      bounds:\n        x: 1530\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_create_application\n      label: CREATE APPLICATION\n      bounds:\n        x: 1600\n        y: 220\n        width: 150\n        height: 28\n    - component_type: button\n      id: btn_discard_changes\n      label: DISCARD CHANGES\n      bounds:\n        x: 1760\n        y: 220\n        width: 130\n        height: 28\n    - component_type: button\n      id: btn_view_notes\n      label: VIEW NOTES\n      bounds:\n        x: 1290\n        y: 250\n        width: 100\n        height: 28\n    - component_type: button\n      id: btn_delete\n      label: DELETE\n      bounds:\n        x: 1400\n        y: 250\n        width: 70\n        height: 28\n    - component_type: button\n      id: btn_more\n      label: '... MORE'\n      bounds:\n        x: 1480\n        y: 250\n        width: 70\n        height: 28\n    - component_type: text\n      id: title_prior_carrier\n      label: Prior Carrier Details\n      bounds:\n        x: 280\n        y: 292\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_prior_carrier\n      label: Prior Carrier*\n      value: New Purchase\n      bounds:\n        x: 280\n        y: 324\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_prior_policy_expiration\n      label: Prior Policy Expiration Date\n      bounds:\n        x: 700\n        y: 324\n        width: 200\n        height: 36\n    - component_type: text\n      id: title_insured_info\n      label: Insured Information\n      bounds:\n        x: 280\n        y: 376\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_entity_type\n      label: Entity Type*\n      value: Individual\n      bounds:\n        x: 280\n        y: 408\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_first_name\n      label: First*\n      value: Landon\n      bounds:\n        x: 500\n        y: 408\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_middle_name\n      label: Middle\n      bounds:\n        x: 670\n        y: 408\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_last_name\n      label: Last*\n      value: Cassidy\n      bounds:\n        x: 840\n        y: 408\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_suffix\n      label: Suffix\n      bounds:\n        x: 1010\n        y: 408\n        width: 100\n        height: 36\n    - component_type: input\n      id: input_dob\n      label: DOB*\n      value: 05/20/1988\n      bounds:\n        x: 280\n        y: 460\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_insurance_score\n      label: Insurance Score*\n      value: Excellent (850-999)\n      bounds:\n        x: 500\n        y: 460\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_search_name\n      label: Search Name*\n      value: Landon Cassidy\n      bounds:\n        x: 280\n        y: 512\n        width: 200\n        height: 36\n    - component_type: link\n      id: link_reset\n      label: Reset\n      bounds:\n        x: 490\n        y: 520\n        width: 40\n        height: 20\n    - component_type: dropdown\n      id: dropdown_primary_phone\n      label: Primary Phone\n      value: Select...\n      bounds:\n        x: 280\n        y: 564\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_email\n      label: Email\n      bounds:\n        x: 500\n        y: 564\n        width: 200\n        height: 36\n    - component_type: checkbox\n      id: checkbox_no_email\n      label: No Email\n      bounds:\n        x: 720\n        y: 572\n        width: 80\n        height: 20\n    - component_type: text\n      id: title_dwelling_info\n      label: Dwelling Information\n      bounds:\n        x: 280\n        y: 616\n        width: 150\n        height: 20\n    - component_type: input\n      id: input_lookup_address\n      label: Lookup Address\n      bounds:\n        x: 280\n        y: 648\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_number\n      label: Number*\n      value: '4227'\n      bounds:\n        x: 500\n        y: 648\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_direction\n      label: Direction\n      bounds:\n        x: 600\n        y: 648\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_street\n      label: Street*\n      value: 5th\n      bounds:\n        x: 700\n        y: 648\n        width: 100\n        height: 36\n    - component_type: dropdown\n      id: dropdown_suffix\n      label: Suffix\n      value: Ave\n      bounds:\n        x: 820\n        y: 648\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_post_dir\n      label: Post Dir\n      value: S\n      bounds:\n        x: 920\n        y: 648\n        width: 80\n        height: 36\n    - component_type: dropdown\n      id: dropdown_type\n      label: Type\n      bounds:\n        x: 1020\n        y: 648\n        width: 80\n        height: 36\n    - component_type: checkbox\n      id: checkbox_ignore_address_validation\n      label: Ignore Address Validation\n      bounds:\n        x: 1120\n        y: 632\n        width: 150\n        height: 20\n    - component_type: input\n      id: input_number_2\n      label: Number\n      bounds:\n        x: 1120\n        y: 648\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_city\n      label: City*\n      value: St Petersburg\n      bounds:\n        x: 280\n        y: 700\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_county\n      label: County*\n      value: Pinellas\n      bounds:\n        x: 450\n        y: 700\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_state\n      label: State*\n      value: Florida\n      bounds:\n        x: 620\n        y: 700\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_zip\n      label: Zip*\n      value: 33711-1522\n      bounds:\n        x: 790\n        y: 700\n        width: 100\n        height: 36\n    - component_type: text\n      id: text_address_verified\n      label: Address Verified\n      bounds:\n        x: 910\n        y: 708\n        width: 100\n        height: 20\n    - component_type: link\n      id: link_view_map\n      label: View Map\n      bounds:\n        x: 1020\n        y: 708\n        width: 60\n        height: 20\n    - component_type: input\n      id: input_latitude\n      label: Latitude*\n      value: '27.766685'\n      bounds:\n        x: 280\n        y: 752\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_longitude\n      label: Longitude*\n      value: \"-82.690887\"\n      bounds:\n        x: 450\n        y: 752\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_construction_type\n      label: Construction Type*\n      value: Masonry\n      bounds:\n        x: 280\n        y: 804\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_occupancy\n      label: Occupancy*\n      value: Owner Occupied\n      bounds:\n        x: 450\n        y: 804\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_months_occupied\n      label: Months Occupied*\n      value: 0 to 12 Months\n      bounds:\n        x: 620\n        y: 804\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_resided_less_than_2_years\n      label: Has the Insured resided at the risk address for less than 2 years?*\n      value: 'Yes'\n      bounds:\n        x: 280\n        y: 856\n        width: 150\n        height: 36\n    - component_type: text\n      id: title_prior_address\n      label: Prior Address\n      bounds:\n        x: 280\n        y: 908\n        width: 100\n        height: 20\n    - component_type: input\n      id: input_prior_address_number\n      label: Number*\n      value: '18001'\n      bounds:\n        x: 280\n        y: 940\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_direction\n      label: Direction\n      bounds:\n        x: 380\n        y: 940\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_street\n      label: Street*\n      value: Avalon\n      bounds:\n        x: 480\n        y: 940\n        width: 100\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_address_suffix\n      label: Suffix\n      value: Ln\n      bounds:\n        x: 600\n        y: 940\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_post_dir\n      label: Post Dir\n      bounds:\n        x: 700\n        y: 940\n        width: 80\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_address_type\n      label: Type\n      bounds:\n        x: 800\n        y: 940\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_number_2\n      label: Number\n      bounds:\n        x: 900\n        y: 940\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_city\n      label: City*\n      value: Tampa\n      bounds:\n        x: 280\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_county\n      label: County*\n      value: Hillsborough\n      bounds:\n        x: 450\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_state\n      label: State*\n      value: Florida\n      bounds:\n        x: 620\n        y: 992\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_prior_zip\n      label: Zip*\n      value: 33647-3102\n      bounds:\n        x: 790\n        y: 992\n        width: 100\n        height: 36\n    - component_type: link\n      id: link_verify_address\n      label: Verify Address\n      bounds:\n        x: 910\n        y: 1000\n        width: 80\n        height: 20\n    - component_type: text\n      id: text_prior_address_verified\n      label: Address Verified\n      bounds:\n        x: 1000\n        y: 1000\n        width: 100\n        height: 20\n    - component_type: link\n      id: link_view_map_prior\n      label: View Map\n      bounds:\n        x: 1110\n        y: 1000\n        width: 60\n        height: 20\n    - component_type: button\n      id: btn_next_page_bottom\n      label: Next Page\n      bounds:\n        x: 280\n        y: 1044\n        width: 80\n        height: 36\n  footer:\n  - component_type: container\n    id: main_footer\n    bounds:\n      x: 0\n      y: 1050\n      width: 1920\n      height: 30\n    subcomponents:\n    - component_type: text\n      id: text_powered_by\n      label: Powered by GUIDEWIRE\n      bounds:\n        x: 280\n        y: 1058\n        width: 150\n        height: 20\n    - component_type: text\n      id: text_environment\n      label: 'Environment : PROD AIIG'\n      bounds:\n        x: 1200\n        y: 1058\n        width: 150\n        height: 20\n    - component_type: text\n      id: text_current_logon\n      label: 'Current Logon : AG8529A (Sign Out)'\n      bounds:\n        x: 1400\n        y: 1058\n        width: 200\n        height: 20\n    - component_type: text\n      id: text_posting_date\n      label: 'Posting Date : 05/29/2025'\n      bounds:\n        x: 1650\n        y: 1058\n        width: 150\n        height: 20\n```"]}, {"role": "model", "parts": ["```yaml\nbrowser_component:\n  tab_title: *********** - Guide...\n  url: ai.iscs.com/innovation\n  address_bar_focused: false\n  tab_state: idle\nwebpage:\n  header:\n  - component_type: container\n    id: main_header\n    bounds:\n      x: 0\n      y: 69\n      width: 1920\n      height: 61\n    subcomponents:\n    - component_type: image\n      id: logo_american_integrity\n      label: AMERICAN INTEGRITY\n      bounds:\n        x: 16\n        y: 116\n        width: 128\n        height: 40\n    - component_type: navigation_menu\n      id: top_navigation\n      bounds:\n        x: 1290\n        y: 116\n        width: 600\n        height: 36\n      subcomponents:\n      - component_type: link\n        id: link_home\n        label: Home\n        bounds:\n          x: 1298\n          y: 126\n          width: 40\n          height: 20\n      - component_type: link\n        id: link_quote_policy\n        label: Quote/Policy\n        state: active\n        bounds:\n          x: 1362\n          y: 126\n          width: 80\n          height: 20\n      - component_type: link\n        id: link_claims\n        label: Claims\n        bounds:\n          x: 1466\n          y: 126\n          width: 48\n          height: 20\n      - component_type: link\n        id: link_cabinets\n        label: Cabinets\n        bounds:\n          x: 1538\n          y: 126\n          width: 58\n          height: 20\n      - component_type: link\n        id: link_support\n        label: Support\n        bounds:\n          x: 1620\n          y: 126\n          width: 54\n          height: 20\n  sidebar:\n  - component_type: navigation_menu\n    id: left_navigation\n    bounds:\n      x: 0\n      y: 169\n      width: 256\n      height: 809\n    subcomponents:\n    - component_type: search_input\n      id: search_sidebar\n      label: Search\n      bounds:\n        x: 16\n        y: 180\n        width: 188\n        height: 36\n    - component_type: button_icon\n      id: btn_search\n      label: Search\n      bounds:\n        x: 204\n        y: 180\n        width: 36\n        height: 36\n    - component_type: text\n      id: text_advanced_search\n      label: 'ADVANCED SEARCH:'\n      bounds:\n        x: 16\n        y: 224\n        width: 100\n        height: 16\n    - component_type: link\n      id: link_policy_search\n      label: POLICY\n      bounds:\n        x: 120\n        y: 224\n        width: 40\n        height: 16\n    - component_type: link\n      id: link_claims_search\n      label: CLAIMS\n      bounds:\n        x: 170\n        y: 224\n        width: 45\n        height: 16\n    - component_type: dropdown\n      id: dropdown_quote\n      label: Quote\n      state: expanded\n      bounds:\n        x: 16\n        y: 252\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy\n      label: Policy\n      state: active\n      bounds:\n        x: 32\n        y: 284\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_dwelling\n      label: Dwelling\n      bounds:\n        x: 32\n        y: 316\n        width: 208\n        height: 32\n      subcomponents:\n      - component_type: badge\n        id: badge_dwelling_count\n        label: '2'\n        bounds:\n          x: 210\n          y: 322\n          width: 16\n          height: 20\n    - component_type: link\n      id: link_review\n      label: Review\n      bounds:\n        x: 32\n        y: 348\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_attachments\n      label: Attachments\n      bounds:\n        x: 16\n        y: 380\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_correspondence\n      label: Correspondence\n      bounds:\n        x: 16\n        y: 412\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_tasks\n      label: Tasks\n      bounds:\n        x: 16\n        y: 444\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_notes\n      label: Notes\n      bounds:\n        x: 16\n        y: 476\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy_file\n      label: Policy File\n      bounds:\n        x: 16\n        y: 508\n        width: 224\n        height: 32\n  - component_type: action_bar\n    id: right_action_bar\n    bounds:\n      x: 1888\n      y: 169\n      width: 32\n      height: 809\n    subcomponents:\n    - component_type: button_icon\n      id: btn_summary\n      label: SUMMARY\n      bounds:\n        x: 1888\n        y: 180\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_quick_qt\n      label: WTCROFT QUICK QT\n      bounds:\n        x: 1888\n        y: 230\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_quote\n      label: NEW QUOTE\n      bounds:\n        x: 1888\n        y: 280\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_note\n      label: NEW NOTE\n      bounds:\n        x: 1888\n        y: 330\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_attach\n      label: NEW ATTACH...\n      bounds:\n        x: 1888\n        y: 380\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_task\n      label: NEW TASK\n      bounds:\n        x: 1888\n        y: 430\n        width: 32\n        height: 50\n  main_content:\n  - component_type: container\n    id: quote_summary_header\n    bounds:\n      x: 256\n      y: 169\n      width: 1632\n      height: 80\n    subcomponents:\n    - component_type: button\n      id: btn_quote\n      label: QUOTE\n      bounds:\n        x: 280\n        y: 180\n        width: 80\n        height: 36\n    - component_type: text\n      id: text_quote_number_label\n      label: Quote Number\n      bounds:\n        x: 376\n        y: 180\n        width: 80\n        height: 16\n    - component_type: text\n      id: text_quote_number_value\n      label: ***********\n      bounds:\n        x: 376\n        y: 198\n        width: 80\n        height: 16\n    - component_type: text\n      id: text_insured_label\n      label: Insured\n      bounds:\n        x: 472\n        y: 180\n        width: 80\n        height: 16\n    - component_type: link\n      id: link_insured_name\n      label: Landon Cassidy\n      bounds:\n        x: 472\n        y: 198\n        width: 90\n        height: 16\n    - component_type: text\n      id: text_product_label\n      label: Product\n      bounds:\n        x: 578\n        y: 180\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_product_value\n      label: Voluntary Homeowners (HO3)\n      bounds:\n        x: 578\n        y: 198\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_sub_type_label\n      label: Sub Type\n      bounds:\n        x: 744\n        y: 180\n        width: 50\n        height: 16\n    - component_type: text\n      id: text_sub_type_value\n      label: HO3\n      bounds:\n        x: 744\n        y: 198\n        width: 30\n        height: 16\n    - component_type: text\n      id: text_policy_term_label\n      label: Policy Term\n      bounds:\n        x: 810\n        y: 180\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_policy_term_value\n      label: 06/20/2025 - 06/20/2026\n      bounds:\n        x: 810\n        y: 198\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_producer_label\n      label: Producer\n      bounds:\n        x: 976\n        y: 180\n        width: 120\n        height: 16\n    - component_type: link\n      id: link_producer_name\n      label: HH Insurance Group, LLC\n      bounds:\n        x: 976\n        y: 198\n        width: 120\n        height: 16\n    - component_type: text\n      id: text_status_label\n      label: Status\n      bounds:\n        x: 1112\n        y: 180\n        width: 60\n        height: 16\n    - component_type: text\n      id: text_status_value\n      label: In Process\n      bounds:\n        x: 1112\n        y: 198\n        width: 60\n        height: 16\n    - component_type: text\n      id: text_premium_fees_label\n      label: Premium + Fees\n      bounds:\n        x: 1188\n        y: 180\n        width: 90\n        height: 16\n    - component_type: text\n      id: text_premium_fees_value\n      label: $17,776.90\n      bounds:\n        x: 1188\n        y: 198\n        width: 70\n        height: 16\n  - component_type: container\n    id: policy_general_form\n    bounds:\n      x: 256\n      y: 249\n      width: 1632\n      height: 831\n    subcomponents:\n    - component_type: link\n      id: link_return_to_home\n      label: < Return to Home\n      bounds:\n        x: 280\n        y: 260\n        width: 100\n        height: 20\n    - component_type: button\n      id: btn_next_page\n      label: NEXT PAGE\n      bounds:\n        x: 1290\n        y: 220\n        width: 90\n        height: 28\n    - component_type: button\n      id: btn_save\n      label: SAVE\n      bounds:\n        x: 1390\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_copy\n      label: COPY\n      bounds:\n        x: 1460\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_print\n      label: PRINT\n      bounds:\n        x: 1530\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_create_application\n      label: CREATE APPLICATION\n      bounds:\n        x: 1600\n        y: 220\n        width: 150\n        height: 28\n    - component_type: button\n      id: btn_discard_changes\n      label: DISCARD CHANGES\n      bounds:\n        x: 1760\n        y: 220\n        width: 130\n        height: 28\n    - component_type: button\n      id: btn_view_notes\n      label: VIEW NOTES\n      bounds:\n        x: 1290\n        y: 250\n        width: 100\n        height: 28\n    - component_type: button\n      id: btn_delete\n      label: DELETE\n      bounds:\n        x: 1400\n        y: 250\n        width: 70\n        height: 28\n    - component_type: button\n      id: btn_more\n      label: '... MORE'\n      bounds:\n        x: 1480\n        y: 250\n        width: 70\n        height: 28\n    - component_type: input\n      id: input_producer_code\n      label: 'Producer: Code*'\n      value: AG8529A1\n      bounds:\n        x: 280\n        y: 292\n        width: 150\n        height: 36\n    - component_type: text\n      id: text_producer_name\n      label: HH Insurance Group, LLC\n      bounds:\n        x: 470\n        y: 302\n        width: 150\n        height: 20\n    - component_type: text\n      id: title_prior_carrier\n      label: Prior Carrier Details\n      bounds:\n        x: 280\n        y: 344\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_prior_carrier\n      label: Prior Carrier*\n      value: New Purchase\n      bounds:\n        x: 280\n        y: 376\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_prior_policy_expiration\n      label: Prior Policy Expiration Date\n      bounds:\n        x: 700\n        y: 376\n        width: 200\n        height: 36\n    - component_type: text\n      id: title_insured_info\n      label: Insured Information\n      bounds:\n        x: 280\n        y: 428\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_entity_type\n      label: Entity Type*\n      value: Individual\n      bounds:\n        x: 280\n        y: 460\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_first_name\n      label: First*\n      value: Landon\n      bounds:\n        x: 500\n        y: 460\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_middle_name\n      label: Middle\n      bounds:\n        x: 670\n        y: 460\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_last_name\n      label: Last*\n      value: Cassidy\n      bounds:\n        x: 840\n        y: 460\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_suffix\n      label: Suffix\n      bounds:\n        x: 1010\n        y: 460\n        width: 100\n        height: 36\n    - component_type: input\n      id: input_dob\n      label: DOB*\n      value: 05/20/1998\n      bounds:\n        x: 280\n        y: 512\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_insurance_score\n      label: Insurance Score*\n      value: Excellent (850-999)\n      bounds:\n        x: 500\n        y: 512\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_search_name\n      label: Search Name*\n      value: Landon Cassidy\n      bounds:\n        x: 280\n        y: 564\n        width: 200\n        height: 36\n    - component_type: link\n      id: link_reset\n      label: Reset\n      bounds:\n        x: 490\n        y: 572\n        width: 40\n        height: 20\n    - component_type: dropdown\n      id: dropdown_primary_phone\n      label: Primary Phone\n      value: Select...\n      bounds:\n        x: 280\n        y: 616\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_email\n      label: Email\n      bounds:\n        x: 500\n        y: 616\n        width: 200\n        height: 36\n    - component_type: checkbox\n      id: checkbox_no_email\n      label: No Email\n      bounds:\n        x: 720\n        y: 624\n        width: 80\n        height: 20\n    - component_type: text\n      id: title_dwelling_info\n      label: Dwelling Information\n      bounds:\n        x: 280\n        y: 668\n        width: 150\n        height: 20\n    - component_type: input\n      id: input_lookup_address\n      label: Lookup Address\n      bounds:\n        x: 280\n        y: 700\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_number\n      label: Number*\n      value: '4227'\n      bounds:\n        x: 500\n        y: 700\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_direction\n      label: Direction\n      bounds:\n        x: 600\n        y: 700\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_street\n      label: Street*\n      value: 5th\n      bounds:\n        x: 700\n        y: 700\n        width: 100\n        height: 36\n    - component_type: dropdown\n      id: dropdown_suffix\n      label: Suffix\n      value: Ave\n      bounds:\n        x: 820\n        y: 700\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_post_dir\n      label: Post Dir\n      value: S\n      bounds:\n        x: 920\n        y: 700\n        width: 80\n        height: 36\n    - component_type: dropdown\n      id: dropdown_type\n      label: Type\n      bounds:\n        x: 1020\n        y: 700\n        width: 80\n        height: 36\n    - component_type: checkbox\n      id: checkbox_ignore_address_validation\n      label: Ignore Address Validation\n      bounds:\n        x: 1120\n        y: 684\n        width: 150\n        height: 20\n    - component_type: input\n      id: input_number_2\n      label: Number\n      bounds:\n        x: 1120\n        y: 700\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_city\n      label: City*\n      value: St Petersburg\n      bounds:\n        x: 280\n        y: 752\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_county\n      label: County*\n      value: Pinellas\n      bounds:\n        x: 450\n        y: 752\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_state\n      label: State*\n      value: Florida\n      bounds:\n        x: 620\n        y: 752\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_zip\n      label: Zip*\n      value: 33711-1522\n      bounds:\n        x: 790\n        y: 752\n        width: 100\n        height: 36\n    - component_type: text\n      id: text_address_verified\n      label: Address Verified\n      bounds:\n        x: 910\n        y: 760\n        width: 100\n        height: 20\n    - component_type: link\n      id: link_view_map\n      label: View Map\n      bounds:\n        x: 1020\n        y: 760\n        width: 60\n        height: 20\n    - component_type: input\n      id: input_latitude\n      label: Latitude*\n      value: '27.766685'\n      bounds:\n        x: 280\n        y: 804\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_longitude\n      label: Longitude*\n      value: \"-82.690887\"\n      bounds:\n        x: 450\n        y: 804\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_construction_type\n      label: Construction Type*\n      value: Masonry\n      bounds:\n        x: 280\n        y: 856\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_occupancy\n      label: Occupancy*\n      value: Owner Occupied\n      bounds:\n        x: 450\n        y: 856\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_months_occupied\n      label: Months Occupied*\n      value: 9 to 12 Months\n      bounds:\n        x: 620\n        y: 856\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_resided_less_than_2_years\n      label: Has the Insured resided at the risk address for less than 2 years?*\n      value: 'Yes'\n      bounds:\n        x: 280\n        y: 908\n        width: 150\n        height: 36\n    - component_type: text\n      id: title_prior_address\n      label: Prior Address\n      bounds:\n        x: 280\n        y: 960\n        width: 100\n        height: 20\n    - component_type: input\n      id: input_prior_address_number\n      label: Number*\n      value: '18001'\n      bounds:\n        x: 280\n        y: 992\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_direction\n      label: Direction\n      bounds:\n        x: 380\n        y: 992\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_street\n      label: Street*\n      value: Avalon\n      bounds:\n        x: 480\n        y: 992\n        width: 100\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_address_suffix\n      label: Suffix\n      value: Ln\n      bounds:\n        x: 600\n        y: 992\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_post_dir\n      label: Post Dir\n      bounds:\n        x: 700\n        y: 992\n        width: 80\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_address_type\n      label: Type\n      bounds:\n        x: 800\n        y: 992\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_number_2\n      label: Number\n      bounds:\n        x: 900\n        y: 992\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_city\n      label: City*\n      value: Tampa\n      bounds:\n        x: 280\n        y: 1044\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_county\n      label: County*\n      value: Hillsborough\n      bounds:\n        x: 450\n        y: 1044\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_state\n      label: State*\n      value: Florida\n      bounds:\n        x: 620\n        y: 1044\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_prior_zip\n      label: Zip*\n      value: 33647-3102\n      bounds:\n        x: 790\n        y: 1044\n        width: 100\n        height: 36\n    - component_type: link\n      id: link_verify_address\n      label: Verify Address\n      bounds:\n        x: 910\n        y: 1052\n        width: 80\n        height: 20\n    - component_type: text\n      id: text_prior_address_verified\n      label: Address Verified\n      bounds:\n        x: 1000\n        y: 1052\n        width: 100\n        height: 20\n    - component_type: link\n      id: link_view_map_prior\n      label: View Map\n      bounds:\n        x: 1110\n        y: 1052\n        width: 60\n        height: 20\n    - component_type: button\n      id: btn_next_page_bottom\n      label: Next Page\n      bounds:\n        x: 280\n        y: 1096\n        width: 80\n        height: 36\n```"]}, {"role": "model", "parts": ["```yaml\nbrowser_component:\n  tab_title: *********** - Guide...\n  url: ai.iscs.com/innovation\n  address_bar_focused: false\n  tab_state: idle\nwebpage:\n  header:\n  - component_type: container\n    id: main_header\n    bounds:\n      x: 0\n      y: 69\n      width: 1920\n      height: 61\n    subcomponents:\n    - component_type: image\n      id: logo_american_integrity\n      label: AMERICAN INTEGRITY\n      bounds:\n        x: 16\n        y: 116\n        width: 128\n        height: 40\n    - component_type: navigation_menu\n      id: top_navigation\n      bounds:\n        x: 1290\n        y: 116\n        width: 600\n        height: 36\n      subcomponents:\n      - component_type: link\n        id: link_home\n        label: Home\n        bounds:\n          x: 1298\n          y: 126\n          width: 40\n          height: 20\n      - component_type: link\n        id: link_quote_policy\n        label: Quote/Policy\n        state: active\n        bounds:\n          x: 1362\n          y: 126\n          width: 80\n          height: 20\n      - component_type: link\n        id: link_claims\n        label: Claims\n        bounds:\n          x: 1466\n          y: 126\n          width: 48\n          height: 20\n      - component_type: link\n        id: link_cabinets\n        label: Cabinets\n        bounds:\n          x: 1538\n          y: 126\n          width: 58\n          height: 20\n      - component_type: link\n        id: link_support\n        label: Support\n        bounds:\n          x: 1620\n          y: 126\n          width: 54\n          height: 20\n  sidebar:\n  - component_type: navigation_menu\n    id: left_navigation\n    bounds:\n      x: 0\n      y: 169\n      width: 256\n      height: 809\n    subcomponents:\n    - component_type: search_input\n      id: search_sidebar\n      label: Search\n      bounds:\n        x: 16\n        y: 180\n        width: 188\n        height: 36\n    - component_type: button_icon\n      id: btn_search\n      label: Search\n      bounds:\n        x: 204\n        y: 180\n        width: 36\n        height: 36\n    - component_type: text\n      id: text_advanced_search\n      label: 'ADVANCED SEARCH:'\n      bounds:\n        x: 16\n        y: 224\n        width: 100\n        height: 16\n    - component_type: link\n      id: link_policy_search\n      label: POLICY\n      bounds:\n        x: 120\n        y: 224\n        width: 40\n        height: 16\n    - component_type: link\n      id: link_claims_search\n      label: CLAIMS\n      bounds:\n        x: 170\n        y: 224\n        width: 45\n        height: 16\n    - component_type: dropdown\n      id: dropdown_quote\n      label: Quote\n      state: expanded\n      bounds:\n        x: 16\n        y: 252\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy\n      label: Policy\n      state: active\n      bounds:\n        x: 32\n        y: 284\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_dwelling\n      label: Dwelling\n      bounds:\n        x: 32\n        y: 316\n        width: 208\n        height: 32\n      subcomponents:\n      - component_type: badge\n        id: badge_dwelling_count\n        label: '2'\n        bounds:\n          x: 210\n          y: 322\n          width: 16\n          height: 20\n    - component_type: link\n      id: link_review\n      label: Review\n      bounds:\n        x: 32\n        y: 348\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_attachments\n      label: Attachments\n      bounds:\n        x: 16\n        y: 380\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_correspondence\n      label: Correspondence\n      bounds:\n        x: 16\n        y: 412\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_tasks\n      label: Tasks\n      bounds:\n        x: 16\n        y: 444\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_notes\n      label: Notes\n      bounds:\n        x: 16\n        y: 476\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy_file\n      label: Policy File\n      bounds:\n        x: 16\n        y: 508\n        width: 224\n        height: 32\n  - component_type: action_bar\n    id: right_action_bar\n    bounds:\n      x: 1888\n      y: 169\n      width: 32\n      height: 809\n    subcomponents:\n    - component_type: button_icon\n      id: btn_summary\n      label: SUMMARY\n      bounds:\n        x: 1888\n        y: 180\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_quick_qt\n      label: WTCROFT QUICK QT\n      bounds:\n        x: 1888\n        y: 230\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_quote\n      label: NEW QUOTE\n      bounds:\n        x: 1888\n        y: 280\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_note\n      label: NEW NOTE\n      bounds:\n        x: 1888\n        y: 330\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_attach\n      label: NEW ATTACH...\n      bounds:\n        x: 1888\n        y: 380\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_task\n      label: NEW TASK\n      bounds:\n        x: 1888\n        y: 430\n        width: 32\n        height: 50\n  main_content:\n  - component_type: container\n    id: quote_summary_header\n    bounds:\n      x: 256\n      y: 169\n      width: 1632\n      height: 80\n    subcomponents:\n    - component_type: button\n      id: btn_quote\n      label: QUOTE\n      bounds:\n        x: 280\n        y: 180\n        width: 80\n        height: 36\n    - component_type: text\n      id: text_quote_number_label\n      label: Quote Number\n      bounds:\n        x: 376\n        y: 180\n        width: 80\n        height: 16\n    - component_type: text\n      id: text_quote_number_value\n      label: ***********\n      bounds:\n        x: 376\n        y: 198\n        width: 80\n        height: 16\n    - component_type: text\n      id: text_insured_label\n      label: Insured\n      bounds:\n        x: 472\n        y: 180\n        width: 80\n        height: 16\n    - component_type: link\n      id: link_insured_name\n      label: Landon Cassidy\n      bounds:\n        x: 472\n        y: 198\n        width: 90\n        height: 16\n    - component_type: text\n      id: text_product_label\n      label: Product\n      bounds:\n        x: 578\n        y: 180\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_product_value\n      label: Voluntary Homeowners (HO3)\n      bounds:\n        x: 578\n        y: 198\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_sub_type_label\n      label: Sub Type\n      bounds:\n        x: 744\n        y: 180\n        width: 50\n        height: 16\n    - component_type: text\n      id: text_sub_type_value\n      label: HO3\n      bounds:\n        x: 744\n        y: 198\n        width: 30\n        height: 16\n    - component_type: text\n      id: text_policy_term_label\n      label: Policy Term\n      bounds:\n        x: 810\n        y: 180\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_policy_term_value\n      label: 06/20/2025 - 06/20/2026\n      bounds:\n        x: 810\n        y: 198\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_producer_label\n      label: Producer\n      bounds:\n        x: 976\n        y: 180\n        width: 120\n        height: 16\n    - component_type: link\n      id: link_producer_name\n      label: HH Insurance Group, LLC\n      bounds:\n        x: 976\n        y: 198\n        width: 120\n        height: 16\n    - component_type: text\n      id: text_status_label\n      label: Status\n      bounds:\n        x: 1112\n        y: 180\n        width: 60\n        height: 16\n    - component_type: text\n      id: text_status_value\n      label: In Process\n      bounds:\n        x: 1112\n        y: 198\n        width: 60\n        height: 16\n    - component_type: text\n      id: text_premium_fees_label\n      label: Premium + Fees\n      bounds:\n        x: 1188\n        y: 180\n        width: 90\n        height: 16\n    - component_type: text\n      id: text_premium_fees_value\n      label: $17,776.90\n      bounds:\n        x: 1188\n        y: 198\n        width: 70\n        height: 16\n  - component_type: container\n    id: policy_general_form\n    bounds:\n      x: 256\n      y: 249\n      width: 1632\n      height: 831\n    subcomponents:\n    - component_type: link\n      id: link_return_to_home\n      label: < Return to Home\n      bounds:\n        x: 280\n        y: 260\n        width: 100\n        height: 20\n    - component_type: button\n      id: btn_next_page\n      label: NEXT PAGE\n      bounds:\n        x: 1290\n        y: 220\n        width: 90\n        height: 28\n    - component_type: button\n      id: btn_save\n      label: SAVE\n      bounds:\n        x: 1390\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_copy\n      label: COPY\n      bounds:\n        x: 1460\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_print\n      label: PRINT\n      bounds:\n        x: 1530\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_create_application\n      label: CREATE APPLICATION\n      bounds:\n        x: 1600\n        y: 220\n        width: 150\n        height: 28\n    - component_type: button\n      id: btn_discard_changes\n      label: DISCARD CHANGES\n      bounds:\n        x: 1760\n        y: 220\n        width: 130\n        height: 28\n    - component_type: button\n      id: btn_view_notes\n      label: VIEW NOTES\n      bounds:\n        x: 1290\n        y: 250\n        width: 100\n        height: 28\n    - component_type: button\n      id: btn_delete\n      label: DELETE\n      bounds:\n        x: 1400\n        y: 250\n        width: 70\n        height: 28\n    - component_type: button\n      id: btn_more\n      label: '... MORE'\n      bounds:\n        x: 1480\n        y: 250\n        width: 70\n        height: 28\n    - component_type: text\n      id: title_policy_general\n      label: Policy General\n      bounds:\n        x: 280\n        y: 292\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_product\n      label: Product*\n      value: Florida - Voluntary Homeowners (HO3) - American Integrity Insurance Group\n      bounds:\n        x: 280\n        y: 324\n        width: 400\n        height: 36\n    - component_type: input\n      id: input_effective_date\n      label: Effective Date*\n      value: 06/20/2025\n      bounds:\n        x: 280\n        y: 376\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_producer_code\n      label: 'Producer: Code*'\n      value: AG8529A1\n      bounds:\n        x: 280\n        y: 428\n        width: 150\n        height: 36\n    - component_type: text\n      id: text_producer_name\n      label: HH Insurance Group, LLC\n      bounds:\n        x: 470\n        y: 438\n        width: 150\n        height: 20\n    - component_type: text\n      id: title_prior_carrier\n      label: Prior Carrier Details\n      bounds:\n        x: 280\n        y: 480\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_prior_carrier\n      label: Prior Carrier*\n      value: New Purchase\n      bounds:\n        x: 280\n        y: 512\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_prior_policy_expiration\n      label: Prior Policy Expiration Date\n      bounds:\n        x: 700\n        y: 512\n        width: 200\n        height: 36\n    - component_type: text\n      id: title_insured_info\n      label: Insured Information\n      bounds:\n        x: 280\n        y: 564\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_entity_type\n      label: Entity Type*\n      value: Individual\n      bounds:\n        x: 280\n        y: 596\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_first_name\n      label: First*\n      value: Landon\n      bounds:\n        x: 500\n        y: 596\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_middle_name\n      label: Middle\n      bounds:\n        x: 670\n        y: 596\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_last_name\n      label: Last*\n      value: Cassidy\n      bounds:\n        x: 840\n        y: 596\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_suffix\n      label: Suffix\n      bounds:\n        x: 1010\n        y: 596\n        width: 100\n        height: 36\n    - component_type: input\n      id: input_dob\n      label: DOB*\n      value: 05/20/1998\n      bounds:\n        x: 280\n        y: 648\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_insurance_score\n      label: Insurance Score*\n      value: Excellent (850-999)\n      bounds:\n        x: 500\n        y: 648\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_search_name\n      label: Search Name*\n      value: Landon Cassidy\n      bounds:\n        x: 280\n        y: 700\n        width: 200\n        height: 36\n    - component_type: link\n      id: link_reset\n      label: Reset\n      state: hovered\n      bounds:\n        x: 490\n        y: 708\n        width: 40\n        height: 20\n    - component_type: dropdown\n      id: dropdown_primary_phone\n      label: Primary Phone\n      value: Select...\n      bounds:\n        x: 280\n        y: 752\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_email\n      label: Email\n      bounds:\n        x: 500\n        y: 752\n        width: 200\n        height: 36\n    - component_type: checkbox\n      id: checkbox_no_email\n      label: No Email\n      bounds:\n        x: 720\n        y: 760\n        width: 80\n        height: 20\n    - component_type: text\n      id: title_dwelling_info\n      label: Dwelling Information\n      bounds:\n        x: 280\n        y: 804\n        width: 150\n        height: 20\n    - component_type: input\n      id: input_lookup_address\n      label: Lookup Address\n      bounds:\n        x: 280\n        y: 836\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_number\n      label: Number*\n      value: '4227'\n      bounds:\n        x: 500\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_direction\n      label: Direction\n      bounds:\n        x: 600\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_street\n      label: Street*\n      value: 5th\n      bounds:\n        x: 700\n        y: 836\n        width: 100\n        height: 36\n    - component_type: dropdown\n      id: dropdown_suffix\n      label: Suffix\n      value: Ave\n      bounds:\n        x: 820\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_post_dir\n      label: Post Dir\n      value: S\n      bounds:\n        x: 920\n        y: 836\n        width: 80\n        height: 36\n    - component_type: dropdown\n      id: dropdown_type\n      label: Type\n      bounds:\n        x: 1020\n        y: 836\n        width: 80\n        height: 36\n    - component_type: checkbox\n      id: checkbox_ignore_address_validation\n      label: Ignore Address Validation\n      bounds:\n        x: 1120\n        y: 820\n        width: 150\n        height: 20\n    - component_type: input\n      id: input_number_2\n      label: Number\n      bounds:\n        x: 1120\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_city\n      label: City*\n      value: St Petersburg\n      bounds:\n        x: 280\n        y: 888\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_county\n      label: County*\n      value: Pinellas\n      bounds:\n        x: 450\n        y: 888\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_state\n      label: State*\n      value: Florida\n      bounds:\n        x: 620\n        y: 888\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_zip\n      label: Zip*\n      value: 33711-1522\n      bounds:\n        x: 790\n        y: 888\n        width: 100\n        height: 36\n    - component_type: text\n      id: text_address_verified\n      label: Address Verified\n      bounds:\n        x: 910\n        y: 896\n        width: 100\n        height: 20\n    - component_type: link\n      id: link_view_map\n      label: View Map\n      bounds:\n        x: 1020\n        y: 896\n        width: 60\n        height: 20\n    - component_type: input\n      id: input_latitude\n      label: Latitude*\n      value: '27.766685'\n      bounds:\n        x: 280\n        y: 940\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_longitude\n      label: Longitude*\n      value: \"-82.690887\"\n      bounds:\n        x: 450\n        y: 940\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_construction_type\n      label: Construction Type*\n      value: Masonry\n      bounds:\n        x: 280\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_occupancy\n      label: Occupancy*\n      value: Owner Occupied\n      bounds:\n        x: 450\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_months_occupied\n      label: Months Occupied*\n      value: 9 to 12 Months\n      bounds:\n        x: 620\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_resided_less_than_2_years\n      label: Has the Insured resided at the risk address for less than 2 years?*\n      value: 'Yes'\n      bounds:\n        x: 280\n        y: 1044\n        width: 150\n        height: 36\n    - component_type: text\n      id: title_prior_address\n      label: Prior Address\n      bounds:\n        x: 280\n        y: 1096\n        width: 100\n        height: 20\n    - component_type: input\n      id: input_prior_address_number\n      label: Number*\n      value: '18001'\n      bounds:\n        x: 280\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_direction\n      label: Direction\n      bounds:\n        x: 380\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_street\n      label: Street*\n      value: Avalon\n      bounds:\n        x: 480\n        y: 1128\n        width: 100\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_address_suffix\n      label: Suffix\n      value: Ln\n      bounds:\n        x: 600\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_post_dir\n      label: Post Dir\n      bounds:\n        x: 700\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_address_type\n      label: Type\n      bounds:\n        x: 800\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_number_2\n      label: Number\n      bounds:\n        x: 900\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_city\n      label: City*\n      value: Tampa\n      bounds:\n        x: 280\n        y: 1180\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_county\n      label: County*\n      value: Hillsborough\n      bounds:\n        x: 450\n        y: 1180\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_state\n      label: State*\n      value: Florida\n      bounds:\n        x: 620\n        y: 1180\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_prior_zip\n      label: Zip*\n      value: 33647-3102\n      bounds:\n        x: 790\n        y: 1180\n        width: 100\n        height: 36\n    - component_type: link\n      id: link_verify_address\n      label: Verify\n      bounds:\n        x: 910\n        y: 1188\n        width: 80\n        height: 20\n    - component_type: text\n      id: text_prior_address_verified\n      label: Address\n      bounds:\n        x: 1000\n        y: 1188\n        width: 100\n        height: 20\n    - component_type: button\n      id: btn_next_page_bottom\n      label: Next Page\n      bounds:\n        x: 280\n        y: 1232\n        width: 80\n        height: 36\n  footer:\n  - component_type: container\n    id: main_footer\n    bounds:\n      x: 0\n      y: 1050\n      width: 1920\n      height: 30\n    subcomponents:\n    - component_type: text\n      id: text_powered_by\n      label: Powered by GUIDEWIRE\n      bounds:\n        x: 280\n        y: 1058\n        width: 150\n        height: 20\n    - component_type: text\n      id: text_environment\n      label: 'Environment : PROD AIIG'\n      bounds:\n        x: 1200\n        y: 1058\n        width: 150\n        height: 20\n    - component_type: text\n      id: text_current_logon\n      label: 'Current Logon : AG8529A (Sign Out)'\n      bounds:\n        x: 1400\n        y: 1058\n        width: 200\n        height: 20\n    - component_type: text\n      id: text_posting_date\n      label: 'Posting Date : 5/29/2025'\n      bounds:\n        x: 1650\n        y: 1058\n        width: 150\n        height: 20\n```"]}, {"role": "model", "parts": ["```yaml\nbrowser_component:\n  tab_title: *********** - Guide...\n  url: ai.iscs.com/innovation\n  address_bar_focused: false\n  tab_state: idle\nwebpage:\n  header:\n  - component_type: container\n    id: main_header\n    bounds:\n      x: 0\n      y: 69\n      width: 1920\n      height: 61\n    subcomponents:\n    - component_type: image\n      id: logo_american_integrity\n      label: AMERICAN INTEGRITY\n      bounds:\n        x: 16\n        y: 116\n        width: 128\n        height: 40\n    - component_type: navigation_menu\n      id: top_navigation\n      bounds:\n        x: 1290\n        y: 116\n        width: 600\n        height: 36\n      subcomponents:\n      - component_type: link\n        id: link_home\n        label: Home\n        bounds:\n          x: 1298\n          y: 126\n          width: 40\n          height: 20\n      - component_type: link\n        id: link_quote_policy\n        label: Quote/Policy\n        state: active\n        bounds:\n          x: 1362\n          y: 126\n          width: 80\n          height: 20\n      - component_type: link\n        id: link_claims\n        label: Claims\n        bounds:\n          x: 1466\n          y: 126\n          width: 48\n          height: 20\n      - component_type: link\n        id: link_cabinets\n        label: Cabinets\n        bounds:\n          x: 1538\n          y: 126\n          width: 58\n          height: 20\n      - component_type: link\n        id: link_support\n        label: Support\n        bounds:\n          x: 1620\n          y: 126\n          width: 54\n          height: 20\n  sidebar:\n  - component_type: navigation_menu\n    id: left_navigation\n    bounds:\n      x: 0\n      y: 169\n      width: 256\n      height: 809\n    subcomponents:\n    - component_type: search_input\n      id: search_sidebar\n      label: Search\n      bounds:\n        x: 16\n        y: 180\n        width: 188\n        height: 36\n    - component_type: button_icon\n      id: btn_search\n      label: Search\n      bounds:\n        x: 204\n        y: 180\n        width: 36\n        height: 36\n    - component_type: text\n      id: text_advanced_search\n      label: 'ADVANCED SEARCH:'\n      bounds:\n        x: 16\n        y: 224\n        width: 100\n        height: 16\n    - component_type: link\n      id: link_policy_search\n      label: POLICY\n      bounds:\n        x: 120\n        y: 224\n        width: 40\n        height: 16\n    - component_type: link\n      id: link_claims_search\n      label: CLAIMS\n      bounds:\n        x: 170\n        y: 224\n        width: 45\n        height: 16\n    - component_type: dropdown\n      id: dropdown_quote\n      label: Quote\n      state: expanded\n      bounds:\n        x: 16\n        y: 252\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy\n      label: Policy\n      state: active\n      bounds:\n        x: 32\n        y: 284\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_dwelling\n      label: Dwelling\n      bounds:\n        x: 32\n        y: 316\n        width: 208\n        height: 32\n      subcomponents:\n      - component_type: badge\n        id: badge_dwelling_count\n        label: '2'\n        bounds:\n          x: 210\n          y: 322\n          width: 16\n          height: 20\n    - component_type: link\n      id: link_review\n      label: Review\n      bounds:\n        x: 32\n        y: 348\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_attachments\n      label: Attachments\n      bounds:\n        x: 16\n        y: 380\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_correspondence\n      label: Correspondence\n      bounds:\n        x: 16\n        y: 412\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_tasks\n      label: Tasks\n      bounds:\n        x: 16\n        y: 444\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_notes\n      label: Notes\n      bounds:\n        x: 16\n        y: 476\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy_file\n      label: Policy File\n      bounds:\n        x: 16\n        y: 508\n        width: 224\n        height: 32\n  - component_type: action_bar\n    id: right_action_bar\n    bounds:\n      x: 1888\n      y: 169\n      width: 32\n      height: 809\n    subcomponents:\n    - component_type: button_icon\n      id: btn_summary\n      label: SUMMARY\n      bounds:\n        x: 1888\n        y: 180\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_quick_qt\n      label: WTCROFT QUICK QT\n      bounds:\n        x: 1888\n        y: 230\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_quote\n      label: NEW QUOTE\n      bounds:\n        x: 1888\n        y: 280\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_note\n      label: NEW NOTE\n      bounds:\n        x: 1888\n        y: 330\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_attach\n      label: NEW ATTACH...\n      bounds:\n        x: 1888\n        y: 380\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_task\n      label: NEW TASK\n      bounds:\n        x: 1888\n        y: 430\n        width: 32\n        height: 50\n  main_content:\n  - component_type: container\n    id: quote_summary_header\n    bounds:\n      x: 256\n      y: 169\n      width: 1632\n      height: 80\n    subcomponents:\n    - component_type: button\n      id: btn_quote\n      label: QUOTE\n      bounds:\n        x: 280\n        y: 180\n        width: 80\n        height: 36\n    - component_type: text\n      id: text_quote_number_label\n      label: Quote Number\n      bounds:\n        x: 376\n        y: 180\n        width: 80\n        height: 16\n    - component_type: text\n      id: text_quote_number_value\n      label: ***********\n      bounds:\n        x: 376\n        y: 198\n        width: 80\n        height: 16\n    - component_type: text\n      id: text_insured_label\n      label: Insured\n      bounds:\n        x: 472\n        y: 180\n        width: 80\n        height: 16\n    - component_type: link\n      id: link_insured_name\n      label: Landon Cassidy\n      bounds:\n        x: 472\n        y: 198\n        width: 90\n        height: 16\n    - component_type: text\n      id: text_product_label\n      label: Product\n      bounds:\n        x: 578\n        y: 180\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_product_value\n      label: Voluntary Homeowners (HO3)\n      bounds:\n        x: 578\n        y: 198\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_sub_type_label\n      label: Sub Type\n      bounds:\n        x: 744\n        y: 180\n        width: 50\n        height: 16\n    - component_type: text\n      id: text_sub_type_value\n      label: HO3\n      bounds:\n        x: 744\n        y: 198\n        width: 30\n        height: 16\n    - component_type: text\n      id: text_policy_term_label\n      label: Policy Term\n      bounds:\n        x: 810\n        y: 180\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_policy_term_value\n      label: 06/20/2025 - 06/20/2026\n      bounds:\n        x: 810\n        y: 198\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_producer_label\n      label: Producer\n      bounds:\n        x: 976\n        y: 180\n        width: 120\n        height: 16\n    - component_type: link\n      id: link_producer_name\n      label: HH Insurance Group, LLC\n      bounds:\n        x: 976\n        y: 198\n        width: 120\n        height: 16\n    - component_type: text\n      id: text_status_label\n      label: Status\n      bounds:\n        x: 1112\n        y: 180\n        width: 60\n        height: 16\n    - component_type: text\n      id: text_status_value\n      label: In Process\n      bounds:\n        x: 1112\n        y: 198\n        width: 60\n        height: 16\n    - component_type: text\n      id: text_premium_fees_label\n      label: Premium + Fees\n      bounds:\n        x: 1188\n        y: 180\n        width: 90\n        height: 16\n    - component_type: text\n      id: text_premium_fees_value\n      label: $17,776.90\n      bounds:\n        x: 1188\n        y: 198\n        width: 70\n        height: 16\n  - component_type: container\n    id: policy_general_form\n    bounds:\n      x: 256\n      y: 249\n      width: 1632\n      height: 831\n    subcomponents:\n    - component_type: link\n      id: link_return_to_home\n      label: < Return to Home\n      bounds:\n        x: 280\n        y: 260\n        width: 100\n        height: 20\n    - component_type: button\n      id: btn_next_page\n      label: NEXT PAGE\n      bounds:\n        x: 1290\n        y: 220\n        width: 90\n        height: 28\n    - component_type: button\n      id: btn_save\n      label: SAVE\n      bounds:\n        x: 1390\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_copy\n      label: COPY\n      bounds:\n        x: 1460\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_print\n      label: PRINT\n      bounds:\n        x: 1530\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_create_application\n      label: CREATE APPLICATION\n      bounds:\n        x: 1600\n        y: 220\n        width: 150\n        height: 28\n    - component_type: button\n      id: btn_discard_changes\n      label: DISCARD CHANGES\n      bounds:\n        x: 1760\n        y: 220\n        width: 130\n        height: 28\n    - component_type: button\n      id: btn_view_notes\n      label: VIEW NOTES\n      bounds:\n        x: 1290\n        y: 250\n        width: 100\n        height: 28\n    - component_type: button\n      id: btn_delete\n      label: DELETE\n      bounds:\n        x: 1400\n        y: 250\n        width: 70\n        height: 28\n    - component_type: button\n      id: btn_more\n      label: '... MORE'\n      bounds:\n        x: 1480\n        y: 250\n        width: 70\n        height: 28\n    - component_type: text\n      id: title_policy_general\n      label: Policy General\n      bounds:\n        x: 280\n        y: 292\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_product\n      label: Product*\n      value: Florida - Voluntary Homeowners (HO3) - American Integrity Insurance Group\n      bounds:\n        x: 280\n        y: 324\n        width: 400\n        height: 36\n    - component_type: input\n      id: input_effective_date\n      label: Effective Date*\n      value: 06/20/2025\n      bounds:\n        x: 280\n        y: 376\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_producer_code\n      label: 'Producer: Code*'\n      value: AG8529A1\n      bounds:\n        x: 280\n        y: 428\n        width: 150\n        height: 36\n    - component_type: text\n      id: text_producer_name\n      label: HH Insurance Group, LLC\n      bounds:\n        x: 470\n        y: 438\n        width: 150\n        height: 20\n    - component_type: text\n      id: title_prior_carrier\n      label: Prior Carrier Details\n      bounds:\n        x: 280\n        y: 480\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_prior_carrier\n      label: Prior Carrier*\n      value: New Purchase\n      bounds:\n        x: 280\n        y: 512\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_prior_policy_expiration\n      label: Prior Policy Expiration Date\n      bounds:\n        x: 700\n        y: 512\n        width: 200\n        height: 36\n    - component_type: text\n      id: title_insured_info\n      label: Insured Information\n      bounds:\n        x: 280\n        y: 564\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_entity_type\n      label: Entity Type*\n      value: Individual\n      bounds:\n        x: 280\n        y: 596\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_first_name\n      label: First*\n      value: Landon\n      bounds:\n        x: 500\n        y: 596\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_middle_name\n      label: Middle\n      bounds:\n        x: 670\n        y: 596\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_last_name\n      label: Last*\n      value: Cassidy\n      bounds:\n        x: 840\n        y: 596\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_suffix\n      label: Suffix\n      bounds:\n        x: 1010\n        y: 596\n        width: 100\n        height: 36\n    - component_type: input\n      id: input_dob\n      label: DOB*\n      value: 05/20/1998\n      bounds:\n        x: 280\n        y: 648\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_insurance_score\n      label: Insurance Score*\n      value: Excellent (850-999)\n      bounds:\n        x: 500\n        y: 648\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_search_name\n      label: Search Name*\n      value: Landon Cassidy\n      bounds:\n        x: 280\n        y: 700\n        width: 200\n        height: 36\n    - component_type: link\n      id: link_reset\n      label: Reset\n      bounds:\n        x: 490\n        y: 708\n        width: 40\n        height: 20\n    - component_type: dropdown\n      id: dropdown_primary_phone\n      label: Primary Phone\n      value: Select...\n      bounds:\n        x: 280\n        y: 752\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_email\n      label: Email\n      bounds:\n        x: 500\n        y: 752\n        width: 200\n        height: 36\n    - component_type: checkbox\n      id: checkbox_no_email\n      label: No Email\n      bounds:\n        x: 720\n        y: 760\n        width: 80\n        height: 20\n    - component_type: text\n      id: title_dwelling_info\n      label: Dwelling Information\n      bounds:\n        x: 280\n        y: 804\n        width: 150\n        height: 20\n    - component_type: input\n      id: input_lookup_address\n      label: Lookup Address\n      bounds:\n        x: 280\n        y: 836\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_number\n      label: Number*\n      value: '4227'\n      bounds:\n        x: 500\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_direction\n      label: Direction\n      bounds:\n        x: 600\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_street\n      label: Street*\n      value: 5th\n      bounds:\n        x: 700\n        y: 836\n        width: 100\n        height: 36\n    - component_type: dropdown\n      id: dropdown_suffix\n      label: Suffix\n      value: Ave\n      bounds:\n        x: 820\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_post_dir\n      label: Post Dir\n      value: S\n      bounds:\n        x: 920\n        y: 836\n        width: 80\n        height: 36\n    - component_type: dropdown\n      id: dropdown_type\n      label: Type\n      bounds:\n        x: 1020\n        y: 836\n        width: 80\n        height: 36\n    - component_type: checkbox\n      id: checkbox_ignore_address_validation\n      label: Ignore Address Validation\n      bounds:\n        x: 1120\n        y: 820\n        width: 150\n        height: 20\n    - component_type: input\n      id: input_number_2\n      label: Number\n      bounds:\n        x: 1120\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_city\n      label: City*\n      value: St Petersburg\n      bounds:\n        x: 280\n        y: 888\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_county\n      label: County*\n      value: Pinellas\n      bounds:\n        x: 450\n        y: 888\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_state\n      label: State*\n      value: Florida\n      bounds:\n        x: 620\n        y: 888\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_zip\n      label: Zip*\n      value: 33711-1522\n      bounds:\n        x: 790\n        y: 888\n        width: 100\n        height: 36\n    - component_type: text\n      id: text_address_verified\n      label: Address Verified\n      bounds:\n        x: 910\n        y: 896\n        width: 100\n        height: 20\n    - component_type: link\n      id: link_view_map\n      label: View Map\n      bounds:\n        x: 1020\n        y: 896\n        width: 60\n        height: 20\n    - component_type: input\n      id: input_latitude\n      label: Latitude*\n      value: '27.766685'\n      bounds:\n        x: 280\n        y: 940\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_longitude\n      label: Longitude*\n      value: \"-82.690887\"\n      bounds:\n        x: 450\n        y: 940\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_construction_type\n      label: Construction Type*\n      value: Masonry\n      bounds:\n        x: 280\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_occupancy\n      label: Occupancy*\n      value: Owner Occupied\n      bounds:\n        x: 450\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_months_occupied\n      label: Months Occupied*\n      value: 9 to 12 Months\n      bounds:\n        x: 620\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_resided_less_than_2_years\n      label: Has the Insured resided at the risk address for less than 2 years?*\n      value: 'Yes'\n      bounds:\n        x: 280\n        y: 1044\n        width: 150\n        height: 36\n    - component_type: text\n      id: title_prior_address\n      label: Prior Address\n      bounds:\n        x: 280\n        y: 1096\n        width: 100\n        height: 20\n    - component_type: input\n      id: input_prior_address_number\n      label: Number*\n      value: '18001'\n      bounds:\n        x: 280\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_direction\n      label: Direction\n      bounds:\n        x: 380\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_street\n      label: Street*\n      value: Avalon\n      bounds:\n        x: 480\n        y: 1128\n        width: 100\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_address_suffix\n      label: Suffix\n      value: Ln\n      bounds:\n        x: 600\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_post_dir\n      label: Post Dir\n      bounds:\n        x: 700\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_address_type\n      label: Type\n      bounds:\n        x: 800\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_number_2\n      label: Number\n      bounds:\n        x: 900\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: link\n      id: link_verify_address\n      label: Verify\n      bounds:\n        x: 910\n        y: 1188\n        width: 80\n        height: 20\n    - component_type: text\n      id: text_prior_address_verified\n      label: Address\n      bounds:\n        x: 1000\n        y: 1188\n        width: 100\n        height: 20\n    - component_type: button\n      id: btn_next_page_bottom\n      label: Next Page\n      bounds:\n        x: 280\n        y: 1232\n        width: 80\n        height: 36\n```"]}, {"role": "model", "parts": ["```yaml\nbrowser_component:\n  tab_title: *********** - Guidewire PolicyCenter\n  url: ai.iscs.com/innovation\n  address_bar_focused: false\n  tab_state: idle\nwebpage:\n  header:\n  - component_type: container\n    id: main_header\n    bounds:\n      x: 0\n      y: 69\n      width: 1920\n      height: 61\n    subcomponents:\n    - component_type: image\n      id: logo_american_integrity\n      label: AMERICAN INTEGRITY\n      bounds:\n        x: 16\n        y: 116\n        width: 128\n        height: 40\n    - component_type: navigation_menu\n      id: top_navigation\n      bounds:\n        x: 1290\n        y: 116\n        width: 600\n        height: 36\n      subcomponents:\n      - component_type: link\n        id: link_home\n        label: Home\n        bounds:\n          x: 1298\n          y: 126\n          width: 40\n          height: 20\n      - component_type: link\n        id: link_quote_policy\n        label: Quote/Policy\n        state: active\n        bounds:\n          x: 1362\n          y: 126\n          width: 80\n          height: 20\n      - component_type: link\n        id: link_claims\n        label: Claims\n        bounds:\n          x: 1466\n          y: 126\n          width: 48\n          height: 20\n      - component_type: link\n        id: link_cabinets\n        label: Cabinets\n        bounds:\n          x: 1538\n          y: 126\n          width: 58\n          height: 20\n      - component_type: link\n        id: link_support\n        label: Support\n        bounds:\n          x: 1620\n          y: 126\n          width: 54\n          height: 20\n  sidebar:\n  - component_type: navigation_menu\n    id: left_navigation\n    bounds:\n      x: 0\n      y: 169\n      width: 256\n      height: 809\n    subcomponents:\n    - component_type: search_input\n      id: search_sidebar\n      label: Search\n      bounds:\n        x: 16\n        y: 180\n        width: 188\n        height: 36\n    - component_type: button_icon\n      id: btn_search\n      label: Search\n      bounds:\n        x: 204\n        y: 180\n        width: 36\n        height: 36\n    - component_type: text\n      id: text_advanced_search\n      label: 'ADVANCED SEARCH:'\n      bounds:\n        x: 16\n        y: 224\n        width: 100\n        height: 16\n    - component_type: link\n      id: link_policy_search\n      label: POLICY\n      bounds:\n        x: 120\n        y: 224\n        width: 40\n        height: 16\n    - component_type: link\n      id: link_claims_search\n      label: CLAIMS\n      bounds:\n        x: 170\n        y: 224\n        width: 45\n        height: 16\n    - component_type: dropdown\n      id: dropdown_quote\n      label: Quote\n      state: expanded\n      bounds:\n        x: 16\n        y: 252\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy\n      label: Policy\n      state: active\n      bounds:\n        x: 32\n        y: 284\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_dwelling\n      label: Dwelling\n      bounds:\n        x: 32\n        y: 316\n        width: 208\n        height: 32\n      subcomponents:\n      - component_type: badge\n        id: badge_dwelling_count\n        label: '2'\n        bounds:\n          x: 210\n          y: 322\n          width: 16\n          height: 20\n    - component_type: link\n      id: link_review\n      label: Review\n      bounds:\n        x: 32\n        y: 348\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_attachments\n      label: Attachments\n      bounds:\n        x: 16\n        y: 380\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_correspondence\n      label: Correspondence\n      bounds:\n        x: 16\n        y: 412\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_tasks\n      label: Tasks\n      bounds:\n        x: 16\n        y: 444\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_notes\n      label: Notes\n      bounds:\n        x: 16\n        y: 476\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy_file\n      label: Policy File\n      bounds:\n        x: 16\n        y: 508\n        width: 224\n        height: 32\n  - component_type: action_bar\n    id: right_action_bar\n    bounds:\n      x: 1888\n      y: 169\n      width: 32\n      height: 809\n    subcomponents:\n    - component_type: button_icon\n      id: btn_summary\n      label: SUMMARY\n      bounds:\n        x: 1888\n        y: 180\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_quick_qt\n      label: WTCROFT QUICK QT\n      bounds:\n        x: 1888\n        y: 230\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_quote\n      label: NEW QUOTE\n      bounds:\n        x: 1888\n        y: 280\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_note\n      label: NEW NOTE\n      bounds:\n        x: 1888\n        y: 330\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_attach\n      label: NEW ATTACH...\n      bounds:\n        x: 1888\n        y: 380\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_task\n      label: NEW TASK\n      bounds:\n        x: 1888\n        y: 430\n        width: 32\n        height: 50\n  main_content:\n  - component_type: container\n    id: quote_summary_header\n    bounds:\n      x: 256\n      y: 169\n      width: 1632\n      height: 80\n    subcomponents:\n    - component_type: button\n      id: btn_quote\n      label: QUOTE\n      bounds:\n        x: 280\n        y: 180\n        width: 80\n        height: 36\n    - component_type: text\n      id: text_quote_number_label\n      label: Quote Number\n      bounds:\n        x: 376\n        y: 180\n        width: 80\n        height: 16\n    - component_type: text\n      id: text_quote_number_value\n      label: ***********\n      bounds:\n        x: 376\n        y: 198\n        width: 80\n        height: 16\n    - component_type: text\n      id: text_insured_label\n      label: Insured\n      bounds:\n        x: 472\n        y: 180\n        width: 80\n        height: 16\n    - component_type: link\n      id: link_insured_name\n      label: Landon Cassidy\n      bounds:\n        x: 472\n        y: 198\n        width: 90\n        height: 16\n    - component_type: text\n      id: text_product_label\n      label: Product\n      bounds:\n        x: 578\n        y: 180\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_product_value\n      label: Voluntary Homeowners (HO3)\n      bounds:\n        x: 578\n        y: 198\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_sub_type_label\n      label: Sub Type\n      bounds:\n        x: 744\n        y: 180\n        width: 50\n        height: 16\n    - component_type: text\n      id: text_sub_type_value\n      label: HO3\n      bounds:\n        x: 744\n        y: 198\n        width: 30\n        height: 16\n    - component_type: text\n      id: text_policy_term_label\n      label: Policy Term\n      bounds:\n        x: 810\n        y: 180\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_policy_term_value\n      label: 06/20/2025 - 06/20/2026\n      bounds:\n        x: 810\n        y: 198\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_producer_label\n      label: Producer\n      bounds:\n        x: 976\n        y: 180\n        width: 120\n        height: 16\n    - component_type: link\n      id: link_producer_name\n      label: HH Insurance Group, LLC\n      bounds:\n        x: 976\n        y: 198\n        width: 120\n        height: 16\n    - component_type: text\n      id: text_status_label\n      label: Status\n      bounds:\n        x: 1112\n        y: 180\n        width: 60\n        height: 16\n    - component_type: text\n      id: text_status_value\n      label: In Process\n      bounds:\n        x: 1112\n        y: 198\n        width: 60\n        height: 16\n    - component_type: text\n      id: text_premium_fees_label\n      label: Premium + Fees\n      bounds:\n        x: 1188\n        y: 180\n        width: 90\n        height: 16\n    - component_type: text\n      id: text_premium_fees_value\n      label: $17,776.90\n      bounds:\n        x: 1188\n        y: 198\n        width: 70\n        height: 16\n  - component_type: container\n    id: policy_general_form\n    bounds:\n      x: 256\n      y: 249\n      width: 1632\n      height: 729\n    subcomponents:\n    - component_type: link\n      id: link_return_to_home\n      label: < Return to Home\n      bounds:\n        x: 280\n        y: 260\n        width: 100\n        height: 20\n    - component_type: button\n      id: btn_next_page\n      label: NEXT PAGE\n      bounds:\n        x: 1290\n        y: 220\n        width: 90\n        height: 28\n    - component_type: button\n      id: btn_save\n      label: SAVE\n      bounds:\n        x: 1390\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_copy\n      label: COPY\n      bounds:\n        x: 1460\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_print\n      label: PRINT\n      bounds:\n        x: 1530\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_create_application\n      label: CREATE APPLICATION\n      bounds:\n        x: 1600\n        y: 220\n        width: 150\n        height: 28\n    - component_type: button\n      id: btn_discard_changes\n      label: DISCARD CHANGES\n      bounds:\n        x: 1760\n        y: 220\n        width: 130\n        height: 28\n    - component_type: button\n      id: btn_view_notes\n      label: VIEW NOTES\n      bounds:\n        x: 1290\n        y: 250\n        width: 100\n        height: 28\n    - component_type: button\n      id: btn_delete\n      label: DELETE\n      bounds:\n        x: 1400\n        y: 250\n        width: 70\n        height: 28\n    - component_type: button\n      id: btn_more\n      label: '... MORE'\n      bounds:\n        x: 1480\n        y: 250\n        width: 70\n        height: 28\n    - component_type: text\n      id: title_policy_general\n      label: Policy General\n      bounds:\n        x: 280\n        y: 292\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_product\n      label: Product*\n      value: Florida - Voluntary Homeowners (HO3) - American Integrity Insurance Group\n      bounds:\n        x: 280\n        y: 324\n        width: 400\n        height: 36\n    - component_type: input\n      id: input_effective_date\n      label: Effective Date*\n      value: 06/20/2025\n      bounds:\n        x: 280\n        y: 376\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_producer_code\n      label: 'Producer: Code*'\n      value: AG8529A1\n      bounds:\n        x: 280\n        y: 428\n        width: 150\n        height: 36\n    - component_type: text\n      id: text_producer_name\n      label: HH Insurance Group, LLC\n      bounds:\n        x: 470\n        y: 438\n        width: 150\n        height: 20\n    - component_type: text\n      id: title_prior_carrier\n      label: Prior Carrier Details\n      bounds:\n        x: 280\n        y: 480\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_prior_carrier\n      label: Prior Carrier*\n      value: New Purchase\n      bounds:\n        x: 280\n        y: 512\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_prior_policy_expiration\n      label: Prior Policy Expiration Date\n      bounds:\n        x: 700\n        y: 512\n        width: 200\n        height: 36\n    - component_type: text\n      id: title_insured_info\n      label: Insured Information\n      bounds:\n        x: 280\n        y: 564\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_entity_type\n      label: Entity Type*\n      value: Individual\n      bounds:\n        x: 280\n        y: 596\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_first_name\n      label: First*\n      value: Landon\n      bounds:\n        x: 500\n        y: 596\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_middle_name\n      label: Middle\n      bounds:\n        x: 670\n        y: 596\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_last_name\n      label: Last*\n      value: Cassidy\n      bounds:\n        x: 840\n        y: 596\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_suffix\n      label: Suffix\n      bounds:\n        x: 1010\n        y: 596\n        width: 100\n        height: 36\n    - component_type: input\n      id: input_dob\n      label: DOB*\n      value: 05/20/1998\n      bounds:\n        x: 280\n        y: 648\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_insurance_score\n      label: Insurance Score*\n      value: Excellent (850-999)\n      bounds:\n        x: 500\n        y: 648\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_search_name\n      label: Search Name*\n      value: Landon Cassidy\n      bounds:\n        x: 280\n        y: 700\n        width: 200\n        height: 36\n    - component_type: link\n      id: link_reset\n      label: Reset\n      state: hovered\n      bounds:\n        x: 490\n        y: 708\n        width: 40\n        height: 20\n    - component_type: dropdown\n      id: dropdown_primary_phone\n      label: Primary Phone\n      value: Select...\n      bounds:\n        x: 280\n        y: 752\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_email\n      label: Email\n      bounds:\n        x: 500\n        y: 752\n        width: 200\n        height: 36\n    - component_type: checkbox\n      id: checkbox_no_email\n      label: No Email\n      bounds:\n        x: 720\n        y: 760\n        width: 80\n        height: 20\n    - component_type: text\n      id: title_dwelling_info\n      label: Dwelling Information\n      bounds:\n        x: 280\n        y: 804\n        width: 150\n        height: 20\n    - component_type: input\n      id: input_lookup_address\n      label: Lookup Address\n      bounds:\n        x: 280\n        y: 836\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_number\n      label: Number*\n      value: '4227'\n      bounds:\n        x: 500\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_direction\n      label: Direction\n      bounds:\n        x: 600\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_street\n      label: Street*\n      value: 5th\n      bounds:\n        x: 700\n        y: 836\n        width: 100\n        height: 36\n    - component_type: dropdown\n      id: dropdown_suffix\n      label: Suffix\n      value: Ave\n      bounds:\n        x: 820\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_post_dir\n      label: Post Dir\n      value: S\n      bounds:\n        x: 920\n        y: 836\n        width: 80\n        height: 36\n    - component_type: dropdown\n      id: dropdown_type\n      label: Type\n      bounds:\n        x: 1020\n        y: 836\n        width: 80\n        height: 36\n    - component_type: checkbox\n      id: checkbox_ignore_address_validation\n      label: Ignore Address Validation\n      bounds:\n        x: 1120\n        y: 820\n        width: 150\n        height: 20\n    - component_type: input\n      id: input_number_2\n      label: Number\n      bounds:\n        x: 1120\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_city\n      label: City*\n      value: St Petersburg\n      bounds:\n        x: 280\n        y: 888\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_county\n      label: County*\n      value: Pinellas\n      bounds:\n        x: 450\n        y: 888\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_state\n      label: State*\n      value: Florida\n      bounds:\n        x: 620\n        y: 888\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_zip\n      label: Zip*\n      value: 33711-1522\n      bounds:\n        x: 790\n        y: 888\n        width: 100\n        height: 36\n    - component_type: text\n      id: text_address_verified\n      label: Address Verified\n      bounds:\n        x: 910\n        y: 896\n        width: 100\n        height: 20\n    - component_type: link\n      id: link_view_map\n      label: View Map\n      bounds:\n        x: 1020\n        y: 896\n        width: 60\n        height: 20\n    - component_type: input\n      id: input_latitude\n      label: Latitude*\n      value: '27.766685'\n      bounds:\n        x: 280\n        y: 940\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_longitude\n      label: Longitude*\n      value: \"-82.690887\"\n      bounds:\n        x: 450\n        y: 940\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_construction_type\n      label: Construction Type*\n      value: Masonry\n      bounds:\n        x: 280\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_occupancy\n      label: Occupancy*\n      value: Owner Occupied\n      bounds:\n        x: 450\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_months_occupied\n      label: Months Occupied*\n      value: 9 to 12 Months\n      bounds:\n        x: 620\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_resided_less_than_2_years\n      label: Has the Insured resided at the risk address for less than 2 years?*\n      value: 'Yes'\n      bounds:\n        x: 280\n        y: 1044\n        width: 150\n        height: 36\n    - component_type: text\n      id: title_prior_address\n      label: Prior Address\n      bounds:\n        x: 280\n        y: 1096\n        width: 100\n        height: 20\n    - component_type: input\n      id: input_prior_address_number\n      label: Number*\n      value: '18001'\n      bounds:\n        x: 280\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_direction\n      label: Direction\n      bounds:\n        x: 380\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_street\n      label: Street*\n      value: Avalon\n      bounds:\n        x: 480\n        y: 1128\n        width: 100\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_address_suffix\n      label: Suffix\n      value: Ln\n      bounds:\n        x: 600\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_post_dir\n      label: Post Dir\n      bounds:\n        x: 700\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_address_type\n      label: Type\n      bounds:\n        x: 800\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_number_2\n      label: Number\n      bounds:\n        x: 900\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: link\n      id: link_verify_address\n      label: Verify\n      bounds:\n        x: 910\n        y: 1188\n        width: 80\n        height: 20\n    - component_type: text\n      id: text_prior_address_verified\n      label: Address\n      bounds:\n        x: 1000\n        y: 1188\n        width: 100\n        height: 20\n```"]}, {"role": "model", "parts": ["```yaml\nbrowser_component:\n  tab_title: *********** - Guide...\n  url: ai.iscs.com/innovation\n  address_bar_focused: false\n  tab_state: idle\nwebpage:\n  header:\n  - component_type: container\n    id: main_header\n    bounds:\n      x: 0\n      y: 69\n      width: 1920\n      height: 61\n    subcomponents:\n    - component_type: image\n      id: logo_american_integrity\n      label: AMERICAN INTEGRITY\n      bounds:\n        x: 16\n        y: 116\n        width: 128\n        height: 40\n    - component_type: navigation_menu\n      id: top_navigation\n      bounds:\n        x: 1290\n        y: 116\n        width: 600\n        height: 36\n      subcomponents:\n      - component_type: link\n        id: link_home\n        label: Home\n        bounds:\n          x: 1298\n          y: 126\n          width: 40\n          height: 20\n      - component_type: link\n        id: link_quote_policy\n        label: Quote/Policy\n        state: active\n        bounds:\n          x: 1362\n          y: 126\n          width: 80\n          height: 20\n      - component_type: link\n        id: link_claims\n        label: Claims\n        bounds:\n          x: 1466\n          y: 126\n          width: 48\n          height: 20\n      - component_type: link\n        id: link_cabinets\n        label: Cabinets\n        bounds:\n          x: 1538\n          y: 126\n          width: 58\n          height: 20\n      - component_type: link\n        id: link_support\n        label: Support\n        bounds:\n          x: 1620\n          y: 126\n          width: 54\n          height: 20\n  sidebar:\n  - component_type: navigation_menu\n    id: left_navigation\n    bounds:\n      x: 0\n      y: 169\n      width: 256\n      height: 809\n    subcomponents:\n    - component_type: search_input\n      id: search_sidebar\n      label: Search\n      bounds:\n        x: 16\n        y: 180\n        width: 188\n        height: 36\n    - component_type: button_icon\n      id: btn_search\n      label: Search\n      bounds:\n        x: 204\n        y: 180\n        width: 36\n        height: 36\n    - component_type: text\n      id: text_advanced_search\n      label: 'ADVANCED SEARCH:'\n      bounds:\n        x: 16\n        y: 224\n        width: 100\n        height: 16\n    - component_type: link\n      id: link_policy_search\n      label: POLICY\n      bounds:\n        x: 120\n        y: 224\n        width: 40\n        height: 16\n    - component_type: link\n      id: link_claims_search\n      label: CLAIMS\n      bounds:\n        x: 170\n        y: 224\n        width: 45\n        height: 16\n    - component_type: dropdown\n      id: dropdown_quote\n      label: Quote\n      state: expanded\n      bounds:\n        x: 16\n        y: 252\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy\n      label: Policy\n      state: active\n      bounds:\n        x: 32\n        y: 284\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_dwelling\n      label: Dwelling\n      bounds:\n        x: 32\n        y: 316\n        width: 208\n        height: 32\n      subcomponents:\n      - component_type: badge\n        id: badge_dwelling_count\n        label: '2'\n        bounds:\n          x: 210\n          y: 322\n          width: 16\n          height: 20\n    - component_type: link\n      id: link_review\n      label: Review\n      bounds:\n        x: 32\n        y: 348\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_attachments\n      label: Attachments\n      bounds:\n        x: 16\n        y: 380\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_correspondence\n      label: Correspondence\n      bounds:\n        x: 16\n        y: 412\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_tasks\n      label: Tasks\n      bounds:\n        x: 16\n        y: 444\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_notes\n      label: Notes\n      bounds:\n        x: 16\n        y: 476\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy_file\n      label: Policy File\n      bounds:\n        x: 16\n        y: 508\n        width: 224\n        height: 32\n  - component_type: action_bar\n    id: right_action_bar\n    bounds:\n      x: 1888\n      y: 169\n      width: 32\n      height: 809\n    subcomponents:\n    - component_type: button_icon\n      id: btn_summary\n      label: SUMMARY\n      bounds:\n        x: 1888\n        y: 180\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_quick_qt\n      label: WTCROFT QUICK QT\n      bounds:\n        x: 1888\n        y: 230\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_quote\n      label: NEW QUOTE\n      bounds:\n        x: 1888\n        y: 280\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_note\n      label: NEW NOTE\n      bounds:\n        x: 1888\n        y: 330\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_attach\n      label: NEW ATTACH...\n      bounds:\n        x: 1888\n        y: 380\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_task\n      label: NEW TASK\n      bounds:\n        x: 1888\n        y: 430\n        width: 32\n        height: 50\n  main_content:\n  - component_type: container\n    id: quote_summary_header\n    bounds:\n      x: 256\n      y: 169\n      width: 1632\n      height: 80\n    subcomponents:\n    - component_type: button\n      id: btn_quote\n      label: QUOTE\n      bounds:\n        x: 280\n        y: 180\n        width: 80\n        height: 36\n    - component_type: text\n      id: text_quote_number_label\n      label: Quote Number\n      bounds:\n        x: 376\n        y: 180\n        width: 80\n        height: 16\n    - component_type: text\n      id: text_quote_number_value\n      label: ***********\n      bounds:\n        x: 376\n        y: 198\n        width: 80\n        height: 16\n    - component_type: text\n      id: text_insured_label\n      label: Insured\n      bounds:\n        x: 472\n        y: 180\n        width: 80\n        height: 16\n    - component_type: link\n      id: link_insured_name\n      label: Landon Cassidy\n      bounds:\n        x: 472\n        y: 198\n        width: 90\n        height: 16\n    - component_type: text\n      id: text_product_label\n      label: Product\n      bounds:\n        x: 578\n        y: 180\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_product_value\n      label: Voluntary Homeowners (HO3)\n      bounds:\n        x: 578\n        y: 198\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_sub_type_label\n      label: Sub Type\n      bounds:\n        x: 744\n        y: 180\n        width: 50\n        height: 16\n    - component_type: text\n      id: text_sub_type_value\n      label: HO3\n      bounds:\n        x: 744\n        y: 198\n        width: 30\n        height: 16\n    - component_type: text\n      id: text_policy_term_label\n      label: Policy Term\n      bounds:\n        x: 810\n        y: 180\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_policy_term_value\n      label: 06/20/2025 - 06/20/2026\n      bounds:\n        x: 810\n        y: 198\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_producer_label\n      label: Producer\n      bounds:\n        x: 976\n        y: 180\n        width: 120\n        height: 16\n    - component_type: link\n      id: link_producer_name\n      label: HH Insurance Group, LLC\n      bounds:\n        x: 976\n        y: 198\n        width: 120\n        height: 16\n    - component_type: text\n      id: text_status_label\n      label: Status\n      bounds:\n        x: 1112\n        y: 180\n        width: 60\n        height: 16\n    - component_type: text\n      id: text_status_value\n      label: In Process\n      bounds:\n        x: 1112\n        y: 198\n        width: 60\n        height: 16\n    - component_type: text\n      id: text_premium_fees_label\n      label: Premium + Fees\n      bounds:\n        x: 1188\n        y: 180\n        width: 90\n        height: 16\n    - component_type: text\n      id: text_premium_fees_value\n      label: $17,776.90\n      bounds:\n        x: 1188\n        y: 198\n        width: 70\n        height: 16\n  - component_type: container\n    id: policy_general_form\n    bounds:\n      x: 256\n      y: 249\n      width: 1632\n      height: 831\n    subcomponents:\n    - component_type: link\n      id: link_return_to_home\n      label: < Return to Home\n      bounds:\n        x: 280\n        y: 260\n        width: 100\n        height: 20\n    - component_type: button\n      id: btn_next_page\n      label: NEXT PAGE\n      bounds:\n        x: 1290\n        y: 220\n        width: 90\n        height: 28\n    - component_type: button\n      id: btn_save\n      label: SAVE\n      bounds:\n        x: 1390\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_copy\n      label: COPY\n      bounds:\n        x: 1460\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_print\n      label: PRINT\n      bounds:\n        x: 1530\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_create_application\n      label: CREATE APPLICATION\n      bounds:\n        x: 1600\n        y: 220\n        width: 150\n        height: 28\n    - component_type: button\n      id: btn_discard_changes\n      label: DISCARD CHANGES\n      bounds:\n        x: 1760\n        y: 220\n        width: 130\n        height: 28\n    - component_type: button\n      id: btn_view_notes\n      label: VIEW NOTES\n      bounds:\n        x: 1290\n        y: 250\n        width: 100\n        height: 28\n    - component_type: button\n      id: btn_delete\n      label: DELETE\n      bounds:\n        x: 1400\n        y: 250\n        width: 70\n        height: 28\n    - component_type: button\n      id: btn_more\n      label: '... MORE'\n      bounds:\n        x: 1480\n        y: 250\n        width: 70\n        height: 28\n    - component_type: text\n      id: title_policy_general\n      label: Policy General\n      bounds:\n        x: 280\n        y: 292\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_product\n      label: Product*\n      value: Florida - Voluntary Homeowners (HO3) - American Integrity Insurance Group\n      bounds:\n        x: 280\n        y: 324\n        width: 400\n        height: 36\n    - component_type: input\n      id: input_effective_date\n      label: Effective Date*\n      value: 06/20/2025\n      bounds:\n        x: 280\n        y: 376\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_producer_code\n      label: 'Producer: Code*'\n      value: AG8529A1\n      bounds:\n        x: 280\n        y: 428\n        width: 150\n        height: 36\n    - component_type: text\n      id: text_producer_name\n      label: HH Insurance Group, LLC\n      bounds:\n        x: 470\n        y: 438\n        width: 150\n        height: 20\n    - component_type: text\n      id: title_prior_carrier\n      label: Prior Carrier Details\n      bounds:\n        x: 280\n        y: 480\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_prior_carrier\n      label: Prior Carrier*\n      value: New Purchase\n      bounds:\n        x: 280\n        y: 512\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_prior_policy_expiration\n      label: Prior Policy Expiration Date\n      bounds:\n        x: 700\n        y: 512\n        width: 200\n        height: 36\n    - component_type: text\n      id: title_insured_info\n      label: Insured Information\n      bounds:\n        x: 280\n        y: 564\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_entity_type\n      label: Entity Type*\n      value: Individual\n      bounds:\n        x: 280\n        y: 596\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_first_name\n      label: First*\n      value: Landon\n      bounds:\n        x: 500\n        y: 596\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_middle_name\n      label: Middle\n      bounds:\n        x: 670\n        y: 596\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_last_name\n      label: Last*\n      value: Cassidy\n      bounds:\n        x: 840\n        y: 596\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_suffix\n      label: Suffix\n      bounds:\n        x: 1010\n        y: 596\n        width: 100\n        height: 36\n    - component_type: input\n      id: input_dob\n      label: DOB*\n      value: 05/20/1998\n      bounds:\n        x: 280\n        y: 648\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_insurance_score\n      label: Insurance Score*\n      value: Excellent (850-999)\n      bounds:\n        x: 500\n        y: 648\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_search_name\n      label: Search Name*\n      value: Landon Cassidy\n      bounds:\n        x: 280\n        y: 700\n        width: 200\n        height: 36\n    - component_type: link\n      id: link_reset\n      label: Reset\n      state: hovered\n      bounds:\n        x: 490\n        y: 708\n        width: 40\n        height: 20\n    - component_type: dropdown\n      id: dropdown_primary_phone\n      label: Primary Phone\n      value: Select...\n      bounds:\n        x: 280\n        y: 752\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_email\n      label: Email\n      bounds:\n        x: 500\n        y: 752\n        width: 200\n        height: 36\n    - component_type: checkbox\n      id: checkbox_no_email\n      label: No Email\n      bounds:\n        x: 720\n        y: 760\n        width: 80\n        height: 20\n    - component_type: text\n      id: title_dwelling_info\n      label: Dwelling Information\n      bounds:\n        x: 280\n        y: 804\n        width: 150\n        height: 20\n    - component_type: input\n      id: input_lookup_address\n      label: Lookup Address\n      bounds:\n        x: 280\n        y: 836\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_number\n      label: Number*\n      value: '4227'\n      bounds:\n        x: 500\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_direction\n      label: Direction\n      bounds:\n        x: 600\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_street\n      label: Street*\n      value: 5th\n      bounds:\n        x: 700\n        y: 836\n        width: 100\n        height: 36\n    - component_type: dropdown\n      id: dropdown_suffix\n      label: Suffix\n      value: Ave\n      bounds:\n        x: 820\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_post_dir\n      label: Post Dir\n      value: S\n      bounds:\n        x: 920\n        y: 836\n        width: 80\n        height: 36\n    - component_type: dropdown\n      id: dropdown_type\n      label: Type\n      bounds:\n        x: 1020\n        y: 836\n        width: 80\n        height: 36\n    - component_type: checkbox\n      id: checkbox_ignore_address_validation\n      label: Ignore Address Validation\n      bounds:\n        x: 1120\n        y: 820\n        width: 150\n        height: 20\n    - component_type: input\n      id: input_number_2\n      label: Number\n      bounds:\n        x: 1120\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_city\n      label: City*\n      value: St Petersburg\n      bounds:\n        x: 280\n        y: 888\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_county\n      label: County*\n      value: Pinellas\n      bounds:\n        x: 450\n        y: 888\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_state\n      label: State*\n      value: Florida\n      bounds:\n        x: 620\n        y: 888\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_zip\n      label: Zip*\n      value: 33711-1522\n      bounds:\n        x: 790\n        y: 888\n        width: 100\n        height: 36\n    - component_type: text\n      id: text_address_verified\n      label: Address Verified\n      bounds:\n        x: 910\n        y: 896\n        width: 100\n        height: 20\n    - component_type: link\n      id: link_view_map\n      label: View Map\n      bounds:\n        x: 1020\n        y: 896\n        width: 60\n        height: 20\n    - component_type: input\n      id: input_latitude\n      label: Latitude*\n      value: '27.766685'\n      bounds:\n        x: 280\n        y: 940\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_longitude\n      label: Longitude*\n      value: \"-82.690887\"\n      bounds:\n        x: 450\n        y: 940\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_construction_type\n      label: Construction Type*\n      value: Masonry\n      bounds:\n        x: 280\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_occupancy\n      label: Occupancy*\n      value: Owner Occupied\n      bounds:\n        x: 450\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_months_occupied\n      label: Months Occupied*\n      value: 9 to 12 Months\n      bounds:\n        x: 620\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_resided_less_than_2_years\n      label: Has the Insured resided at the risk address for less than 2 years?*\n      value: 'Yes'\n      bounds:\n        x: 280\n        y: 1044\n        width: 150\n        height: 36\n    - component_type: text\n      id: title_prior_address\n      label: Prior Address\n      bounds:\n        x: 280\n        y: 1096\n        width: 100\n        height: 20\n    - component_type: input\n      id: input_prior_address_number\n      label: Number*\n      value: '18001'\n      bounds:\n        x: 280\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_direction\n      label: Direction\n      bounds:\n        x: 380\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_street\n      label: Street*\n      value: Avalon\n      bounds:\n        x: 480\n        y: 1128\n        width: 100\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_address_suffix\n      label: Suffix\n      value: Ln\n      bounds:\n        x: 600\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_post_dir\n      label: Post Dir\n      bounds:\n        x: 700\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_address_type\n      label: Type\n      bounds:\n        x: 800\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_number_2\n      label: Number\n      bounds:\n        x: 900\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: link\n      id: link_verify_address\n      label: Verify\n      bounds:\n        x: 910\n        y: 1188\n        width: 80\n        height: 20\n    - component_type: text\n      id: text_prior_address_verified\n      label: Address\n      bounds:\n        x: 1000\n        y: 1188\n        width: 100\n        height: 20\n```"]}, {"role": "model", "parts": ["```yaml\nbrowser_component:\n  tab_title: *********** - Guide...\n  url: ai.iscs.com/innovation\n  address_bar_focused: false\n  tab_state: idle\nwebpage:\n  header:\n  - component_type: container\n    id: main_header\n    bounds:\n      x: 0\n      y: 69\n      width: 1920\n      height: 61\n    subcomponents:\n    - component_type: image\n      id: logo_american_integrity\n      label: AMERICAN INTEGRITY\n      bounds:\n        x: 16\n        y: 116\n        width: 128\n        height: 40\n    - component_type: navigation_menu\n      id: top_navigation\n      bounds:\n        x: 1290\n        y: 116\n        width: 600\n        height: 36\n      subcomponents:\n      - component_type: link\n        id: link_home\n        label: Home\n        bounds:\n          x: 1298\n          y: 126\n          width: 40\n          height: 20\n      - component_type: link\n        id: link_quote_policy\n        label: Quote/Policy\n        state: active\n        bounds:\n          x: 1362\n          y: 126\n          width: 80\n          height: 20\n      - component_type: link\n        id: link_claims\n        label: Claims\n        bounds:\n          x: 1466\n          y: 126\n          width: 48\n          height: 20\n      - component_type: link\n        id: link_cabinets\n        label: Cabinets\n        bounds:\n          x: 1538\n          y: 126\n          width: 58\n          height: 20\n      - component_type: link\n        id: link_support\n        label: Support\n        bounds:\n          x: 1620\n          y: 126\n          width: 54\n          height: 20\n  sidebar:\n  - component_type: navigation_menu\n    id: left_navigation\n    bounds:\n      x: 0\n      y: 169\n      width: 256\n      height: 809\n    subcomponents:\n    - component_type: search_input\n      id: search_sidebar\n      label: Search\n      bounds:\n        x: 16\n        y: 180\n        width: 188\n        height: 36\n    - component_type: button_icon\n      id: btn_search\n      label: Search\n      bounds:\n        x: 204\n        y: 180\n        width: 36\n        height: 36\n    - component_type: text\n      id: text_advanced_search\n      label: 'ADVANCED SEARCH:'\n      bounds:\n        x: 16\n        y: 224\n        width: 100\n        height: 16\n    - component_type: link\n      id: link_policy_search\n      label: POLICY\n      bounds:\n        x: 120\n        y: 224\n        width: 40\n        height: 16\n    - component_type: link\n      id: link_claims_search\n      label: CLAIMS\n      bounds:\n        x: 170\n        y: 224\n        width: 45\n        height: 16\n    - component_type: dropdown\n      id: dropdown_quote\n      label: Quote\n      state: expanded\n      bounds:\n        x: 16\n        y: 252\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy\n      label: Policy\n      state: active\n      bounds:\n        x: 32\n        y: 284\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_dwelling\n      label: Dwelling\n      bounds:\n        x: 32\n        y: 316\n        width: 208\n        height: 32\n      subcomponents:\n      - component_type: badge\n        id: badge_dwelling_count\n        label: '2'\n        bounds:\n          x: 210\n          y: 322\n          width: 16\n          height: 20\n    - component_type: link\n      id: link_review\n      label: Review\n      bounds:\n        x: 32\n        y: 348\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_attachments\n      label: Attachments\n      bounds:\n        x: 16\n        y: 380\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_correspondence\n      label: Correspondence\n      bounds:\n        x: 16\n        y: 412\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_tasks\n      label: Tasks\n      bounds:\n        x: 16\n        y: 444\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_notes\n      label: Notes\n      bounds:\n        x: 16\n        y: 476\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy_file\n      label: Policy File\n      bounds:\n        x: 16\n        y: 508\n        width: 224\n        height: 32\n  - component_type: action_bar\n    id: right_action_bar\n    bounds:\n      x: 1888\n      y: 169\n      width: 32\n      height: 809\n    subcomponents:\n    - component_type: button_icon\n      id: btn_summary\n      label: SUMMARY\n      bounds:\n        x: 1888\n        y: 180\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_quick_qt\n      label: WTCROFT QUICK QT\n      bounds:\n        x: 1888\n        y: 230\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_quote\n      label: NEW QUOTE\n      bounds:\n        x: 1888\n        y: 280\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_note\n      label: NEW NOTE\n      bounds:\n        x: 1888\n        y: 330\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_attach\n      label: NEW ATTACH...\n      bounds:\n        x: 1888\n        y: 380\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_task\n      label: NEW TASK\n      bounds:\n        x: 1888\n        y: 430\n        width: 32\n        height: 50\n  main_content:\n  - component_type: container\n    id: quote_summary_header\n    bounds:\n      x: 256\n      y: 169\n      width: 1632\n      height: 80\n    subcomponents:\n    - component_type: button\n      id: btn_quote\n      label: QUOTE\n      bounds:\n        x: 280\n        y: 180\n        width: 80\n        height: 36\n    - component_type: text\n      id: text_quote_number_label\n      label: Quote Number\n      bounds:\n        x: 376\n        y: 180\n        width: 80\n        height: 16\n    - component_type: text\n      id: text_quote_number_value\n      label: ***********\n      bounds:\n        x: 376\n        y: 198\n        width: 80\n        height: 16\n    - component_type: text\n      id: text_insured_label\n      label: Insured\n      bounds:\n        x: 472\n        y: 180\n        width: 80\n        height: 16\n    - component_type: link\n      id: link_insured_name\n      label: Landon Cassidy\n      bounds:\n        x: 472\n        y: 198\n        width: 90\n        height: 16\n    - component_type: text\n      id: text_product_label\n      label: Product\n      bounds:\n        x: 578\n        y: 180\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_product_value\n      label: Voluntary Homeowners (HO3)\n      bounds:\n        x: 578\n        y: 198\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_sub_type_label\n      label: Sub Type\n      bounds:\n        x: 744\n        y: 180\n        width: 50\n        height: 16\n    - component_type: text\n      id: text_sub_type_value\n      label: HO3\n      bounds:\n        x: 744\n        y: 198\n        width: 30\n        height: 16\n    - component_type: text\n      id: text_policy_term_label\n      label: Policy Term\n      bounds:\n        x: 810\n        y: 180\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_policy_term_value\n      label: 06/20/2025 - 06/20/2026\n      bounds:\n        x: 810\n        y: 198\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_producer_label\n      label: Producer\n      bounds:\n        x: 976\n        y: 180\n        width: 120\n        height: 16\n    - component_type: link\n      id: link_producer_name\n      label: HH Insurance Group, LLC\n      bounds:\n        x: 976\n        y: 198\n        width: 120\n        height: 16\n    - component_type: text\n      id: text_status_label\n      label: Status\n      bounds:\n        x: 1112\n        y: 180\n        width: 60\n        height: 16\n    - component_type: text\n      id: text_status_value\n      label: In Process\n      bounds:\n        x: 1112\n        y: 198\n        width: 60\n        height: 16\n    - component_type: text\n      id: text_premium_fees_label\n      label: Premium + Fees\n      bounds:\n        x: 1188\n        y: 180\n        width: 90\n        height: 16\n    - component_type: text\n      id: text_premium_fees_value\n      label: $17,776.90\n      bounds:\n        x: 1188\n        y: 198\n        width: 70\n        height: 16\n  - component_type: container\n    id: policy_general_form\n    bounds:\n      x: 256\n      y: 249\n      width: 1632\n      height: 831\n    subcomponents:\n    - component_type: link\n      id: link_return_to_home\n      label: < Return to Home\n      bounds:\n        x: 280\n        y: 260\n        width: 100\n        height: 20\n    - component_type: button\n      id: btn_next_page\n      label: NEXT PAGE\n      bounds:\n        x: 1290\n        y: 220\n        width: 90\n        height: 28\n    - component_type: button\n      id: btn_save\n      label: SAVE\n      bounds:\n        x: 1390\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_copy\n      label: COPY\n      bounds:\n        x: 1460\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_print\n      label: PRINT\n      bounds:\n        x: 1530\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_create_application\n      label: CREATE APPLICATION\n      bounds:\n        x: 1600\n        y: 220\n        width: 150\n        height: 28\n    - component_type: button\n      id: btn_discard_changes\n      label: DISCARD CHANGES\n      bounds:\n        x: 1760\n        y: 220\n        width: 130\n        height: 28\n    - component_type: button\n      id: btn_view_notes\n      label: VIEW NOTES\n      bounds:\n        x: 1290\n        y: 250\n        width: 100\n        height: 28\n    - component_type: button\n      id: btn_delete\n      label: DELETE\n      bounds:\n        x: 1400\n        y: 250\n        width: 70\n        height: 28\n    - component_type: button\n      id: btn_more\n      label: '... MORE'\n      bounds:\n        x: 1480\n        y: 250\n        width: 70\n        height: 28\n    - component_type: text\n      id: title_policy_general\n      label: Policy General\n      bounds:\n        x: 280\n        y: 292\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_product\n      label: Product*\n      value: Florida - Voluntary Homeowners (HO3) - American Integrity Insurance Group\n      bounds:\n        x: 280\n        y: 324\n        width: 400\n        height: 36\n    - component_type: input\n      id: input_effective_date\n      label: Effective Date*\n      value: 06/20/2025\n      bounds:\n        x: 280\n        y: 376\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_producer_code\n      label: 'Producer: Code*'\n      value: AG8529A1\n      bounds:\n        x: 280\n        y: 428\n        width: 150\n        height: 36\n    - component_type: text\n      id: text_producer_name\n      label: HH Insurance Group, LLC\n      bounds:\n        x: 470\n        y: 438\n        width: 150\n        height: 20\n    - component_type: text\n      id: title_prior_carrier\n      label: Prior Carrier Details\n      bounds:\n        x: 280\n        y: 480\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_prior_carrier\n      label: Prior Carrier*\n      value: New Purchase\n      bounds:\n        x: 280\n        y: 512\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_prior_policy_expiration\n      label: Prior Policy Expiration Date\n      bounds:\n        x: 700\n        y: 512\n        width: 200\n        height: 36\n    - component_type: text\n      id: title_insured_info\n      label: Insured Information\n      bounds:\n        x: 280\n        y: 564\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_entity_type\n      label: Entity Type*\n      value: Individual\n      bounds:\n        x: 280\n        y: 596\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_first_name\n      label: First*\n      value: Landon\n      bounds:\n        x: 500\n        y: 596\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_middle_name\n      label: Middle\n      bounds:\n        x: 670\n        y: 596\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_last_name\n      label: Last*\n      value: Cassidy\n      bounds:\n        x: 840\n        y: 596\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_suffix\n      label: Suffix\n      bounds:\n        x: 1010\n        y: 596\n        width: 100\n        height: 36\n    - component_type: input\n      id: input_dob\n      label: DOB*\n      value: 05/20/1998\n      bounds:\n        x: 280\n        y: 648\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_insurance_score\n      label: Insurance Score*\n      value: Excellent (850-999)\n      bounds:\n        x: 500\n        y: 648\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_search_name\n      label: Search Name*\n      value: Landon Cassidy\n      bounds:\n        x: 280\n        y: 700\n        width: 200\n        height: 36\n    - component_type: link\n      id: link_reset\n      label: Reset\n      state: hovered\n      bounds:\n        x: 490\n        y: 708\n        width: 40\n        height: 20\n    - component_type: dropdown\n      id: dropdown_primary_phone\n      label: Primary Phone\n      value: Select...\n      bounds:\n        x: 280\n        y: 752\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_email\n      label: Email\n      bounds:\n        x: 500\n        y: 752\n        width: 200\n        height: 36\n    - component_type: checkbox\n      id: checkbox_no_email\n      label: No Email\n      bounds:\n        x: 720\n        y: 760\n        width: 80\n        height: 20\n    - component_type: text\n      id: title_dwelling_info\n      label: Dwelling Information\n      bounds:\n        x: 280\n        y: 804\n        width: 150\n        height: 20\n    - component_type: input\n      id: input_lookup_address\n      label: Lookup Address\n      bounds:\n        x: 280\n        y: 836\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_number\n      label: Number*\n      value: '4227'\n      bounds:\n        x: 500\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_direction\n      label: Direction\n      bounds:\n        x: 600\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_street\n      label: Street*\n      value: 5th\n      bounds:\n        x: 700\n        y: 836\n        width: 100\n        height: 36\n    - component_type: dropdown\n      id: dropdown_suffix\n      label: Suffix\n      value: Ave\n      bounds:\n        x: 820\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_post_dir\n      label: Post Dir\n      value: S\n      bounds:\n        x: 920\n        y: 836\n        width: 80\n        height: 36\n    - component_type: dropdown\n      id: dropdown_type\n      label: Type\n      bounds:\n        x: 1020\n        y: 836\n        width: 80\n        height: 36\n    - component_type: checkbox\n      id: checkbox_ignore_address_validation\n      label: Ignore Address Validation\n      bounds:\n        x: 1120\n        y: 820\n        width: 150\n        height: 20\n    - component_type: input\n      id: input_number_2\n      label: Number\n      bounds:\n        x: 1120\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_city\n      label: City*\n      value: St Petersburg\n      bounds:\n        x: 280\n        y: 888\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_county\n      label: County*\n      value: Pinellas\n      bounds:\n        x: 450\n        y: 888\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_state\n      label: State*\n      value: Florida\n      bounds:\n        x: 620\n        y: 888\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_zip\n      label: Zip*\n      value: 33711-1522\n      bounds:\n        x: 790\n        y: 888\n        width: 100\n        height: 36\n    - component_type: text\n      id: text_address_verified\n      label: Address Verified\n      bounds:\n        x: 910\n        y: 896\n        width: 100\n        height: 20\n    - component_type: link\n      id: link_view_map\n      label: View Map\n      bounds:\n        x: 1020\n        y: 896\n        width: 60\n        height: 20\n    - component_type: input\n      id: input_latitude\n      label: Latitude*\n      value: '27.766685'\n      bounds:\n        x: 280\n        y: 940\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_longitude\n      label: Longitude*\n      value: \"-82.690887\"\n      bounds:\n        x: 450\n        y: 940\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_construction_type\n      label: Construction Type*\n      value: Masonry\n      bounds:\n        x: 280\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_occupancy\n      label: Occupancy*\n      value: Owner Occupied\n      bounds:\n        x: 450\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_months_occupied\n      label: Months Occupied*\n      value: 9 to 12 Months\n      bounds:\n        x: 620\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_resided_less_than_2_years\n      label: Has the Insured resided at the risk address for less than 2 years?*\n      value: 'Yes'\n      bounds:\n        x: 280\n        y: 1044\n        width: 150\n        height: 36\n    - component_type: text\n      id: title_prior_address\n      label: Prior Address\n      bounds:\n        x: 280\n        y: 1096\n        width: 100\n        height: 20\n    - component_type: input\n      id: input_prior_address_number\n      label: Number*\n      value: '18001'\n      bounds:\n        x: 280\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_direction\n      label: Direction\n      bounds:\n        x: 380\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_street\n      label: Street*\n      value: Avalon\n      bounds:\n        x: 480\n        y: 1128\n        width: 100\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_address_suffix\n      label: Suffix\n      value: Ln\n      bounds:\n        x: 600\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_post_dir\n      label: Post Dir\n      bounds:\n        x: 700\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_address_type\n      label: Type\n      bounds:\n        x: 800\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_number_2\n      label: Number\n      bounds:\n        x: 900\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: link\n      id: link_verify_address\n      label: Verify\n      bounds:\n        x: 910\n        y: 1188\n        width: 80\n        height: 20\n    - component_type: text\n      id: text_prior_address_verified\n      label: Address\n      bounds:\n        x: 1000\n        y: 1188\n        width: 100\n        height: 20\n  footer:\n  - component_type: container\n    id: main_footer\n    bounds:\n      x: 0\n      y: 1050\n      width: 1920\n      height: 30\n    subcomponents:\n    - component_type: text\n      id: text_powered_by\n      label: Powered by GUIDEWIRE\n      bounds:\n        x: 280\n        y: 1058\n        width: 150\n        height: 20\n    - component_type: text\n      id: text_environment\n      label: 'Environment : PROD AIIG'\n      bounds:\n        x: 1200\n        y: 1058\n        width: 150\n        height: 20\n    - component_type: text\n      id: text_current_logon\n      label: 'Current Logon : AG8529A (Sign Out)'\n      bounds:\n        x: 1400\n        y: 1058\n        width: 200\n        height: 20\n    - component_type: text\n      id: text_posting_date\n      label: 'Posting Date : 5/29/2025'\n      bounds:\n        x: 1650\n        y: 1058\n        width: 150\n        height: 20\n```"]}, {"role": "model", "parts": ["```yaml\nbrowser_component:\n  tab_title: *********** - Guidewire PolicyCenter\n  url: ai.iscs.com/innovation\n  address_bar_focused: false\n  tab_state: idle\nwebpage:\n  header:\n  - component_type: container\n    id: main_header\n    bounds:\n      x: 0\n      y: 69\n      width: 1920\n      height: 61\n    subcomponents:\n    - component_type: image\n      id: logo_american_integrity\n      label: AMERICAN INTEGRITY\n      bounds:\n        x: 16\n        y: 116\n        width: 128\n        height: 40\n    - component_type: navigation_menu\n      id: top_navigation\n      bounds:\n        x: 1290\n        y: 116\n        width: 600\n        height: 36\n      subcomponents:\n      - component_type: link\n        id: link_home\n        label: Home\n        bounds:\n          x: 1298\n          y: 126\n          width: 40\n          height: 20\n      - component_type: link\n        id: link_quote_policy\n        label: Quote/Policy\n        state: active\n        bounds:\n          x: 1362\n          y: 126\n          width: 80\n          height: 20\n      - component_type: link\n        id: link_claims\n        label: Claims\n        bounds:\n          x: 1466\n          y: 126\n          width: 48\n          height: 20\n      - component_type: link\n        id: link_cabinets\n        label: Cabinets\n        bounds:\n          x: 1538\n          y: 126\n          width: 58\n          height: 20\n      - component_type: link\n        id: link_support\n        label: Support\n        bounds:\n          x: 1620\n          y: 126\n          width: 54\n          height: 20\n  sidebar:\n  - component_type: navigation_menu\n    id: left_navigation\n    bounds:\n      x: 0\n      y: 169\n      width: 256\n      height: 809\n    subcomponents:\n    - component_type: search_input\n      id: search_sidebar\n      label: Search\n      bounds:\n        x: 16\n        y: 180\n        width: 188\n        height: 36\n    - component_type: button_icon\n      id: btn_search\n      label: Search\n      bounds:\n        x: 204\n        y: 180\n        width: 36\n        height: 36\n    - component_type: text\n      id: text_advanced_search\n      label: 'ADVANCED SEARCH:'\n      bounds:\n        x: 16\n        y: 224\n        width: 100\n        height: 16\n    - component_type: link\n      id: link_policy_search\n      label: POLICY\n      bounds:\n        x: 120\n        y: 224\n        width: 40\n        height: 16\n    - component_type: link\n      id: link_claims_search\n      label: CLAIMS\n      bounds:\n        x: 170\n        y: 224\n        width: 45\n        height: 16\n    - component_type: dropdown\n      id: dropdown_quote\n      label: Quote\n      state: expanded\n      bounds:\n        x: 16\n        y: 252\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy\n      label: Policy\n      state: active\n      bounds:\n        x: 32\n        y: 284\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_dwelling\n      label: Dwelling\n      bounds:\n        x: 32\n        y: 316\n        width: 208\n        height: 32\n      subcomponents:\n      - component_type: badge\n        id: badge_dwelling_count\n        label: '2'\n        bounds:\n          x: 210\n          y: 322\n          width: 16\n          height: 20\n    - component_type: link\n      id: link_review\n      label: Review\n      bounds:\n        x: 32\n        y: 348\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_attachments\n      label: Attachments\n      bounds:\n        x: 16\n        y: 380\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_correspondence\n      label: Correspondence\n      bounds:\n        x: 16\n        y: 412\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_tasks\n      label: Tasks\n      bounds:\n        x: 16\n        y: 444\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_notes\n      label: Notes\n      bounds:\n        x: 16\n        y: 476\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy_file\n      label: Policy File\n      bounds:\n        x: 16\n        y: 508\n        width: 224\n        height: 32\n  - component_type: action_bar\n    id: right_action_bar\n    bounds:\n      x: 1888\n      y: 169\n      width: 32\n      height: 809\n    subcomponents:\n    - component_type: button_icon\n      id: btn_summary\n      label: SUMMARY\n      bounds:\n        x: 1888\n        y: 180\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_quick_qt\n      label: WTCROFT QUICK QT\n      bounds:\n        x: 1888\n        y: 230\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_quote\n      label: NEW QUOTE\n      bounds:\n        x: 1888\n        y: 280\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_note\n      label: NEW NOTE\n      bounds:\n        x: 1888\n        y: 330\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_attach\n      label: NEW ATTACH...\n      bounds:\n        x: 1888\n        y: 380\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_task\n      label: NEW TASK\n      bounds:\n        x: 1888\n        y: 430\n        width: 32\n        height: 50\n  main_content:\n  - component_type: container\n    id: quote_summary_header\n    bounds:\n      x: 256\n      y: 169\n      width: 1632\n      height: 80\n    subcomponents:\n    - component_type: button\n      id: btn_quote\n      label: QUOTE\n      bounds:\n        x: 280\n        y: 180\n        width: 80\n        height: 36\n    - component_type: text\n      id: text_quote_number_label\n      label: Quote Number\n      bounds:\n        x: 376\n        y: 180\n        width: 80\n        height: 16\n    - component_type: text\n      id: text_quote_number_value\n      label: ***********\n      bounds:\n        x: 376\n        y: 198\n        width: 80\n        height: 16\n    - component_type: text\n      id: text_insured_label\n      label: Insured\n      bounds:\n        x: 472\n        y: 180\n        width: 80\n        height: 16\n    - component_type: link\n      id: link_insured_name\n      label: Landon Cassidy\n      bounds:\n        x: 472\n        y: 198\n        width: 90\n        height: 16\n    - component_type: text\n      id: text_product_label\n      label: Product\n      bounds:\n        x: 578\n        y: 180\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_product_value\n      label: Voluntary Homeowners (HO3)\n      bounds:\n        x: 578\n        y: 198\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_sub_type_label\n      label: Sub Type\n      bounds:\n        x: 744\n        y: 180\n        width: 50\n        height: 16\n    - component_type: text\n      id: text_sub_type_value\n      label: HO3\n      bounds:\n        x: 744\n        y: 198\n        width: 30\n        height: 16\n    - component_type: text\n      id: text_policy_term_label\n      label: Policy Term\n      bounds:\n        x: 810\n        y: 180\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_policy_term_value\n      label: 06/20/2025 - 06/20/2026\n      bounds:\n        x: 810\n        y: 198\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_producer_label\n      label: Producer\n      bounds:\n        x: 976\n        y: 180\n        width: 120\n        height: 16\n    - component_type: link\n      id: link_producer_name\n      label: HH Insurance Group, LLC\n      bounds:\n        x: 976\n        y: 198\n        width: 120\n        height: 16\n    - component_type: text\n      id: text_status_label\n      label: Status\n      bounds:\n        x: 1112\n        y: 180\n        width: 60\n        height: 16\n    - component_type: text\n      id: text_status_value\n      label: In Process\n      bounds:\n        x: 1112\n        y: 198\n        width: 60\n        height: 16\n    - component_type: text\n      id: text_premium_fees_label\n      label: Premium + Fees\n      bounds:\n        x: 1188\n        y: 180\n        width: 90\n        height: 16\n    - component_type: text\n      id: text_premium_fees_value\n      label: $17,776.90\n      bounds:\n        x: 1188\n        y: 198\n        width: 70\n        height: 16\n  - component_type: container\n    id: policy_general_form\n    bounds:\n      x: 256\n      y: 249\n      width: 1632\n      height: 729\n    subcomponents:\n    - component_type: link\n      id: link_return_to_home\n      label: < Return to Home\n      bounds:\n        x: 280\n        y: 260\n        width: 100\n        height: 20\n    - component_type: button\n      id: btn_next_page\n      label: NEXT PAGE\n      bounds:\n        x: 1290\n        y: 220\n        width: 90\n        height: 28\n    - component_type: button\n      id: btn_save\n      label: SAVE\n      bounds:\n        x: 1390\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_copy\n      label: COPY\n      bounds:\n        x: 1460\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_print\n      label: PRINT\n      bounds:\n        x: 1530\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_create_application\n      label: CREATE APPLICATION\n      bounds:\n        x: 1600\n        y: 220\n        width: 150\n        height: 28\n    - component_type: button\n      id: btn_discard_changes\n      label: DISCARD CHANGES\n      bounds:\n        x: 1760\n        y: 220\n        width: 130\n        height: 28\n    - component_type: button\n      id: btn_view_notes\n      label: VIEW NOTES\n      bounds:\n        x: 1290\n        y: 250\n        width: 100\n        height: 28\n    - component_type: button\n      id: btn_delete\n      label: DELETE\n      bounds:\n        x: 1400\n        y: 250\n        width: 70\n        height: 28\n    - component_type: button\n      id: btn_more\n      label: '... MORE'\n      bounds:\n        x: 1480\n        y: 250\n        width: 70\n        height: 28\n    - component_type: text\n      id: title_policy_general\n      label: Policy General\n      bounds:\n        x: 280\n        y: 292\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_product\n      label: Product*\n      value: Florida - Voluntary Homeowners (HO3) - American Integrity Insurance Group\n      bounds:\n        x: 280\n        y: 324\n        width: 400\n        height: 36\n    - component_type: input\n      id: input_effective_date\n      label: Effective Date*\n      value: 06/20/2025\n      bounds:\n        x: 280\n        y: 376\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_producer_code\n      label: 'Producer: Code*'\n      value: AG8529A1\n      bounds:\n        x: 280\n        y: 428\n        width: 150\n        height: 36\n    - component_type: text\n      id: text_producer_name\n      label: HH Insurance Group, LLC\n      bounds:\n        x: 470\n        y: 438\n        width: 150\n        height: 20\n    - component_type: text\n      id: title_prior_carrier\n      label: Prior Carrier Details\n      bounds:\n        x: 280\n        y: 480\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_prior_carrier\n      label: Prior Carrier*\n      value: New Purchase\n      bounds:\n        x: 280\n        y: 512\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_prior_policy_expiration\n      label: Prior Policy Expiration Date\n      bounds:\n        x: 700\n        y: 512\n        width: 200\n        height: 36\n    - component_type: text\n      id: title_insured_info\n      label: Insured Information\n      bounds:\n        x: 280\n        y: 564\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_entity_type\n      label: Entity Type*\n      value: Individual\n      bounds:\n        x: 280\n        y: 596\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_first_name\n      label: First*\n      value: Landon\n      bounds:\n        x: 500\n        y: 596\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_middle_name\n      label: Middle\n      bounds:\n        x: 670\n        y: 596\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_last_name\n      label: Last*\n      value: Cassidy\n      bounds:\n        x: 840\n        y: 596\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_suffix\n      label: Suffix\n      bounds:\n        x: 1010\n        y: 596\n        width: 100\n        height: 36\n    - component_type: input\n      id: input_dob\n      label: DOB*\n      value: 05/20/1998\n      bounds:\n        x: 280\n        y: 648\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_insurance_score\n      label: Insurance Score*\n      value: Excellent (850-999)\n      bounds:\n        x: 500\n        y: 648\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_search_name\n      label: Search Name*\n      value: Landon Cassidy\n      bounds:\n        x: 280\n        y: 700\n        width: 200\n        height: 36\n    - component_type: link\n      id: link_reset\n      label: Reset\n      bounds:\n        x: 490\n        y: 708\n        width: 40\n        height: 20\n    - component_type: dropdown\n      id: dropdown_primary_phone\n      label: Primary Phone\n      value: Select...\n      bounds:\n        x: 280\n        y: 752\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_email\n      label: Email\n      bounds:\n        x: 500\n        y: 752\n        width: 200\n        height: 36\n    - component_type: checkbox\n      id: checkbox_no_email\n      label: No Email\n      bounds:\n        x: 720\n        y: 760\n        width: 80\n        height: 20\n    - component_type: text\n      id: title_dwelling_info\n      label: Dwelling Information\n      bounds:\n        x: 280\n        y: 804\n        width: 150\n        height: 20\n    - component_type: input\n      id: input_lookup_address\n      label: Lookup Address\n      bounds:\n        x: 280\n        y: 836\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_number\n      label: Number*\n      value: '4227'\n      bounds:\n        x: 500\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_direction\n      label: Direction\n      bounds:\n        x: 600\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_street\n      label: Street*\n      value: 5th\n      bounds:\n        x: 700\n        y: 836\n        width: 100\n        height: 36\n    - component_type: dropdown\n      id: dropdown_suffix\n      label: Suffix\n      value: Ave\n      bounds:\n        x: 820\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_post_dir\n      label: Post Dir\n      value: S\n      bounds:\n        x: 920\n        y: 836\n        width: 80\n        height: 36\n    - component_type: dropdown\n      id: dropdown_type\n      label: Type\n      bounds:\n        x: 1020\n        y: 836\n        width: 80\n        height: 36\n    - component_type: checkbox\n      id: checkbox_ignore_address_validation\n      label: Ignore Address Validation\n      bounds:\n        x: 1120\n        y: 820\n        width: 150\n        height: 20\n    - component_type: input\n      id: input_number_2\n      label: Number\n      bounds:\n        x: 1120\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_city\n      label: City*\n      value: St Petersburg\n      bounds:\n        x: 280\n        y: 888\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_county\n      label: County*\n      value: Pinellas\n      bounds:\n        x: 450\n        y: 888\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_state\n      label: State*\n      value: Florida\n      bounds:\n        x: 620\n        y: 888\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_zip\n      label: Zip*\n      value: 33711-1522\n      bounds:\n        x: 790\n        y: 888\n        width: 100\n        height: 36\n    - component_type: text\n      id: text_address_verified\n      label: Address Verified\n      bounds:\n        x: 910\n        y: 896\n        width: 100\n        height: 20\n    - component_type: link\n      id: link_view_map\n      label: View Map\n      bounds:\n        x: 1020\n        y: 896\n        width: 60\n        height: 20\n    - component_type: input\n      id: input_latitude\n      label: Latitude*\n      value: '27.766685'\n      bounds:\n        x: 280\n        y: 940\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_longitude\n      label: Longitude*\n      value: \"-82.690887\"\n      bounds:\n        x: 450\n        y: 940\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_construction_type\n      label: Construction Type*\n      value: Masonry\n      bounds:\n        x: 280\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_occupancy\n      label: Occupancy*\n      value: Owner Occupied\n      bounds:\n        x: 450\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_months_occupied\n      label: Months Occupied*\n      value: 9 to 12 Months\n      bounds:\n        x: 620\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_resided_less_than_2_years\n      label: Has the Insured resided at the risk address for less than 2 years?*\n      value: 'Yes'\n      bounds:\n        x: 280\n        y: 1044\n        width: 150\n        height: 36\n    - component_type: text\n      id: title_prior_address\n      label: Prior Address\n      bounds:\n        x: 280\n        y: 1096\n        width: 100\n        height: 20\n    - component_type: input\n      id: input_prior_address_number\n      label: Number*\n      value: '18001'\n      bounds:\n        x: 280\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_direction\n      label: Direction\n      bounds:\n        x: 380\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_street\n      label: Street*\n      value: Avalon\n      bounds:\n        x: 480\n        y: 1128\n        width: 100\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_address_suffix\n      label: Suffix\n      value: Ln\n      bounds:\n        x: 600\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_post_dir\n      label: Post Dir\n      bounds:\n        x: 700\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_address_type\n      label: Type\n      bounds:\n        x: 800\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_number_2\n      label: Number\n      bounds:\n        x: 900\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: link\n      id: link_verify_address\n      label: Verify\n      bounds:\n        x: 910\n        y: 1188\n        width: 80\n        height: 20\n    - component_type: text\n      id: text_prior_address_verified\n      label: Address\n      bounds:\n        x: 1000\n        y: 1188\n        width: 100\n        height: 20\n```"]}, {"role": "model", "parts": ["```yaml\nbrowser_component:\n  tab_title: *********** - Guidewire PolicyCenter\n  url: ai.iscs.com/innovation\n  address_bar_focused: false\n  tab_state: idle\nwebpage:\n  header:\n  - component_type: container\n    id: main_header\n    bounds:\n      x: 0\n      y: 69\n      width: 1920\n      height: 61\n    subcomponents:\n    - component_type: image\n      id: logo_american_integrity\n      label: AMERICAN INTEGRITY\n      bounds:\n        x: 16\n        y: 116\n        width: 128\n        height: 40\n    - component_type: navigation_menu\n      id: top_navigation\n      bounds:\n        x: 1290\n        y: 116\n        width: 600\n        height: 36\n      subcomponents:\n      - component_type: link\n        id: link_home\n        label: Home\n        bounds:\n          x: 1298\n          y: 126\n          width: 40\n          height: 20\n      - component_type: link\n        id: link_quote_policy\n        label: Quote/Policy\n        state: active\n        bounds:\n          x: 1362\n          y: 126\n          width: 80\n          height: 20\n      - component_type: link\n        id: link_claims\n        label: Claims\n        bounds:\n          x: 1466\n          y: 126\n          width: 48\n          height: 20\n      - component_type: link\n        id: link_cabinets\n        label: Cabinets\n        bounds:\n          x: 1538\n          y: 126\n          width: 58\n          height: 20\n      - component_type: link\n        id: link_support\n        label: Support\n        bounds:\n          x: 1620\n          y: 126\n          width: 54\n          height: 20\n  sidebar:\n  - component_type: navigation_menu\n    id: left_navigation\n    bounds:\n      x: 0\n      y: 169\n      width: 256\n      height: 809\n    subcomponents:\n    - component_type: search_input\n      id: search_sidebar\n      label: Search\n      bounds:\n        x: 16\n        y: 180\n        width: 188\n        height: 36\n    - component_type: button_icon\n      id: btn_search\n      label: Search\n      bounds:\n        x: 204\n        y: 180\n        width: 36\n        height: 36\n    - component_type: text\n      id: text_advanced_search\n      label: 'ADVANCED SEARCH:'\n      bounds:\n        x: 16\n        y: 224\n        width: 100\n        height: 16\n    - component_type: link\n      id: link_policy_search\n      label: POLICY\n      bounds:\n        x: 120\n        y: 224\n        width: 40\n        height: 16\n    - component_type: link\n      id: link_claims_search\n      label: CLAIMS\n      bounds:\n        x: 170\n        y: 224\n        width: 45\n        height: 16\n    - component_type: dropdown\n      id: dropdown_quote\n      label: Quote\n      state: expanded\n      bounds:\n        x: 16\n        y: 252\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy\n      label: Policy\n      state: active\n      bounds:\n        x: 32\n        y: 284\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_dwelling\n      label: Dwelling\n      bounds:\n        x: 32\n        y: 316\n        width: 208\n        height: 32\n      subcomponents:\n      - component_type: badge\n        id: badge_dwelling_count\n        label: '2'\n        bounds:\n          x: 210\n          y: 322\n          width: 16\n          height: 20\n    - component_type: link\n      id: link_review\n      label: Review\n      bounds:\n        x: 32\n        y: 348\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_attachments\n      label: Attachments\n      bounds:\n        x: 16\n        y: 380\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_correspondence\n      label: Correspondence\n      bounds:\n        x: 16\n        y: 412\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_tasks\n      label: Tasks\n      bounds:\n        x: 16\n        y: 444\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_notes\n      label: Notes\n      bounds:\n        x: 16\n        y: 476\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy_file\n      label: Policy File\n      bounds:\n        x: 16\n        y: 508\n        width: 224\n        height: 32\n  - component_type: action_bar\n    id: right_action_bar\n    bounds:\n      x: 1888\n      y: 169\n      width: 32\n      height: 809\n    subcomponents:\n    - component_type: button_icon\n      id: btn_summary\n      label: SUMMARY\n      bounds:\n        x: 1888\n        y: 180\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_quick_qt\n      label: WTCROFT QUICK QT\n      bounds:\n        x: 1888\n        y: 230\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_quote\n      label: NEW QUOTE\n      bounds:\n        x: 1888\n        y: 280\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_note\n      label: NEW NOTE\n      bounds:\n        x: 1888\n        y: 330\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_attach\n      label: NEW ATTACH...\n      bounds:\n        x: 1888\n        y: 380\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_task\n      label: NEW TASK\n      bounds:\n        x: 1888\n        y: 430\n        width: 32\n        height: 50\n  main_content:\n  - component_type: container\n    id: quote_summary_header\n    bounds:\n      x: 256\n      y: 169\n      width: 1632\n      height: 80\n    subcomponents:\n    - component_type: button\n      id: btn_quote\n      label: QUOTE\n      bounds:\n        x: 280\n        y: 180\n        width: 80\n        height: 36\n    - component_type: text\n      id: text_quote_number_label\n      label: Quote Number\n      bounds:\n        x: 376\n        y: 180\n        width: 80\n        height: 16\n    - component_type: text\n      id: text_quote_number_value\n      label: ***********\n      bounds:\n        x: 376\n        y: 198\n        width: 80\n        height: 16\n    - component_type: text\n      id: text_insured_label\n      label: Insured\n      bounds:\n        x: 472\n        y: 180\n        width: 80\n        height: 16\n    - component_type: link\n      id: link_insured_name\n      label: Landon Cassidy\n      bounds:\n        x: 472\n        y: 198\n        width: 90\n        height: 16\n    - component_type: text\n      id: text_product_label\n      label: Product\n      bounds:\n        x: 578\n        y: 180\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_product_value\n      label: Voluntary Homeowners (HO3)\n      bounds:\n        x: 578\n        y: 198\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_sub_type_label\n      label: Sub Type\n      bounds:\n        x: 744\n        y: 180\n        width: 50\n        height: 16\n    - component_type: text\n      id: text_sub_type_value\n      label: HO3\n      bounds:\n        x: 744\n        y: 198\n        width: 30\n        height: 16\n    - component_type: text\n      id: text_policy_term_label\n      label: Policy Term\n      bounds:\n        x: 810\n        y: 180\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_policy_term_value\n      label: 06/20/2025 - 06/20/2026\n      bounds:\n        x: 810\n        y: 198\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_producer_label\n      label: Producer\n      bounds:\n        x: 976\n        y: 180\n        width: 120\n        height: 16\n    - component_type: link\n      id: link_producer_name\n      label: HH Insurance Group, LLC\n      bounds:\n        x: 976\n        y: 198\n        width: 120\n        height: 16\n    - component_type: text\n      id: text_status_label\n      label: Status\n      bounds:\n        x: 1112\n        y: 180\n        width: 60\n        height: 16\n    - component_type: text\n      id: text_status_value\n      label: In Process\n      bounds:\n        x: 1112\n        y: 198\n        width: 60\n        height: 16\n    - component_type: text\n      id: text_premium_fees_label\n      label: Premium + Fees\n      bounds:\n        x: 1188\n        y: 180\n        width: 90\n        height: 16\n    - component_type: text\n      id: text_premium_fees_value\n      label: $17,776.90\n      bounds:\n        x: 1188\n        y: 198\n        width: 70\n        height: 16\n  - component_type: container\n    id: policy_general_form\n    bounds:\n      x: 256\n      y: 249\n      width: 1632\n      height: 729\n    subcomponents:\n    - component_type: link\n      id: link_return_to_home\n      label: < Return to Home\n      bounds:\n        x: 280\n        y: 260\n        width: 100\n        height: 20\n    - component_type: button\n      id: btn_next_page\n      label: NEXT PAGE\n      bounds:\n        x: 1290\n        y: 220\n        width: 90\n        height: 28\n    - component_type: button\n      id: btn_save\n      label: SAVE\n      bounds:\n        x: 1390\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_copy\n      label: COPY\n      bounds:\n        x: 1460\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_print\n      label: PRINT\n      bounds:\n        x: 1530\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_create_application\n      label: CREATE APPLICATION\n      bounds:\n        x: 1600\n        y: 220\n        width: 150\n        height: 28\n    - component_type: button\n      id: btn_discard_changes\n      label: DISCARD CHANGES\n      bounds:\n        x: 1760\n        y: 220\n        width: 130\n        height: 28\n    - component_type: button\n      id: btn_view_notes\n      label: VIEW NOTES\n      bounds:\n        x: 1290\n        y: 250\n        width: 100\n        height: 28\n    - component_type: button\n      id: btn_delete\n      label: DELETE\n      bounds:\n        x: 1400\n        y: 250\n        width: 70\n        height: 28\n    - component_type: button\n      id: btn_more\n      label: '... MORE'\n      bounds:\n        x: 1480\n        y: 250\n        width: 70\n        height: 28\n    - component_type: text\n      id: title_policy_general\n      label: Policy General\n      bounds:\n        x: 280\n        y: 292\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_product\n      label: Product*\n      value: Florida - Voluntary Homeowners (HO3) - American Integrity Insurance Group\n      bounds:\n        x: 280\n        y: 324\n        width: 400\n        height: 36\n    - component_type: input\n      id: input_effective_date\n      label: Effective Date*\n      value: 06/20/2025\n      bounds:\n        x: 280\n        y: 376\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_producer_code\n      label: 'Producer: Code*'\n      value: AG8529A1\n      bounds:\n        x: 280\n        y: 428\n        width: 150\n        height: 36\n    - component_type: text\n      id: text_producer_name\n      label: HH Insurance Group, LLC\n      bounds:\n        x: 470\n        y: 438\n        width: 150\n        height: 20\n    - component_type: text\n      id: title_prior_carrier\n      label: Prior Carrier Details\n      bounds:\n        x: 280\n        y: 480\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_prior_carrier\n      label: Prior Carrier*\n      value: New Purchase\n      bounds:\n        x: 280\n        y: 512\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_prior_policy_expiration\n      label: Prior Policy Expiration Date\n      bounds:\n        x: 700\n        y: 512\n        width: 200\n        height: 36\n    - component_type: text\n      id: title_insured_info\n      label: Insured Information\n      bounds:\n        x: 280\n        y: 564\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_entity_type\n      label: Entity Type*\n      value: Individual\n      bounds:\n        x: 280\n        y: 596\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_first_name\n      label: First*\n      value: Landon\n      bounds:\n        x: 500\n        y: 596\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_middle_name\n      label: Middle\n      bounds:\n        x: 670\n        y: 596\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_last_name\n      label: Last*\n      value: Cassidy\n      bounds:\n        x: 840\n        y: 596\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_suffix\n      label: Suffix\n      bounds:\n        x: 1010\n        y: 596\n        width: 100\n        height: 36\n    - component_type: input\n      id: input_dob\n      label: DOB*\n      value: 05/20/1998\n      bounds:\n        x: 280\n        y: 648\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_insurance_score\n      label: Insurance Score*\n      value: Excellent (850-999)\n      bounds:\n        x: 500\n        y: 648\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_search_name\n      label: Search Name*\n      value: Landon Cassidy\n      bounds:\n        x: 280\n        y: 700\n        width: 200\n        height: 36\n    - component_type: link\n      id: link_reset\n      label: Reset\n      bounds:\n        x: 490\n        y: 708\n        width: 40\n        height: 20\n    - component_type: dropdown\n      id: dropdown_primary_phone\n      label: Primary Phone\n      value: Select...\n      bounds:\n        x: 280\n        y: 752\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_email\n      label: Email\n      bounds:\n        x: 500\n        y: 752\n        width: 200\n        height: 36\n    - component_type: checkbox\n      id: checkbox_no_email\n      label: No Email\n      bounds:\n        x: 720\n        y: 760\n        width: 80\n        height: 20\n    - component_type: text\n      id: title_dwelling_info\n      label: Dwelling Information\n      bounds:\n        x: 280\n        y: 804\n        width: 150\n        height: 20\n    - component_type: input\n      id: input_lookup_address\n      label: Lookup Address\n      bounds:\n        x: 280\n        y: 836\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_number\n      label: Number*\n      value: '4227'\n      bounds:\n        x: 500\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_direction\n      label: Direction\n      bounds:\n        x: 600\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_street\n      label: Street*\n      value: 5th\n      bounds:\n        x: 700\n        y: 836\n        width: 100\n        height: 36\n    - component_type: dropdown\n      id: dropdown_suffix\n      label: Suffix\n      value: Ave\n      bounds:\n        x: 820\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_post_dir\n      label: Post Dir\n      value: S\n      bounds:\n        x: 920\n        y: 836\n        width: 80\n        height: 36\n    - component_type: dropdown\n      id: dropdown_type\n      label: Type\n      bounds:\n        x: 1020\n        y: 836\n        width: 80\n        height: 36\n    - component_type: checkbox\n      id: checkbox_ignore_address_validation\n      label: Ignore Address Validation\n      bounds:\n        x: 1120\n        y: 820\n        width: 150\n        height: 20\n    - component_type: input\n      id: input_number_2\n      label: Number\n      bounds:\n        x: 1120\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_city\n      label: City*\n      value: St Petersburg\n      bounds:\n        x: 280\n        y: 888\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_county\n      label: County*\n      value: Pinellas\n      bounds:\n        x: 450\n        y: 888\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_state\n      label: State*\n      value: Florida\n      bounds:\n        x: 620\n        y: 888\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_zip\n      label: Zip*\n      value: 33711-1522\n      bounds:\n        x: 790\n        y: 888\n        width: 100\n        height: 36\n    - component_type: text\n      id: text_address_verified\n      label: Address Verified\n      bounds:\n        x: 910\n        y: 896\n        width: 100\n        height: 20\n    - component_type: link\n      id: link_view_map\n      label: View Map\n      bounds:\n        x: 1020\n        y: 896\n        width: 60\n        height: 20\n    - component_type: input\n      id: input_latitude\n      label: Latitude*\n      value: '27.766685'\n      bounds:\n        x: 280\n        y: 940\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_longitude\n      label: Longitude*\n      value: \"-82.690887\"\n      bounds:\n        x: 450\n        y: 940\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_construction_type\n      label: Construction Type*\n      value: Masonry\n      bounds:\n        x: 280\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_occupancy\n      label: Occupancy*\n      value: Owner Occupied\n      bounds:\n        x: 450\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_months_occupied\n      label: Months Occupied*\n      value: 9 to 12 Months\n      bounds:\n        x: 620\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_resided_less_than_2_years\n      label: Has the Insured resided at the risk address for less than 2 years?*\n      value: 'Yes'\n      bounds:\n        x: 280\n        y: 1044\n        width: 150\n        height: 36\n    - component_type: text\n      id: title_prior_address\n      label: Prior Address\n      bounds:\n        x: 280\n        y: 1096\n        width: 100\n        height: 20\n    - component_type: input\n      id: input_prior_address_number\n      label: Number*\n      value: '18001'\n      bounds:\n        x: 280\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_direction\n      label: Direction\n      bounds:\n        x: 380\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_street\n      label: Street*\n      value: Avalon\n      bounds:\n        x: 480\n        y: 1128\n        width: 100\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_address_suffix\n      label: Suffix\n      value: Ln\n      bounds:\n        x: 600\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_post_dir\n      label: Post Dir\n      bounds:\n        x: 700\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_address_type\n      label: Type\n      bounds:\n        x: 800\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_number_2\n      label: Number\n      bounds:\n        x: 900\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: link\n      id: link_verify_address\n      label: Verify\n      bounds:\n        x: 910\n        y: 1188\n        width: 80\n        height: 20\n    - component_type: text\n      id: text_prior_address_verified\n      label: Address\n      bounds:\n        x: 1000\n        y: 1188\n        width: 100\n        height: 20\n  footer:\n  - component_type: container\n    id: main_footer\n    bounds:\n      x: 0\n      y: 1050\n      width: 1920\n      height: 30\n    subcomponents:\n    - component_type: text\n      id: text_posting_date\n      label: 5/29/2025\n      bounds:\n        x: 1650\n        y: 1058\n        width: 150\n        height: 20\n```"]}, {"role": "model", "parts": ["```yaml\nbrowser_component:\n  tab_title: *********** - Guidewire PolicyCenter\n  url: ai.iscs.com/innovation\n  address_bar_focused: false\n  tab_state: idle\nwebpage:\n  header:\n  - component_type: container\n    id: main_header\n    bounds:\n      x: 0\n      y: 69\n      width: 1920\n      height: 61\n    subcomponents:\n    - component_type: image\n      id: logo_american_integrity\n      label: AMERICAN INTEGRITY\n      bounds:\n        x: 16\n        y: 116\n        width: 128\n        height: 40\n    - component_type: navigation_menu\n      id: top_navigation\n      bounds:\n        x: 1290\n        y: 116\n        width: 600\n        height: 36\n      subcomponents:\n      - component_type: link\n        id: link_home\n        label: Home\n        bounds:\n          x: 1298\n          y: 126\n          width: 40\n          height: 20\n      - component_type: link\n        id: link_quote_policy\n        label: Quote/Policy\n        state: active\n        bounds:\n          x: 1362\n          y: 126\n          width: 80\n          height: 20\n      - component_type: link\n        id: link_claims\n        label: Claims\n        bounds:\n          x: 1466\n          y: 126\n          width: 48\n          height: 20\n      - component_type: link\n        id: link_cabinets\n        label: Cabinets\n        bounds:\n          x: 1538\n          y: 126\n          width: 58\n          height: 20\n      - component_type: link\n        id: link_support\n        label: Support\n        bounds:\n          x: 1620\n          y: 126\n          width: 54\n          height: 20\n  sidebar:\n  - component_type: navigation_menu\n    id: left_navigation\n    bounds:\n      x: 0\n      y: 169\n      width: 256\n      height: 809\n    subcomponents:\n    - component_type: search_input\n      id: search_sidebar\n      label: Search\n      bounds:\n        x: 16\n        y: 180\n        width: 188\n        height: 36\n    - component_type: button_icon\n      id: btn_search\n      label: Search\n      bounds:\n        x: 204\n        y: 180\n        width: 36\n        height: 36\n    - component_type: text\n      id: text_advanced_search\n      label: 'ADVANCED SEARCH:'\n      bounds:\n        x: 16\n        y: 224\n        width: 100\n        height: 16\n    - component_type: link\n      id: link_policy_search\n      label: POLICY\n      bounds:\n        x: 120\n        y: 224\n        width: 40\n        height: 16\n    - component_type: link\n      id: link_claims_search\n      label: CLAIMS\n      bounds:\n        x: 170\n        y: 224\n        width: 45\n        height: 16\n    - component_type: dropdown\n      id: dropdown_quote\n      label: Quote\n      state: expanded\n      bounds:\n        x: 16\n        y: 252\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy\n      label: Policy\n      state: active\n      bounds:\n        x: 32\n        y: 284\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_dwelling\n      label: Dwelling\n      bounds:\n        x: 32\n        y: 316\n        width: 208\n        height: 32\n      subcomponents:\n      - component_type: badge\n        id: badge_dwelling_count\n        label: '2'\n        bounds:\n          x: 210\n          y: 322\n          width: 16\n          height: 20\n    - component_type: link\n      id: link_review\n      label: Review\n      bounds:\n        x: 32\n        y: 348\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_attachments\n      label: Attachments\n      bounds:\n        x: 16\n        y: 380\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_correspondence\n      label: Correspondence\n      bounds:\n        x: 16\n        y: 412\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_tasks\n      label: Tasks\n      bounds:\n        x: 16\n        y: 444\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_notes\n      label: Notes\n      bounds:\n        x: 16\n        y: 476\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy_file\n      label: Policy File\n      bounds:\n        x: 16\n        y: 508\n        width: 224\n        height: 32\n  - component_type: action_bar\n    id: right_action_bar\n    bounds:\n      x: 1888\n      y: 169\n      width: 32\n      height: 809\n    subcomponents:\n    - component_type: button_icon\n      id: btn_summary\n      label: SUMMARY\n      bounds:\n        x: 1888\n        y: 180\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_wtcroft_quick_qt\n      label: WTCROFT QUICK QT\n      bounds:\n        x: 1888\n        y: 230\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_quote\n      label: NEW QUOTE\n      bounds:\n        x: 1888\n        y: 280\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_note\n      label: NEW NOTE\n      bounds:\n        x: 1888\n        y: 330\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_attach\n      label: NEW ATTACH...\n      bounds:\n        x: 1888\n        y: 380\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_task\n      label: NEW TASK\n      bounds:\n        x: 1888\n        y: 430\n        width: 32\n        height: 50\n  main_content:\n  - component_type: container\n    id: quote_summary_header\n    bounds:\n      x: 256\n      y: 169\n      width: 1632\n      height: 80\n    subcomponents:\n    - component_type: button\n      id: btn_quote\n      label: QUOTE\n      bounds:\n        x: 280\n        y: 180\n        width: 80\n        height: 36\n    - component_type: text\n      id: text_quote_number_label\n      label: Quote Number\n      bounds:\n        x: 376\n        y: 180\n        width: 80\n        height: 16\n    - component_type: text\n      id: text_quote_number_value\n      label: ***********\n      bounds:\n        x: 376\n        y: 198\n        width: 80\n        height: 16\n    - component_type: text\n      id: text_insured_label\n      label: Insured\n      bounds:\n        x: 472\n        y: 180\n        width: 80\n        height: 16\n    - component_type: link\n      id: link_insured_name\n      label: Landon Cassidy\n      bounds:\n        x: 472\n        y: 198\n        width: 90\n        height: 16\n    - component_type: text\n      id: text_product_label\n      label: Product\n      bounds:\n        x: 578\n        y: 180\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_product_value\n      label: Voluntary Homeowners (HO3)\n      bounds:\n        x: 578\n        y: 198\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_sub_type_label\n      label: Sub Type\n      bounds:\n        x: 744\n        y: 180\n        width: 50\n        height: 16\n    - component_type: text\n      id: text_sub_type_value\n      label: HO3\n      bounds:\n        x: 744\n        y: 198\n        width: 30\n        height: 16\n    - component_type: text\n      id: text_policy_term_label\n      label: Policy Term\n      bounds:\n        x: 810\n        y: 180\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_policy_term_value\n      label: 06/20/2025 - 06/20/2026\n      bounds:\n        x: 810\n        y: 198\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_producer_label\n      label: Producer\n      bounds:\n        x: 976\n        y: 180\n        width: 120\n        height: 16\n    - component_type: link\n      id: link_producer_name\n      label: HH Insurance Group, LLC\n      bounds:\n        x: 976\n        y: 198\n        width: 120\n        height: 16\n    - component_type: text\n      id: text_status_label\n      label: Status\n      bounds:\n        x: 1112\n        y: 180\n        width: 60\n        height: 16\n    - component_type: text\n      id: text_status_value\n      label: In Process\n      bounds:\n        x: 1112\n        y: 198\n        width: 60\n        height: 16\n    - component_type: text\n      id: text_premium_fees_label\n      label: Premium + Fees\n      bounds:\n        x: 1188\n        y: 180\n        width: 90\n        height: 16\n    - component_type: text\n      id: text_premium_fees_value\n      label: $17,776.90\n      bounds:\n        x: 1188\n        y: 198\n        width: 70\n        height: 16\n  - component_type: container\n    id: policy_general_form\n    bounds:\n      x: 256\n      y: 249\n      width: 1632\n      height: 831\n    subcomponents:\n    - component_type: link\n      id: link_return_to_home\n      label: < Return to Home\n      bounds:\n        x: 280\n        y: 260\n        width: 100\n        height: 20\n    - component_type: button\n      id: btn_next_page\n      label: NEXT PAGE\n      bounds:\n        x: 1290\n        y: 220\n        width: 90\n        height: 28\n    - component_type: button\n      id: btn_save\n      label: SAVE\n      bounds:\n        x: 1390\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_copy\n      label: COPY\n      bounds:\n        x: 1460\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_print\n      label: PRINT\n      bounds:\n        x: 1530\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_create_application\n      label: CREATE APPLICATION\n      bounds:\n        x: 1600\n        y: 220\n        width: 150\n        height: 28\n    - component_type: button\n      id: btn_discard_changes\n      label: DISCARD CHANGES\n      bounds:\n        x: 1760\n        y: 220\n        width: 130\n        height: 28\n    - component_type: button\n      id: btn_view_notes\n      label: VIEW NOTES\n      bounds:\n        x: 1290\n        y: 250\n        width: 100\n        height: 28\n    - component_type: button\n      id: btn_delete\n      label: DELETE\n      bounds:\n        x: 1400\n        y: 250\n        width: 70\n        height: 28\n    - component_type: button\n      id: btn_more\n      label: '... MORE'\n      bounds:\n        x: 1480\n        y: 250\n        width: 70\n        height: 28\n    - component_type: text\n      id: title_policy_general\n      label: Policy General\n      bounds:\n        x: 280\n        y: 292\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_product\n      label: Product*\n      value: Florida - Voluntary Homeowners (HO3) - American Integrity Insurance Group\n      bounds:\n        x: 280\n        y: 324\n        width: 400\n        height: 36\n    - component_type: input\n      id: input_effective_date\n      label: Effective Date*\n      value: 06/20/2025\n      bounds:\n        x: 280\n        y: 376\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_producer_code\n      label: 'Producer: Code*'\n      value: AG8529A1\n      bounds:\n        x: 280\n        y: 428\n        width: 150\n        height: 36\n    - component_type: text\n      id: text_producer_name\n      label: HH Insurance Group, LLC\n      bounds:\n        x: 470\n        y: 438\n        width: 150\n        height: 20\n    - component_type: text\n      id: title_prior_carrier\n      label: Prior Carrier Details\n      bounds:\n        x: 280\n        y: 480\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_prior_carrier\n      label: Prior Carrier*\n      value: New Purchase\n      bounds:\n        x: 280\n        y: 512\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_prior_policy_expiration\n      label: Prior Policy Expiration Date\n      bounds:\n        x: 700\n        y: 512\n        width: 200\n        height: 36\n    - component_type: text\n      id: title_insured_info\n      label: Insured Information\n      bounds:\n        x: 280\n        y: 564\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_entity_type\n      label: Entity Type*\n      value: Individual\n      bounds:\n        x: 280\n        y: 596\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_first_name\n      label: First*\n      value: Landon\n      bounds:\n        x: 500\n        y: 596\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_middle_name\n      label: Middle\n      bounds:\n        x: 670\n        y: 596\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_last_name\n      label: Last*\n      value: Cassidy\n      bounds:\n        x: 840\n        y: 596\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_suffix\n      label: Suffix\n      bounds:\n        x: 1010\n        y: 596\n        width: 100\n        height: 36\n    - component_type: input\n      id: input_dob\n      label: DOB*\n      value: 05/20/1998\n      bounds:\n        x: 280\n        y: 648\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_insurance_score\n      label: Insurance Score*\n      value: Excellent (850-999)\n      bounds:\n        x: 500\n        y: 648\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_search_name\n      label: Search Name*\n      value: Landon Cassidy\n      bounds:\n        x: 280\n        y: 700\n        width: 200\n        height: 36\n    - component_type: link\n      id: link_reset\n      label: Reset\n      bounds:\n        x: 490\n        y: 708\n        width: 40\n        height: 20\n    - component_type: dropdown\n      id: dropdown_primary_phone\n      label: Primary Phone\n      value: Select...\n      bounds:\n        x: 280\n        y: 752\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_email\n      label: Email\n      bounds:\n        x: 500\n        y: 752\n        width: 200\n        height: 36\n    - component_type: checkbox\n      id: checkbox_no_email\n      label: No Email\n      bounds:\n        x: 720\n        y: 760\n        width: 80\n        height: 20\n    - component_type: text\n      id: title_dwelling_info\n      label: Dwelling Information\n      bounds:\n        x: 280\n        y: 804\n        width: 150\n        height: 20\n    - component_type: input\n      id: input_lookup_address\n      label: Lookup Address\n      bounds:\n        x: 280\n        y: 836\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_number\n      label: Number*\n      value: '4227'\n      bounds:\n        x: 500\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_direction\n      label: Direction\n      bounds:\n        x: 600\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_street\n      label: Street*\n      value: 5th\n      bounds:\n        x: 700\n        y: 836\n        width: 100\n        height: 36\n    - component_type: dropdown\n      id: dropdown_suffix\n      label: Suffix\n      value: Ave\n      bounds:\n        x: 820\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_post_dir\n      label: Post Dir\n      value: S\n      bounds:\n        x: 920\n        y: 836\n        width: 80\n        height: 36\n    - component_type: dropdown\n      id: dropdown_type\n      label: Type\n      bounds:\n        x: 1020\n        y: 836\n        width: 80\n        height: 36\n    - component_type: checkbox\n      id: checkbox_ignore_address_validation\n      label: Ignore Address Validation\n      bounds:\n        x: 1120\n        y: 820\n        width: 150\n        height: 20\n    - component_type: input\n      id: input_number_2\n      label: Number\n      bounds:\n        x: 1120\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_city\n      label: City*\n      value: St Petersburg\n      bounds:\n        x: 280\n        y: 888\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_county\n      label: County*\n      value: Pinellas\n      bounds:\n        x: 450\n        y: 888\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_state\n      label: State*\n      value: Florida\n      bounds:\n        x: 620\n        y: 888\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_zip\n      label: Zip*\n      value: 33711-1522\n      bounds:\n        x: 790\n        y: 888\n        width: 100\n        height: 36\n    - component_type: text\n      id: text_address_verified\n      label: Address Verified\n      bounds:\n        x: 910\n        y: 896\n        width: 100\n        height: 20\n    - component_type: link\n      id: link_view_map\n      label: View Map\n      bounds:\n        x: 1020\n        y: 896\n        width: 60\n        height: 20\n    - component_type: input\n      id: input_latitude\n      label: Latitude*\n      value: '27.766685'\n      bounds:\n        x: 280\n        y: 940\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_longitude\n      label: Longitude*\n      value: \"-82.690887\"\n      bounds:\n        x: 450\n        y: 940\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_construction_type\n      label: Construction Type*\n      value: Masonry\n      bounds:\n        x: 280\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_occupancy\n      label: Occupancy*\n      value: Owner Occupied\n      bounds:\n        x: 450\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_months_occupied\n      label: Months Occupied*\n      value: 9 to 12 Months\n      bounds:\n        x: 620\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_resided_less_than_2_years\n      label: Has the Insured resided at the risk address for less than 2 years?*\n      value: 'Yes'\n      bounds:\n        x: 280\n        y: 1044\n        width: 150\n        height: 36\n    - component_type: text\n      id: title_prior_address\n      label: Prior Address\n      bounds:\n        x: 280\n        y: 1096\n        width: 100\n        height: 20\n    - component_type: input\n      id: input_prior_address_number\n      label: Number*\n      value: '18001'\n      bounds:\n        x: 280\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_direction\n      label: Direction\n      bounds:\n        x: 380\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_street\n      label: Street*\n      value: Avalon\n      bounds:\n        x: 480\n        y: 1128\n        width: 100\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_address_suffix\n      label: Suffix\n      value: Ln\n      bounds:\n        x: 600\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_post_dir\n      label: Post Dir\n      bounds:\n        x: 700\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_address_type\n      label: Type\n      bounds:\n        x: 800\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_number_2\n      label: Number\n      bounds:\n        x: 900\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: link\n      id: link_verify_address\n      label: Verify\n      state: hovered\n      bounds:\n        x: 910\n        y: 1188\n        width: 80\n        height: 20\n    - component_type: text\n      id: text_prior_address_verified\n      label: Address\n      bounds:\n        x: 1000\n        y: 1188\n        width: 100\n        height: 20\n  footer:\n  - component_type: container\n    id: main_footer\n    bounds:\n      x: 0\n      y: 1050\n      width: 1920\n      height: 30\n    subcomponents:\n    - component_type: text\n      id: text_posting_date\n      label: 5/29/2025\n      bounds:\n        x: 1650\n        y: 1058\n        width: 150\n        height: 20\n```"]}, {"role": "model", "parts": ["```yaml\nbrowser_component:\n  tab_title: *********** - Guide...\n  url: ai.iscs.com/innovation\n  address_bar_focused: false\n  tab_state: idle\nwebpage:\n  header:\n  - component_type: container\n    id: main_header\n    bounds:\n      x: 0\n      y: 69\n      width: 1920\n      height: 61\n    subcomponents:\n    - component_type: image\n      id: logo_american_integrity\n      label: AMERICAN INTEGRITY\n      bounds:\n        x: 16\n        y: 116\n        width: 128\n        height: 40\n    - component_type: navigation_menu\n      id: top_navigation\n      bounds:\n        x: 1290\n        y: 116\n        width: 600\n        height: 36\n      subcomponents:\n      - component_type: link\n        id: link_home\n        label: Home\n        bounds:\n          x: 1298\n          y: 126\n          width: 40\n          height: 20\n      - component_type: link\n        id: link_quote_policy\n        label: Quote/Policy\n        state: active\n        bounds:\n          x: 1362\n          y: 126\n          width: 80\n          height: 20\n      - component_type: link\n        id: link_claims\n        label: Claims\n        bounds:\n          x: 1466\n          y: 126\n          width: 48\n          height: 20\n      - component_type: link\n        id: link_cabinets\n        label: Cabinets\n        bounds:\n          x: 1538\n          y: 126\n          width: 58\n          height: 20\n      - component_type: link\n        id: link_support\n        label: Support\n        bounds:\n          x: 1620\n          y: 126\n          width: 54\n          height: 20\n  sidebar:\n  - component_type: navigation_menu\n    id: left_navigation\n    bounds:\n      x: 0\n      y: 169\n      width: 256\n      height: 809\n    subcomponents:\n    - component_type: search_input\n      id: search_sidebar\n      label: Search\n      bounds:\n        x: 16\n        y: 180\n        width: 188\n        height: 36\n    - component_type: button_icon\n      id: btn_search\n      label: Search\n      bounds:\n        x: 204\n        y: 180\n        width: 36\n        height: 36\n    - component_type: text\n      id: text_advanced_search\n      label: 'ADVANCED SEARCH:'\n      bounds:\n        x: 16\n        y: 224\n        width: 100\n        height: 16\n    - component_type: link\n      id: link_policy_search\n      label: POLICY\n      bounds:\n        x: 120\n        y: 224\n        width: 40\n        height: 16\n    - component_type: link\n      id: link_claims_search\n      label: CLAIMS\n      bounds:\n        x: 170\n        y: 224\n        width: 45\n        height: 16\n    - component_type: dropdown\n      id: dropdown_quote\n      label: Quote\n      state: expanded\n      bounds:\n        x: 16\n        y: 252\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy\n      label: Policy\n      state: active\n      bounds:\n        x: 32\n        y: 284\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_dwelling\n      label: Dwelling\n      bounds:\n        x: 32\n        y: 316\n        width: 208\n        height: 32\n      subcomponents:\n      - component_type: badge\n        id: badge_dwelling_count\n        label: '2'\n        bounds:\n          x: 210\n          y: 322\n          width: 16\n          height: 20\n    - component_type: link\n      id: link_review\n      label: Review\n      bounds:\n        x: 32\n        y: 348\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_attachments\n      label: Attachments\n      bounds:\n        x: 16\n        y: 380\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_correspondence\n      label: Correspondence\n      bounds:\n        x: 16\n        y: 412\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_tasks\n      label: Tasks\n      bounds:\n        x: 16\n        y: 444\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_notes\n      label: Notes\n      bounds:\n        x: 16\n        y: 476\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy_file\n      label: Policy File\n      bounds:\n        x: 16\n        y: 508\n        width: 224\n        height: 32\n  - component_type: action_bar\n    id: right_action_bar\n    bounds:\n      x: 1888\n      y: 169\n      width: 32\n      height: 809\n    subcomponents:\n    - component_type: button_icon\n      id: btn_summary\n      label: SUMMARY\n      bounds:\n        x: 1888\n        y: 180\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_wtcroft_quick_qt\n      label: WTCROFT QUICK QT\n      bounds:\n        x: 1888\n        y: 230\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_quote\n      label: NEW QUOTE\n      bounds:\n        x: 1888\n        y: 280\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_note\n      label: NEW NOTE\n      bounds:\n        x: 1888\n        y: 330\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_attach\n      label: NEW ATTACH...\n      bounds:\n        x: 1888\n        y: 380\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_task\n      label: NEW TASK\n      bounds:\n        x: 1888\n        y: 430\n        width: 32\n        height: 50\n  main_content:\n  - component_type: container\n    id: quote_summary_header\n    bounds:\n      x: 256\n      y: 169\n      width: 1632\n      height: 80\n    subcomponents:\n    - component_type: button\n      id: btn_quote\n      label: QUOTE\n      bounds:\n        x: 280\n        y: 180\n        width: 80\n        height: 36\n    - component_type: text\n      id: text_quote_number_label\n      label: Quote Number\n      bounds:\n        x: 376\n        y: 180\n        width: 80\n        height: 16\n    - component_type: text\n      id: text_quote_number_value\n      label: ***********\n      bounds:\n        x: 376\n        y: 198\n        width: 80\n        height: 16\n    - component_type: text\n      id: text_insured_label\n      label: Insured\n      bounds:\n        x: 472\n        y: 180\n        width: 80\n        height: 16\n    - component_type: link\n      id: link_insured_name\n      label: Landon Cassidy\n      bounds:\n        x: 472\n        y: 198\n        width: 90\n        height: 16\n    - component_type: text\n      id: text_product_label\n      label: Product\n      bounds:\n        x: 578\n        y: 180\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_product_value\n      label: Voluntary Homeowners (HO3)\n      bounds:\n        x: 578\n        y: 198\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_sub_type_label\n      label: Sub Type\n      bounds:\n        x: 744\n        y: 180\n        width: 50\n        height: 16\n    - component_type: text\n      id: text_sub_type_value\n      label: HO3\n      bounds:\n        x: 744\n        y: 198\n        width: 30\n        height: 16\n    - component_type: text\n      id: text_policy_term_label\n      label: Policy Term\n      bounds:\n        x: 810\n        y: 180\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_policy_term_value\n      label: 06/20/2025 - 06/20/2026\n      bounds:\n        x: 810\n        y: 198\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_producer_label\n      label: Producer\n      bounds:\n        x: 976\n        y: 180\n        width: 120\n        height: 16\n    - component_type: link\n      id: link_producer_name\n      label: HH Insurance Group, LLC\n      bounds:\n        x: 976\n        y: 198\n        width: 120\n        height: 16\n    - component_type: text\n      id: text_status_label\n      label: Status\n      bounds:\n        x: 1112\n        y: 180\n        width: 60\n        height: 16\n    - component_type: text\n      id: text_status_value\n      label: In Process\n      bounds:\n        x: 1112\n        y: 198\n        width: 60\n        height: 16\n    - component_type: text\n      id: text_premium_fees_label\n      label: Premium + Fees\n      bounds:\n        x: 1188\n        y: 180\n        width: 90\n        height: 16\n    - component_type: text\n      id: text_premium_fees_value\n      label: $17,776.90\n      bounds:\n        x: 1188\n        y: 198\n        width: 70\n        height: 16\n  - component_type: container\n    id: policy_general_form\n    bounds:\n      x: 256\n      y: 249\n      width: 1632\n      height: 1000\n    subcomponents:\n    - component_type: link\n      id: link_return_to_home\n      label: < Return to Home\n      bounds:\n        x: 280\n        y: 260\n        width: 100\n        height: 20\n    - component_type: button\n      id: btn_next_page\n      label: NEXT PAGE\n      bounds:\n        x: 1290\n        y: 220\n        width: 90\n        height: 28\n    - component_type: button\n      id: btn_save\n      label: SAVE\n      bounds:\n        x: 1390\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_copy\n      label: COPY\n      bounds:\n        x: 1460\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_print\n      label: PRINT\n      bounds:\n        x: 1530\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_create_application\n      label: CREATE APPLICATION\n      bounds:\n        x: 1600\n        y: 220\n        width: 150\n        height: 28\n    - component_type: button\n      id: btn_discard_changes\n      label: DISCARD CHANGES\n      bounds:\n        x: 1760\n        y: 220\n        width: 130\n        height: 28\n    - component_type: button\n      id: btn_view_notes\n      label: VIEW NOTES\n      bounds:\n        x: 1290\n        y: 250\n        width: 100\n        height: 28\n    - component_type: button\n      id: btn_delete\n      label: DELETE\n      bounds:\n        x: 1400\n        y: 250\n        width: 70\n        height: 28\n    - component_type: button\n      id: btn_more\n      label: '... MORE'\n      bounds:\n        x: 1480\n        y: 250\n        width: 70\n        height: 28\n    - component_type: text\n      id: title_policy_general\n      label: Policy General\n      bounds:\n        x: 280\n        y: 292\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_product\n      label: Product*\n      value: Florida - Voluntary Homeowners (HO3) - American Integrity Insurance Group\n      bounds:\n        x: 280\n        y: 324\n        width: 400\n        height: 36\n    - component_type: input\n      id: input_effective_date\n      label: Effective Date*\n      value: 06/20/2025\n      bounds:\n        x: 280\n        y: 376\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_producer_code\n      label: 'Producer: Code*'\n      value: AG8529A1\n      bounds:\n        x: 280\n        y: 428\n        width: 150\n        height: 36\n    - component_type: text\n      id: text_producer_name\n      label: HH Insurance Group, LLC\n      bounds:\n        x: 470\n        y: 438\n        width: 150\n        height: 20\n    - component_type: text\n      id: title_prior_carrier\n      label: Prior Carrier Details\n      bounds:\n        x: 280\n        y: 480\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_prior_carrier\n      label: Prior Carrier*\n      value: New Purchase\n      bounds:\n        x: 280\n        y: 512\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_prior_policy_expiration\n      label: Prior Policy Expiration Date\n      bounds:\n        x: 700\n        y: 512\n        width: 200\n        height: 36\n    - component_type: text\n      id: title_insured_info\n      label: Insured Information\n      bounds:\n        x: 280\n        y: 564\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_entity_type\n      label: Entity Type*\n      value: Individual\n      bounds:\n        x: 280\n        y: 596\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_first_name\n      label: First*\n      value: Landon\n      bounds:\n        x: 500\n        y: 596\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_middle_name\n      label: Middle\n      bounds:\n        x: 670\n        y: 596\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_last_name\n      label: Last*\n      value: Cassidy\n      bounds:\n        x: 840\n        y: 596\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_suffix\n      label: Suffix\n      bounds:\n        x: 1010\n        y: 596\n        width: 100\n        height: 36\n    - component_type: input\n      id: input_dob\n      label: DOB*\n      value: 05/20/1998\n      bounds:\n        x: 280\n        y: 648\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_insurance_score\n      label: Insurance Score*\n      value: Excellent (850-999)\n      bounds:\n        x: 500\n        y: 648\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_search_name\n      label: Search Name*\n      value: Landon Cassidy\n      bounds:\n        x: 280\n        y: 700\n        width: 200\n        height: 36\n    - component_type: link\n      id: link_reset\n      label: Reset\n      bounds:\n        x: 490\n        y: 708\n        width: 40\n        height: 20\n    - component_type: dropdown\n      id: dropdown_primary_phone\n      label: Primary Phone\n      value: Select...\n      bounds:\n        x: 280\n        y: 752\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_email\n      label: Email\n      bounds:\n        x: 500\n        y: 752\n        width: 200\n        height: 36\n    - component_type: checkbox\n      id: checkbox_no_email\n      label: No Email\n      bounds:\n        x: 720\n        y: 760\n        width: 80\n        height: 20\n    - component_type: text\n      id: title_dwelling_info\n      label: Dwelling Information\n      bounds:\n        x: 280\n        y: 804\n        width: 150\n        height: 20\n    - component_type: input\n      id: input_lookup_address\n      label: Lookup Address\n      bounds:\n        x: 280\n        y: 836\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_number\n      label: Number*\n      value: '4227'\n      bounds:\n        x: 500\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_direction\n      label: Direction\n      bounds:\n        x: 600\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_street\n      label: Street*\n      value: 5th\n      bounds:\n        x: 700\n        y: 836\n        width: 100\n        height: 36\n    - component_type: dropdown\n      id: dropdown_suffix\n      label: Suffix\n      value: Ave\n      bounds:\n        x: 820\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_post_dir\n      label: Post Dir\n      value: S\n      bounds:\n        x: 920\n        y: 836\n        width: 80\n        height: 36\n    - component_type: dropdown\n      id: dropdown_type\n      label: Type\n      bounds:\n        x: 1020\n        y: 836\n        width: 80\n        height: 36\n    - component_type: checkbox\n      id: checkbox_ignore_address_validation\n      label: Ignore Address Validation\n      bounds:\n        x: 1120\n        y: 820\n        width: 150\n        height: 20\n    - component_type: input\n      id: input_number_2\n      label: Number\n      bounds:\n        x: 1120\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_city\n      label: City*\n      value: St Petersburg\n      bounds:\n        x: 280\n        y: 888\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_county\n      label: County*\n      value: Pinellas\n      bounds:\n        x: 450\n        y: 888\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_state\n      label: State*\n      value: Florida\n      bounds:\n        x: 620\n        y: 888\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_zip\n      label: Zip*\n      value: 33711-1522\n      bounds:\n        x: 790\n        y: 888\n        width: 100\n        height: 36\n    - component_type: text\n      id: text_address_verified\n      label: Address Verified\n      bounds:\n        x: 910\n        y: 896\n        width: 100\n        height: 20\n    - component_type: link\n      id: link_view_map\n      label: View Map\n      bounds:\n        x: 1020\n        y: 896\n        width: 60\n        height: 20\n    - component_type: input\n      id: input_latitude\n      label: Latitude*\n      value: '27.766685'\n      bounds:\n        x: 280\n        y: 940\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_longitude\n      label: Longitude*\n      value: \"-82.690887\"\n      bounds:\n        x: 450\n        y: 940\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_construction_type\n      label: Construction Type*\n      value: Masonry\n      bounds:\n        x: 280\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_occupancy\n      label: Occupancy*\n      value: Owner Occupied\n      bounds:\n        x: 450\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_months_occupied\n      label: Months Occupied*\n      value: 9 to 12 Months\n      bounds:\n        x: 620\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_resided_less_than_2_years\n      label: Has the Insured resided at the risk address for less than 2 years?*\n      value: 'Yes'\n      bounds:\n        x: 280\n        y: 1044\n        width: 150\n        height: 36\n    - component_type: text\n      id: title_prior_address\n      label: Prior Address\n      bounds:\n        x: 280\n        y: 1096\n        width: 100\n        height: 20\n    - component_type: input\n      id: input_prior_address_number\n      label: Number*\n      value: '18001'\n      bounds:\n        x: 280\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_direction\n      label: Direction\n      bounds:\n        x: 380\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_street\n      label: Street*\n      value: Avalon\n      bounds:\n        x: 480\n        y: 1128\n        width: 100\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_address_suffix\n      label: Suffix\n      value: Ln\n      bounds:\n        x: 600\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_post_dir\n      label: Post Dir\n      bounds:\n        x: 700\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_address_type\n      label: Type\n      bounds:\n        x: 800\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_number_2\n      label: Number\n      bounds:\n        x: 900\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: link\n      id: link_verify_address\n      label: Verify\n      state: hovered\n      bounds:\n        x: 910\n        y: 1188\n        width: 80\n        height: 20\n    - component_type: text\n      id: text_prior_address_verified\n      label: Address\n      bounds:\n        x: 1000\n        y: 1188\n        width: 100\n        height: 20\n```"]}, {"role": "model", "parts": ["```yaml\nbrowser_component:\n  tab_title: *********** - Guide...\n  url: ai.iscs.com/innovation\n  address_bar_focused: false\n  tab_state: idle\nwebpage:\n  header:\n  - component_type: container\n    id: main_header\n    bounds:\n      x: 0\n      y: 69\n      width: 1920\n      height: 61\n    subcomponents:\n    - component_type: image\n      id: logo_american_integrity\n      label: AMERICAN INTEGRITY\n      bounds:\n        x: 16\n        y: 116\n        width: 128\n        height: 40\n    - component_type: navigation_menu\n      id: top_navigation\n      bounds:\n        x: 1290\n        y: 116\n        width: 600\n        height: 36\n      subcomponents:\n      - component_type: link\n        id: link_home\n        label: Home\n        bounds:\n          x: 1298\n          y: 126\n          width: 40\n          height: 20\n      - component_type: link\n        id: link_quote_policy\n        label: Quote/Policy\n        state: active\n        bounds:\n          x: 1362\n          y: 126\n          width: 80\n          height: 20\n      - component_type: link\n        id: link_claims\n        label: Claims\n        bounds:\n          x: 1466\n          y: 126\n          width: 48\n          height: 20\n      - component_type: link\n        id: link_cabinets\n        label: Cabinets\n        bounds:\n          x: 1538\n          y: 126\n          width: 58\n          height: 20\n      - component_type: link\n        id: link_support\n        label: Support\n        bounds:\n          x: 1620\n          y: 126\n          width: 54\n          height: 20\n  sidebar:\n  - component_type: navigation_menu\n    id: left_navigation\n    bounds:\n      x: 0\n      y: 169\n      width: 256\n      height: 809\n    subcomponents:\n    - component_type: search_input\n      id: search_sidebar\n      label: Search\n      bounds:\n        x: 16\n        y: 180\n        width: 188\n        height: 36\n    - component_type: button_icon\n      id: btn_search\n      label: Search\n      bounds:\n        x: 204\n        y: 180\n        width: 36\n        height: 36\n    - component_type: text\n      id: text_advanced_search\n      label: 'ADVANCED SEARCH:'\n      bounds:\n        x: 16\n        y: 224\n        width: 100\n        height: 16\n    - component_type: link\n      id: link_policy_search\n      label: POLICY\n      bounds:\n        x: 120\n        y: 224\n        width: 40\n        height: 16\n    - component_type: link\n      id: link_claims_search\n      label: CLAIMS\n      bounds:\n        x: 170\n        y: 224\n        width: 45\n        height: 16\n    - component_type: dropdown\n      id: dropdown_quote\n      label: Quote\n      state: expanded\n      bounds:\n        x: 16\n        y: 252\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy\n      label: Policy\n      state: active\n      bounds:\n        x: 32\n        y: 284\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_dwelling\n      label: Dwelling\n      bounds:\n        x: 32\n        y: 316\n        width: 208\n        height: 32\n      subcomponents:\n      - component_type: badge\n        id: badge_dwelling_count\n        label: '2'\n        bounds:\n          x: 210\n          y: 322\n          width: 16\n          height: 20\n    - component_type: link\n      id: link_review\n      label: Review\n      bounds:\n        x: 32\n        y: 348\n        width: 208\n        height: 32\n    - component_type: link\n      id: link_attachments\n      label: Attachments\n      bounds:\n        x: 16\n        y: 380\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_correspondence\n      label: Correspondence\n      bounds:\n        x: 16\n        y: 412\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_tasks\n      label: Tasks\n      bounds:\n        x: 16\n        y: 444\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_notes\n      label: Notes\n      bounds:\n        x: 16\n        y: 476\n        width: 224\n        height: 32\n    - component_type: link\n      id: link_policy_file\n      label: Policy File\n      bounds:\n        x: 16\n        y: 508\n        width: 224\n        height: 32\n  - component_type: action_bar\n    id: right_action_bar\n    bounds:\n      x: 1888\n      y: 169\n      width: 32\n      height: 809\n    subcomponents:\n    - component_type: button_icon\n      id: btn_summary\n      label: SUMMARY\n      bounds:\n        x: 1888\n        y: 180\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_wtcroft_quick_qt\n      label: WTCROFT QUICK QT\n      bounds:\n        x: 1888\n        y: 230\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_quote\n      label: NEW QUOTE\n      bounds:\n        x: 1888\n        y: 280\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_note\n      label: NEW NOTE\n      bounds:\n        x: 1888\n        y: 330\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_attach\n      label: NEW ATTACH...\n      bounds:\n        x: 1888\n        y: 380\n        width: 32\n        height: 50\n    - component_type: button_icon\n      id: btn_new_task\n      label: NEW TASK\n      bounds:\n        x: 1888\n        y: 430\n        width: 32\n        height: 50\n  main_content:\n  - component_type: container\n    id: quote_summary_header\n    bounds:\n      x: 256\n      y: 169\n      width: 1632\n      height: 80\n    subcomponents:\n    - component_type: button\n      id: btn_quote\n      label: QUOTE\n      bounds:\n        x: 280\n        y: 180\n        width: 80\n        height: 36\n    - component_type: text\n      id: text_quote_number_label\n      label: Quote Number\n      bounds:\n        x: 376\n        y: 180\n        width: 80\n        height: 16\n    - component_type: text\n      id: text_quote_number_value\n      label: ***********\n      bounds:\n        x: 376\n        y: 198\n        width: 80\n        height: 16\n    - component_type: text\n      id: text_insured_label\n      label: Insured\n      bounds:\n        x: 472\n        y: 180\n        width: 80\n        height: 16\n    - component_type: link\n      id: link_insured_name\n      label: Landon Cassidy\n      bounds:\n        x: 472\n        y: 198\n        width: 90\n        height: 16\n    - component_type: text\n      id: text_product_label\n      label: Product\n      bounds:\n        x: 578\n        y: 180\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_product_value\n      label: Voluntary Homeowners (HO3)\n      bounds:\n        x: 578\n        y: 198\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_sub_type_label\n      label: Sub Type\n      bounds:\n        x: 744\n        y: 180\n        width: 50\n        height: 16\n    - component_type: text\n      id: text_sub_type_value\n      label: HO3\n      bounds:\n        x: 744\n        y: 198\n        width: 30\n        height: 16\n    - component_type: text\n      id: text_policy_term_label\n      label: Policy Term\n      bounds:\n        x: 810\n        y: 180\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_policy_term_value\n      label: 06/20/2025 - 06/20/2026\n      bounds:\n        x: 810\n        y: 198\n        width: 150\n        height: 16\n    - component_type: text\n      id: text_producer_label\n      label: Producer\n      bounds:\n        x: 976\n        y: 180\n        width: 120\n        height: 16\n    - component_type: link\n      id: link_producer_name\n      label: HH Insurance Group, LLC\n      bounds:\n        x: 976\n        y: 198\n        width: 120\n        height: 16\n    - component_type: text\n      id: text_status_label\n      label: Status\n      bounds:\n        x: 1112\n        y: 180\n        width: 60\n        height: 16\n    - component_type: text\n      id: text_status_value\n      label: In Process\n      bounds:\n        x: 1112\n        y: 198\n        width: 60\n        height: 16\n    - component_type: text\n      id: text_premium_fees_label\n      label: Premium + Fees\n      bounds:\n        x: 1188\n        y: 180\n        width: 90\n        height: 16\n    - component_type: text\n      id: text_premium_fees_value\n      label: $17,776.90\n      bounds:\n        x: 1188\n        y: 198\n        width: 70\n        height: 16\n  - component_type: container\n    id: policy_general_form\n    bounds:\n      x: 256\n      y: 249\n      width: 1632\n      height: 1000\n    subcomponents:\n    - component_type: link\n      id: link_return_to_home\n      label: < Return to Home\n      bounds:\n        x: 280\n        y: 260\n        width: 100\n        height: 20\n    - component_type: button\n      id: btn_next_page\n      label: NEXT PAGE\n      bounds:\n        x: 1290\n        y: 220\n        width: 90\n        height: 28\n    - component_type: button\n      id: btn_save\n      label: SAVE\n      bounds:\n        x: 1390\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_copy\n      label: COPY\n      bounds:\n        x: 1460\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_print\n      label: PRINT\n      bounds:\n        x: 1530\n        y: 220\n        width: 60\n        height: 28\n    - component_type: button\n      id: btn_create_application\n      label: CREATE APPLICATION\n      bounds:\n        x: 1600\n        y: 220\n        width: 150\n        height: 28\n    - component_type: button\n      id: btn_discard_changes\n      label: DISCARD CHANGES\n      bounds:\n        x: 1760\n        y: 220\n        width: 130\n        height: 28\n    - component_type: button\n      id: btn_view_notes\n      label: VIEW NOTES\n      bounds:\n        x: 1290\n        y: 250\n        width: 100\n        height: 28\n    - component_type: button\n      id: btn_delete\n      label: DELETE\n      bounds:\n        x: 1400\n        y: 250\n        width: 70\n        height: 28\n    - component_type: button\n      id: btn_more\n      label: '... MORE'\n      bounds:\n        x: 1480\n        y: 250\n        width: 70\n        height: 28\n    - component_type: text\n      id: title_policy_general\n      label: Policy General\n      bounds:\n        x: 280\n        y: 292\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_product\n      label: Product*\n      value: Florida - Voluntary Homeowners (HO3) - American Integrity Insurance Group\n      bounds:\n        x: 280\n        y: 324\n        width: 400\n        height: 36\n    - component_type: input\n      id: input_effective_date\n      label: Effective Date*\n      value: 06/20/2025\n      bounds:\n        x: 280\n        y: 376\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_producer_code\n      label: 'Producer: Code*'\n      value: AG8529A1\n      bounds:\n        x: 280\n        y: 428\n        width: 150\n        height: 36\n    - component_type: text\n      id: text_producer_name\n      label: HH Insurance Group, LLC\n      bounds:\n        x: 470\n        y: 438\n        width: 150\n        height: 20\n    - component_type: text\n      id: title_prior_carrier\n      label: Prior Carrier Details\n      bounds:\n        x: 280\n        y: 480\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_prior_carrier\n      label: Prior Carrier*\n      value: New Purchase\n      bounds:\n        x: 280\n        y: 512\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_prior_policy_expiration\n      label: Prior Policy Expiration Date\n      bounds:\n        x: 700\n        y: 512\n        width: 200\n        height: 36\n    - component_type: text\n      id: title_insured_info\n      label: Insured Information\n      bounds:\n        x: 280\n        y: 564\n        width: 150\n        height: 20\n    - component_type: dropdown\n      id: dropdown_entity_type\n      label: Entity Type*\n      value: Individual\n      bounds:\n        x: 280\n        y: 596\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_first_name\n      label: First*\n      value: Landon\n      bounds:\n        x: 500\n        y: 596\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_middle_name\n      label: Middle\n      bounds:\n        x: 670\n        y: 596\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_last_name\n      label: Last*\n      value: Cassidy\n      bounds:\n        x: 840\n        y: 596\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_suffix\n      label: Suffix\n      bounds:\n        x: 1010\n        y: 596\n        width: 100\n        height: 36\n    - component_type: input\n      id: input_dob\n      label: DOB*\n      value: 05/20/1998\n      bounds:\n        x: 280\n        y: 648\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_insurance_score\n      label: Insurance Score*\n      value: Excellent (850-999)\n      bounds:\n        x: 500\n        y: 648\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_search_name\n      label: Search Name*\n      value: Landon Cassidy\n      bounds:\n        x: 280\n        y: 700\n        width: 200\n        height: 36\n    - component_type: link\n      id: link_reset\n      label: Reset\n      bounds:\n        x: 490\n        y: 708\n        width: 40\n        height: 20\n    - component_type: dropdown\n      id: dropdown_primary_phone\n      label: Primary Phone\n      value: Select...\n      bounds:\n        x: 280\n        y: 752\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_email\n      label: Email\n      bounds:\n        x: 500\n        y: 752\n        width: 200\n        height: 36\n    - component_type: checkbox\n      id: checkbox_no_email\n      label: No Email\n      bounds:\n        x: 720\n        y: 760\n        width: 80\n        height: 20\n    - component_type: text\n      id: title_dwelling_info\n      label: Dwelling Information\n      bounds:\n        x: 280\n        y: 804\n        width: 150\n        height: 20\n    - component_type: input\n      id: input_lookup_address\n      label: Lookup Address\n      bounds:\n        x: 280\n        y: 836\n        width: 200\n        height: 36\n    - component_type: input\n      id: input_number\n      label: Number*\n      value: '4227'\n      bounds:\n        x: 500\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_direction\n      label: Direction\n      bounds:\n        x: 600\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_street\n      label: Street*\n      value: 5th\n      bounds:\n        x: 700\n        y: 836\n        width: 100\n        height: 36\n    - component_type: dropdown\n      id: dropdown_suffix\n      label: Suffix\n      value: Ave\n      bounds:\n        x: 820\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_post_dir\n      label: Post Dir\n      value: S\n      bounds:\n        x: 920\n        y: 836\n        width: 80\n        height: 36\n    - component_type: dropdown\n      id: dropdown_type\n      label: Type\n      bounds:\n        x: 1020\n        y: 836\n        width: 80\n        height: 36\n    - component_type: checkbox\n      id: checkbox_ignore_address_validation\n      label: Ignore Address Validation\n      bounds:\n        x: 1120\n        y: 820\n        width: 150\n        height: 20\n    - component_type: input\n      id: input_number_2\n      label: Number\n      bounds:\n        x: 1120\n        y: 836\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_city\n      label: City*\n      value: St Petersburg\n      bounds:\n        x: 280\n        y: 888\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_county\n      label: County*\n      value: Pinellas\n      bounds:\n        x: 450\n        y: 888\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_state\n      label: State*\n      value: Florida\n      bounds:\n        x: 620\n        y: 888\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_zip\n      label: Zip*\n      value: 33711-1522\n      bounds:\n        x: 790\n        y: 888\n        width: 100\n        height: 36\n    - component_type: text\n      id: text_address_verified\n      label: Address Verified\n      bounds:\n        x: 910\n        y: 896\n        width: 100\n        height: 20\n    - component_type: link\n      id: link_view_map\n      label: View Map\n      bounds:\n        x: 1020\n        y: 896\n        width: 60\n        height: 20\n    - component_type: input\n      id: input_latitude\n      label: Latitude*\n      value: '27.766685'\n      bounds:\n        x: 280\n        y: 940\n        width: 150\n        height: 36\n    - component_type: input\n      id: input_longitude\n      label: Longitude*\n      value: \"-82.690887\"\n      bounds:\n        x: 450\n        y: 940\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_construction_type\n      label: Construction Type*\n      value: Masonry\n      bounds:\n        x: 280\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_occupancy\n      label: Occupancy*\n      value: Owner Occupied\n      bounds:\n        x: 450\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_months_occupied\n      label: Months Occupied*\n      value: 9 to 12 Months\n      bounds:\n        x: 620\n        y: 992\n        width: 150\n        height: 36\n    - component_type: dropdown\n      id: dropdown_resided_less_than_2_years\n      label: Has the Insured resided at the risk address for less than 2 years?*\n      value: 'Yes'\n      bounds:\n        x: 280\n        y: 1044\n        width: 150\n        height: 36\n    - component_type: text\n      id: title_prior_address\n      label: Prior Address\n      bounds:\n        x: 280\n        y: 1096\n        width: 100\n        height: 20\n    - component_type: input\n      id: input_prior_address_number\n      label: Number*\n      value: '18001'\n      bounds:\n        x: 280\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_direction\n      label: Direction\n      bounds:\n        x: 380\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_street\n      label: Street*\n      value: Avalon\n      bounds:\n        x: 480\n        y: 1128\n        width: 100\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_address_suffix\n      label: Suffix\n      value: Ln\n      bounds:\n        x: 600\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_post_dir\n      label: Post Dir\n      bounds:\n        x: 700\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: dropdown\n      id: dropdown_prior_address_type\n      label: Type\n      bounds:\n        x: 800\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: input\n      id: input_prior_address_number_2\n      label: Number\n      bounds:\n        x: 900\n        y: 1128\n        width: 80\n        height: 36\n    - component_type: link\n      id: link_verify_address\n      label: Verify\n      bounds:\n        x: 910\n        y: 1188\n        width: 80\n        height: 20\n    - component_type: text\n      id: text_prior_address_verified\n      label: Address\n      bounds:\n        x: 1000\n        y: 1188\n        width: 100\n        height: 20\n```"]}]