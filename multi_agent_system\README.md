# Multi-Agent System for Insurance Workflow Automation

This project implements a multi-agent system using the a2a-python library to automate insurance workflow processes involving Salesforce, Google Drive documents, and American Integrity systems.

## Architecture

The system consists of 4 specialized agents:

1. **Planner Agent** (Port 8001) - Orchestrates the entire workflow
2. **Salesforce Agent** (Port 8002) - Handles Salesforce login, navigation, and data extraction
3. **Document Agent** (Port 8003) - Handles Google Drive PDF access and quote number extraction
4. **American Integrity Agent** (Port 8004) - Handles American Integrity system form filling

## Communication Flow

```
User → Planner Agent → Salesforce Agent (get data)
                    → Document Agent (get quote number)
                    → American Integrity Agent (fill forms with data)
```

## Features

- **A2A Protocol Communication**: Uses a2a-python library for inter-agent communication
- **Playwright MCP Integration**: Browser automation using Playwright MCP tools
- **Robust Error Handling**: Comprehensive error handling and logging
- **Modular Design**: Each agent is independent and can be scaled separately
- **Configuration Management**: Centralized configuration for all agents

## Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Set up environment variables:
```bash
export OPENAI_API_KEY="your_openai_api_key"
```

3. Install Playwright and MCP server:
```bash
npm install -g @modelcontextprotocol/server-playwright
```

## Usage

### Running the Complete System

```bash
python -m multi_agent_system.main
```

This will:
1. Start all 4 agent servers
2. Set up inter-agent communication
3. Execute the workflow for task "08/19"

### Running Individual Agents

You can also run individual agents for testing:

```bash
# Planner Agent
python -m multi_agent_system.agents.planner_agent

# Salesforce Agent
python -m multi_agent_system.agents.salesforce_agent

# Document Agent
python -m multi_agent_system.agents.document_agent

# American Integrity Agent
python -m multi_agent_system.agents.american_integrity_agent
```

## Workflow Steps

1. **Salesforce Data Extraction**:
   - Login to Salesforce
   - Navigate to Tasks section
   - Search for task "08/19"
   - Extract data from Account, Property, Opportunity, and Claims records

2. **Document Processing**:
   - Access Google Drive folder
   - Open "Troyer HO3 AI.pdf"
   - Extract quote number using pattern matching

3. **Form Filling**:
   - Login to American Integrity system
   - Search for the extracted quote number
   - Fill 29 underwriting questions based on Salesforce data
   - Fill homeowners general information
   - Save and complete the form

## Configuration

All configuration is centralized in `config.py`:

- Agent ports and URLs
- Salesforce credentials and settings
- Google Drive folder and file information
- American Integrity login credentials
- MCP tool configuration

## Data Mapping

The system includes comprehensive mapping between Salesforce fields and American Integrity form questions:

- Account data → Applicant information questions
- Property data → Property-related questions
- Opportunity data → Insurance history questions
- Claims data → Loss history questions

## Error Handling

- Each agent has robust error handling and logging
- Failed operations are logged with detailed error messages
- The system continues processing even if individual steps fail
- Comprehensive status reporting throughout the workflow

## Logging

All agents provide detailed logging:
- Agent initialization and startup
- Task processing and results
- Inter-agent communication
- Error conditions and recovery

## Testing

Run tests with:
```bash
pytest tests/
```

## Development

The system is designed to be easily extensible:

1. Add new agents by extending `BaseAgent`
2. Define new skills using `AgentSkill`
3. Implement custom communication patterns
4. Add new workflow steps to the planner

## Security Considerations

- Credentials are stored in configuration files (use environment variables in production)
- All HTTP communication should use HTTPS in production
- Consider implementing authentication between agents
- Validate all input data before processing

## Troubleshooting

Common issues and solutions:

1. **Agent startup failures**: Check port availability and dependencies
2. **MCP tool errors**: Ensure Playwright MCP server is installed and accessible
3. **Communication timeouts**: Increase timeout values in configuration
4. **Browser automation issues**: Check Playwright installation and browser availability
