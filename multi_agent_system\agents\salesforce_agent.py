"""Salesforce Agent - Handles Salesforce login, navigation, and data extraction."""

import asyncio
import logging
from typing import Dict, Any, Optional
from a2a.types import AgentSkill

from .base_agent import BaseAgent, TaskRequest, TaskResponse
from ..config import SALESFORCE_CONFIG, MCP_CONFIG


class SalesforceAgent(BaseAgent):
    """
    Salesforce Agent handles:
    1. Login to Salesforce
    2. Navigate to Tasks section
    3. Search for specific task (08/19)
    4. Extract household data from Account, Property, Opportunity, Claims records
    """
    
    def __init__(self):
        skills = [
            AgentSkill(
                id="salesforce_login",
                name="Salesforce Login",
                description="Login to Salesforce with provided credentials",
                tags=["salesforce", "authentication", "login"]
            ),
            AgentSkill(
                id="salesforce_navigation",
                name="Salesforce Navigation",
                description="Navigate through Salesforce interface to find specific records",
                tags=["salesforce", "navigation", "search"]
            ),
            AgentSkill(
                id="data_extraction",
                name="Data Extraction",
                description="Extract specific data from Salesforce records",
                tags=["salesforce", "data-extraction", "scraping"]
            )
        ]
        
        super().__init__(
            name="SalesforceAgent",
            description="Handles Salesforce login, navigation, and data extraction using Playwright MCP",
            port=8002,
            skills=skills
        )
        
        self.mcp_toolset = None
    
    async def initialize(self):
        """Initialize the Salesforce agent with MCP toolset."""
        await super().initialize()
        
        # Initialize MCP toolset for Playwright
        from google.adk.tools.mcp_tool.mcp_toolset import (
            MCPToolset,
            StdioConnectionParams,
            StdioServerParameters,
        )
        
        try:
            self.mcp_toolset = MCPToolset(
                connection_params=StdioConnectionParams(
                    server_params=StdioServerParameters(
                        command=MCP_CONFIG["playwright_server_path"],
                        args=MCP_CONFIG["playwright_server_args"]
                    )
                )
            )
            # await self.mcp_toolset.initialize()
            self.logger.info("MCP Playwright toolset initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize MCP toolset: {str(e)}")
            raise
    
    async def process_task(self, request: TaskRequest) -> TaskResponse:
        """Process Salesforce-related tasks."""
        try:
            context = request.context or {}
            action = context.get("action", "")
            
            if action == "get_household_data":
                task_id = context.get("task_id", "")
                result = await self._get_household_data(task_id)
                
                return TaskResponse(
                    id=request.id,
                    status="success" if result else "error",
                    data=result if result else None,
                    error="Failed to extract household data" if not result else None
                )
            
            else:
                return TaskResponse(
                    id=request.id,
                    status="error",
                    error=f"Unknown action: {action}"
                )
                
        except Exception as e:
            self.logger.error(f"Error processing Salesforce task: {str(e)}")
            return TaskResponse(
                id=request.id,
                status="error",
                error=str(e)
            )
    
    async def _get_household_data(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Extract household data from Salesforce."""
        try:
            # Step 1: Login to Salesforce
            await self._login_to_salesforce()
            
            # Step 2: Navigate to Tasks and search
            await self._navigate_to_task(task_id)
            
            # Step 3: Extract data from various records
            household_data = await self._extract_all_data()
            
            return household_data
            
        except Exception as e:
            self.logger.error(f"Error getting household data: {str(e)}")
            return None
    
    async def _login_to_salesforce(self):
        """Login to Salesforce using MCP Playwright."""
        try:
            # Open new browser tab
            await self.mcp_toolset.call_tool(
                "playwright_navigate",
                {"url": SALESFORCE_CONFIG["url"]}
            )
            
            # Fill username
            await self.mcp_toolset.call_tool(
                "playwright_fill",
                {
                    "selector": "input[name='username']",
                    "value": SALESFORCE_CONFIG["username"]
                }
            )
            
            # Fill password
            await self.mcp_toolset.call_tool(
                "playwright_fill",
                {
                    "selector": "input[name='pw']",
                    "value": SALESFORCE_CONFIG["password"]
                }
            )
            
            # Click login button
            await self.mcp_toolset.call_tool(
                "playwright_click",
                {"selector": "input[name='Login']"}
            )
            
            # Wait for page to load
            await asyncio.sleep(3)
            
            self.logger.info("Successfully logged into Salesforce")
            
        except Exception as e:
            self.logger.error(f"Error logging into Salesforce: {str(e)}")
            raise
    
    async def _navigate_to_task(self, task_id: str):
        """Navigate to the specific task in Salesforce."""
        try:
            # Click on global search bar
            await self.mcp_toolset.call_tool(
                "playwright_click",
                {"selector": "[data-target='search.global']"}
            )
            
            # Select Open Tasks from dropdown
            await self.mcp_toolset.call_tool(
                "playwright_click",
                {"selector": "a[title='Open Tasks']"}
            )
            
            # Wait for page to load
            await asyncio.sleep(2)
            
            # Search for the specific task
            await self.mcp_toolset.call_tool(
                "playwright_fill",
                {
                    "selector": "input[placeholder='Search for list']",
                    "value": task_id
                }
            )
            
            # Press Enter or click search
            await self.mcp_toolset.call_tool(
                "playwright_press",
                {"selector": "input[placeholder='Search for list']", "key": "Enter"}
            )
            
            # Wait for results
            await asyncio.sleep(2)
            
            # Click on the household field hyperlink
            await self.mcp_toolset.call_tool(
                "playwright_click",
                {"selector": "a[title*='Household']"}
            )
            
            # Wait for household page to load
            await asyncio.sleep(3)
            
            self.logger.info(f"Successfully navigated to task {task_id}")
            
        except Exception as e:
            self.logger.error(f"Error navigating to task: {str(e)}")
            raise

    async def _extract_all_data(self) -> Dict[str, Any]:
        """Extract all required data from Salesforce records."""
        try:
            data = {}

            # Extract Account data
            account_data = await self._extract_account_data()
            data.update(account_data)

            # Extract Property data
            property_data = await self._extract_property_data()
            data.update(property_data)

            # Extract Opportunity data
            opportunity_data = await self._extract_opportunity_data()
            data.update(opportunity_data)

            # Extract Claims data
            claims_data = await self._extract_claims_data()
            data.update(claims_data)

            self.logger.info("Successfully extracted all Salesforce data")
            return data

        except Exception as e:
            self.logger.error(f"Error extracting data: {str(e)}")
            raise

    async def _extract_account_data(self) -> Dict[str, Any]:
        """Extract data from Account record."""
        try:
            # Navigate to Account record if not already there
            await self.mcp_toolset.call_tool(
                "playwright_click",
                {"selector": "a[title*='Account']"}
            )
            await asyncio.sleep(2)

            account_data = {}

            # Extract Applicant Information fields
            try:
                felony_field = await self.mcp_toolset.call_tool(
                    "playwright_get_text",
                    {"selector": "[data-field='Felony__c'] span"}
                )
                account_data["felony"] = felony_field.get("text", "").lower() in ["true", "yes"]
            except:
                account_data["felony"] = False

            try:
                foreclosure_field = await self.mcp_toolset.call_tool(
                    "playwright_get_text",
                    {"selector": "[data-field='Foreclosure__c'] span"}
                )
                account_data["foreclosure"] = foreclosure_field.get("text", "").lower() in ["true", "yes"]
            except:
                account_data["foreclosure"] = False

            try:
                cancelled_field = await self.mcp_toolset.call_tool(
                    "playwright_get_text",
                    {"selector": "[data-field='Cancelled_Coverage__c'] span"}
                )
                account_data["cancelled_coverage"] = cancelled_field.get("text", "").lower() in ["true", "yes"]
            except:
                account_data["cancelled_coverage"] = False

            try:
                animals_count = await self.mcp_toolset.call_tool(
                    "playwright_get_text",
                    {"selector": "[data-field='Animal_Count__c'] span"}
                )
                account_data["animal_count"] = int(animals_count.get("text", "0") or "0")
            except:
                account_data["animal_count"] = 0

            try:
                sued_carrier = await self.mcp_toolset.call_tool(
                    "playwright_get_text",
                    {"selector": "[data-field='Sued_Carrier__c'] span"}
                )
                account_data["sued_carrier"] = sued_carrier.get("text", "").lower() in ["true", "yes"]
            except:
                account_data["sued_carrier"] = False

            try:
                assignment_benefits = await self.mcp_toolset.call_tool(
                    "playwright_get_text",
                    {"selector": "[data-field='Assignment_Benefits__c'] span"}
                )
                account_data["assignment_benefits"] = assignment_benefits.get("text", "").lower() in ["true", "yes"]
            except:
                account_data["assignment_benefits"] = False

            return account_data

        except Exception as e:
            self.logger.error(f"Error extracting account data: {str(e)}")
            return {}

    async def _extract_property_data(self) -> Dict[str, Any]:
        """Extract data from Property record."""
        try:
            # Navigate to Property record
            await self.mcp_toolset.call_tool(
                "playwright_click",
                {"selector": "a[title*='Property']"}
            )
            await asyncio.sleep(2)

            property_data = {}

            # Extract Property Information fields
            try:
                dwelling_type = await self.mcp_toolset.call_tool(
                    "playwright_get_text",
                    {"selector": "[data-field='Dwelling_Type__c'] span"}
                )
                property_data["dwelling_type"] = dwelling_type.get("text", "")
            except:
                property_data["dwelling_type"] = ""

            try:
                quality_grade = await self.mcp_toolset.call_tool(
                    "playwright_get_text",
                    {"selector": "[data-field='Quality_Grade__c'] span"}
                )
                property_data["quality_grade"] = quality_grade.get("text", "")
            except:
                property_data["quality_grade"] = ""

            # Extract Underwriting Information fields
            try:
                flood_losses = await self.mcp_toolset.call_tool(
                    "playwright_get_text",
                    {"selector": "[data-field='Flood_Losses__c'] span"}
                )
                property_data["flood_losses"] = flood_losses.get("text", "").lower() in ["true", "yes"]
            except:
                property_data["flood_losses"] = False

            try:
                sinkhole_activity = await self.mcp_toolset.call_tool(
                    "playwright_get_text",
                    {"selector": "[data-field='Sinkhole_Activity__c'] span"}
                )
                property_data["sinkhole_activity"] = sinkhole_activity.get("text", "").lower() in ["true", "yes"]
            except:
                property_data["sinkhole_activity"] = False

            try:
                sinkhole_claims = await self.mcp_toolset.call_tool(
                    "playwright_get_text",
                    {"selector": "[data-field='Sinkhole_Claims__c'] span"}
                )
                property_data["sinkhole_claims"] = sinkhole_claims.get("text", "").lower() in ["true", "yes"]
            except:
                property_data["sinkhole_claims"] = False

            try:
                existing_damage = await self.mcp_toolset.call_tool(
                    "playwright_get_text",
                    {"selector": "[data-field='Existing_Damage__c'] span"}
                )
                property_data["existing_damage"] = existing_damage.get("text", "").lower() in ["true", "yes"]
            except:
                property_data["existing_damage"] = False

            try:
                business_activity = await self.mcp_toolset.call_tool(
                    "playwright_get_text",
                    {"selector": "[data-field='Business_Activity__c'] span"}
                )
                property_data["business_activity"] = business_activity.get("text", "").lower() in ["true", "yes"]
            except:
                property_data["business_activity"] = False

            try:
                daycare_services = await self.mcp_toolset.call_tool(
                    "playwright_get_text",
                    {"selector": "[data-field='Daycare_Services__c'] span"}
                )
                property_data["daycare_services"] = daycare_services.get("text", "").lower() in ["true", "yes"]
            except:
                property_data["daycare_services"] = False

            # Extract Fun Stuff fields
            try:
                pool = await self.mcp_toolset.call_tool(
                    "playwright_get_text",
                    {"selector": "[data-field='Pool__c'] span"}
                )
                property_data["pool"] = pool.get("text", "").lower() in ["true", "yes"]
            except:
                property_data["pool"] = False

            try:
                atv = await self.mcp_toolset.call_tool(
                    "playwright_get_text",
                    {"selector": "[data-field='ATV__c'] span"}
                )
                property_data["atv"] = atv.get("text", "").lower() in ["true", "yes"]
            except:
                property_data["atv"] = False

            # Extract Animals fields
            try:
                species = await self.mcp_toolset.call_tool(
                    "playwright_get_text",
                    {"selector": "[data-field='Species__c'] span"}
                )
                property_data["exotic_animals"] = species.get("text", "").lower() == "other"
            except:
                property_data["exotic_animals"] = False

            # Extract EC & Flood Information
            try:
                flood_zone = await self.mcp_toolset.call_tool(
                    "playwright_get_text",
                    {"selector": "[data-field='Flood_Zone__c'] span"}
                )
                property_data["hazardous_flood_zone"] = flood_zone.get("text", "").lower() in ["zone a", "zone v"]
            except:
                property_data["hazardous_flood_zone"] = False

            return property_data

        except Exception as e:
            self.logger.error(f"Error extracting property data: {str(e)}")
            return {}

    async def _extract_opportunity_data(self) -> Dict[str, Any]:
        """Extract data from Opportunity record."""
        try:
            # Navigate to Opportunity record
            await self.mcp_toolset.call_tool(
                "playwright_click",
                {"selector": "a[title*='Opportunity']"}
            )
            await asyncio.sleep(2)

            opportunity_data = {}

            # Extract Currently Owned fields
            try:
                currently_insured = await self.mcp_toolset.call_tool(
                    "playwright_get_text",
                    {"selector": "[data-field='Currently_Insured__c'] span"}
                )
                opportunity_data["currently_insured"] = currently_insured.get("text", "")
            except:
                opportunity_data["currently_insured"] = ""

            try:
                lapse_coverage = await self.mcp_toolset.call_tool(
                    "playwright_get_text",
                    {"selector": "[data-field='Lapse_Coverage__c'] span"}
                )
                opportunity_data["lapse_coverage"] = lapse_coverage.get("text", "").lower() in ["true", "yes"]
            except:
                opportunity_data["lapse_coverage"] = False

            # Extract Occupancy fields
            try:
                home_vacant = await self.mcp_toolset.call_tool(
                    "playwright_get_text",
                    {"selector": "[data-field='Home_Vacant__c'] span"}
                )
                opportunity_data["home_vacant"] = home_vacant.get("text", "").lower() in ["true", "yes"]
            except:
                opportunity_data["home_vacant"] = False

            # Extract Quote(s) to Bind fields
            try:
                previous_carrier = await self.mcp_toolset.call_tool(
                    "playwright_get_text",
                    {"selector": "[data-field='Previous_Carrier__c'] span"}
                )
                opportunity_data["previous_american_integrity"] = previous_carrier.get("text", "").lower() in ["true", "yes"]
            except:
                opportunity_data["previous_american_integrity"] = False

            return opportunity_data

        except Exception as e:
            self.logger.error(f"Error extracting opportunity data: {str(e)}")
            return {}

    async def _extract_claims_data(self) -> Dict[str, Any]:
        """Extract data from Claims record."""
        try:
            # Navigate to Claims record
            await self.mcp_toolset.call_tool(
                "playwright_click",
                {"selector": "a[title*='Claim']"}
            )
            await asyncio.sleep(2)

            claims_data = {}

            # Extract Loss Details fields
            try:
                loss_type = await self.mcp_toolset.call_tool(
                    "playwright_get_text",
                    {"selector": "[data-field='Loss_Type__c'] span"}
                )
                claims_data["loss_type"] = loss_type.get("text", "")
            except:
                claims_data["loss_type"] = ""

            try:
                loss_date = await self.mcp_toolset.call_tool(
                    "playwright_get_text",
                    {"selector": "[data-field='Loss_Date__c'] span"}
                )
                claims_data["loss_date"] = loss_date.get("text", "")
            except:
                claims_data["loss_date"] = ""

            return claims_data

        except Exception as e:
            self.logger.error(f"Error extracting claims data: {str(e)}")
            return {}


if __name__ == "__main__":
    agent = SalesforceAgent()
    asyncio.run(agent.initialize())
    agent.run()
