browser_component:
  tab_title: Guidewire InsuranceN
  url: drive.google.com/drive/folders/1v773cxo-vfGpoJOYt9CYY5YGcpilE5TL
  address_bar_focused: false
  tab_state: idle
webpage:
  sidebar:
  - component_type: navigation_menu
    id: left_navigation
    bounds:
      x: 0
      y: 172
      width: 256
      height: 800
    subcomponents:
    - component_type: button
      id: btn_new
      label: + New
      bounds:
        x: 16
        y: 180
        width: 100
        height: 48
    - component_type: link
      id: link_home
      label: Home
      bounds:
        x: 16
        y: 252
        width: 224
        height: 32
    - component_type: link
      id: link_my_drive
      label: My Drive
      bounds:
        x: 16
        y: 284
        width: 224
        height: 32
    - component_type: link
      id: link_computers
      label: Computers
      bounds:
        x: 16
        y: 316
        width: 224
        height: 32
    - component_type: link
      id: link_shared_with_me
      label: Shared with me
      state: active
      bounds:
        x: 16
        y: 380
        width: 224
        height: 32
    - component_type: link
      id: link_recent
      label: Recent
      bounds:
        x: 16
        y: 412
        width: 224
        height: 32
    - component_type: link
      id: link_starred
      label: Starred
      bounds:
        x: 16
        y: 444
        width: 224
        height: 32
    - component_type: link
      id: link_spam
      label: Spam
      bounds:
        x: 16
        y: 508
        width: 224
        height: 32
    - component_type: link
      id: link_trash
      label: Trash
      bounds:
        x: 16
        y: 540
        width: 224
        height: 32
    - component_type: link
      id: link_storage
      label: Storage
      state: hovered
      bounds:
        x: 16
        y: 572
        width: 224
        height: 32
    - component_type: text
      id: text_storage_usage
      label: 310.4 MB of 15 GB used
      bounds:
        x: 24
        y: 612
        width: 145
        height: 16
    - component_type: button
      id: btn_get_more_storage
      label: Get more storage
      bounds:
        x: 24
        y: 644
        width: 120
        height: 36
  main_content:
  - component_type: container
    id: file_list_container
    bounds:
      x: 256
      y: 172
      width: 1664
      height: 800
    subcomponents:
    - component_type: breadcrumb
      id: breadcrumb_nav
      bounds:
        x: 280
        y: 180
        width: 200
        height: 24
      subcomponents:
      - component_type: text
        id: breadcrumb_shared_with_me
        label: Shared with me
      - component_type: text
        id: breadcrumb_separator
        label: '>'
      - component_type: text
        id: breadcrumb_processed
        label: Processed
    - component_type: table
      id: file_list_table
      bounds:
        x: 280
        y: 280
        width: 1616
        height: 300
      headers:
      - Name
      - Last modified
      - File size
      rows:
      - id: row_1
        cells:
        - component_type: text
          label: Troyer HO3 AI.pdf
        - component_type: text
          label: May 23, 2025 me
        - component_type: text
          label: 140 KB
      - id: row_2
        cells:
        - component_type: text
          label: Towns HO3 AI.pdf
        - component_type: text
          label: May 23, 2025 me
        - component_type: text
          label: 139 KB
      - id: row_3
        cells:
        - component_type: text
          label: Rowen HO3 AI.pdf
        - component_type: text
          label: May 23, 2025 me
        - component_type: text
          label: 139 KB
      - id: row_4
        cells:
        - component_type: text
          label: Guevara HO3 AI.pdf
        - component_type: text
          label: May 23, 2025 me
        - component_type: text
          label: 139 KB
      - id: row_5
        cells:
        - component_type: text
          label: Grady HO3 AI.pdf
        - component_type: text
          label: May 23, 2025 me
        - component_type: text
          label: 139 KB
      - id: row_6
        state: selected
        cells:
        - component_type: text
          label: Cassidy HO3 AI.pdf
        - component_type: text
          label: May 23, 2025 me
        - component_type: text
          label: 277 KB
  overlay:
  - component_type: modal
    id: pdf_viewer
    bounds:
      x: 256
      y: 116
      width: 1664
      height: 862
    subcomponents:
    - component_type: header
      id: pdf_viewer_header
      bounds:
        x: 256
        y: 116
        width: 1664
        height: 56
      subcomponents:
      - component_type: text
        id: pdf_filename
        label: Cassidy HO3 AI.pdf
        bounds:
          x: 280
          y: 132
          width: 130
          height: 24
      - component_type: search_input
        id: search_in_drive
        label: Search in Drive
        bounds:
          x: 424
          y: 128
          width: 200
          height: 32
      - component_type: button
        id: btn_open_with_docs
        label: Open with Google Docs
        bounds:
          x: 648
          y: 124
          width: 180
          height: 36
      - component_type: button_icon
        id: btn_add_comment
        bounds:
          x: 1728
          y: 128
          width: 32
          height: 32
      - component_type: button_icon
        id: btn_print
        bounds:
          x: 1768
          y: 128
          width: 32
          height: 32
      - component_type: button_icon
        id: btn_download
        bounds:
          x: 1808
          y: 128
          width: 32
          height: 32
      - component_type: button_icon
        id: btn_more_actions
        bounds:
          x: 1848
          y: 128
          width: 32
          height: 32
      - component_type: button
        id: btn_share
        label: Share
        bounds:
          x: 1888
          y: 124
          width: 80
          height: 36
    - component_type: document_viewer
      id: pdf_content
      bounds:
        x: 256
        y: 172
        width: 1664
        height: 752
      subcomponents:
      - component_type: image
        id: logo_american_integrity
        label: AMERICAN INTEGRITY logo
        bounds:
          x: 312
          y: 212
          width: 150
          height: 50
      - component_type: text
        id: text_insured_address
        label: |-
          Landon Cassidy
          4227 5th AVE S
          St Petersburg, FL 33711-1522
        bounds:
          x: 312
          y: 272
          width: 180
          height: 48
      - component_type: text
        id: text_agent_address
        label: |-
          HH Insurance Group, LLC
          9887 4th St N Ste 200
          St Petersburg, FL 33702-2451
          (727) 498-5551
        bounds:
          x: 504
          y: 272
          width: 180
          height: 64
      - component_type: text
        id: text_quote_number
        label: 'QUOTE NUMBER: QT-15441432'
        bounds:
          x: 312
          y: 344
          width: 200
          height: 16
      - component_type: text
        id: text_effective_date
        label: 'Effective Date: 06/20/2025 12:01am'
        bounds:
          x: 312
          y: 360
          width: 220
          height: 16
      - component_type: text
        id: text_expiration_date
        label: 'Expiration Date: 06/20/2026 12:01am'
        bounds:
          x: 504
          y: 344
          width: 220
          height: 16
      - component_type: text
        id: text_standard_time
        label: STANDARD TIME at the residence premises
        bounds:
          x: 312
          y: 376
          width: 220
          height: 16
      - component_type: text
        id: text_quote_title
        label: HOMEOWNERS - HO3 INSURANCE QUOTE
        bounds:
          x: 480
          y: 408
          width: 300
          height: 20
      - component_type: table
        id: table_protect_your_home
        bounds:
          x: 312
          y: 432
          width: 600
          height: 180
        headers:
        - PROTECT YOUR HOME
        - '% OF COVERAGE A'
        - LIMIT
        - DEDUCTIBLE
        - PREMIUM
        rows:
        - id: row_dwelling
          cells:
          - component_type: text
            label: Coverage A - Dwelling
          - component_type: text
            label: null
          - component_type: text
            label: $261,000
          - component_type: text
            label: null
          - component_type: text
            label: $17,929.45
        - id: row_other_structures
          cells:
          - component_type: text
            label: Coverage B - Other Structures
          - component_type: text
            label: '20'
          - component_type: text
            label: $52,200
          - component_type: text
            label: null
          - component_type: text
            label: Included
        - id: row_personal_property
          cells:
          - component_type: text
            label: Coverage C - Personal Property
          - component_type: text
            label: '70'
          - component_type: text
            label: $182,700
          - component_type: text
            label: null
          - component_type: text
            label: Included
        - id: row_loss_of_use
          cells:
          - component_type: text
            label: Coverage D - Loss of Use
          - component_type: text
            label: '20'
          - component_type: text
            label: $52,200
          - component_type: text
            label: null
          - component_type: text
            label: Included
        - id: row_ordinance_law
          cells:
          - component_type: text
            label: Ordinance or Law
          - component_type: text
            label: '50'
          - component_type: text
            label: $130,500
          - component_type: text
            label: null
          - component_type: text
            label: Included
        - id: row_fungi_mold
          cells:
          - component_type: text
            label: Limited Fungi, Mold, Wet or Dry Rot, or Bacteria
          - component_type: text
            label: null
          - component_type: text
            label: $10,000
          - component_type: text
            label: null
          - component_type: text
            label: Included
        - id: row_loss_assessment
          cells:
          - component_type: text
            label: Loss Assessment
          - component_type: text
            label: null
          - component_type: text
            label: $1,000
          - component_type: text
            label: null
          - component_type: text
            label: Included
        - id: row_roof_settlement
          cells:
          - component_type: text
            label: Roof Settlement
          - component_type: text
            label: null
          - component_type: text
            label: Actual Cash Value
          - component_type: text
            label: null
          - component_type: text
            label: Included
        - id: row_all_other_perils
          cells:
          - component_type: text
            label: All Other Perils Deductible
          - component_type: text
            label: null
          - component_type: text
            label: null
          - component_type: text
            label: $2,500
          - component_type: text
            label: null
        - id: row_windstorm_hail
          cells:
          - component_type: text
            label: Windstorm or Hail (Other than Hurricane) Deductible
          - component_type: text
            label: '2'
          - component_type: text
            label: null
          - component_type: text
            label: $2,500
          - component_type: text
            label: null
        - id: row_hurricane
          cells:
          - component_type: text
            label: Hurricane Deductible
          - component_type: text
            label: null
          - component_type: text
            label: null
          - component_type: text
            label: $5,220
          - component_type: text
            label: null
      - component_type: table
        id: table_protect_you
        bounds:
          x: 312
          y: 624
          width: 600
          height: 50
        headers:
        - PROTECT YOU
        - LIMIT
        - PREMIUM
        rows:
        - id: row_personal_liability
          cells:
          - component_type: text
            label: Coverage E - Personal Liability
          - component_type: text
            label: $500,000
          - component_type: text
            label: Included
        - id: row_medical_payments
          cells:
          - component_type: text
            label: Coverage F - Medical Payments to Others
          - component_type: text
            label: $5,000
          - component_type: text
            label: Included
      - component_type: table
        id: table_extra_protection
        bounds:
          x: 312
          y: 688
          width: 600
          height: 180
        headers:
        - EXTRA PROTECTION
        - LIMIT
        - PREMIUM
        rows:
        - id: row_diamond_reserve
          cells:
          - component_type: text
            label: Diamond Reserve
          - component_type: text
            label: $500,000
          - component_type: text
            label: Included
        - id: row_animal_liability
          cells:
          - component_type: text
            label: Animal Liability
          - component_type: text
            label: $10,000
          - component_type: text
            label: Included
        - id: row_home_computer
          cells:
          - component_type: text
            label: Home Computer
          - component_type: text
            label: $25,000
          - component_type: text
            label: Included
        - id: row_identity_recovery
          cells:
          - component_type: text
            label: Identity Recovery
          - component_type: text
            label: $15,000
          - component_type: text
            label: Included
        - id: row_limited_carports
          cells:
          - component_type: text
            label: Limited Carport(s), Pool Cage(s), and Screen Enclosure(s)
          - component_type: text
            label: $20,000
          - component_type: text
            label: Included
        - id: row_personal_injury
          cells:
          - component_type: text
            label: Personal Injury
          - component_type: text
            label: $500,000
          - component_type: text
            label: Included
        - id: row_personal_property_replacement
          cells:
          - component_type: text
            label: Personal Property Replacement Cost
          - component_type: text
            label: Included
          - component_type: text
            label: Included
        - id: row_service_line
          cells:
          - component_type: text
            label: Service Line
          - component_type: text
            label: $10,000
          - component_type: text
            label: Included
        - id: row_special_personal_property
          cells:
          - component_type: text
            label: Special Personal Property
          - component_type: text
            label: Included
          - component_type: text
            label: Included
        - id: row_water_damage
          cells:
          - component_type: text
            label: Water Damage
          - component_type: text
            label: Excluded
          - component_type: text
            label: -$459.44
      - component_type: table
        id: table_discounts_surcharges
        bounds:
          x: 312
          y: 880
          width: 600
          height: 60
        headers:
        - DISCOUNTS AND SURCHARGES
        - PREMIUM
        rows:
        - id: row_burglar_alarm
          cells:
          - component_type: text
            label: Burglar Alarm
          - component_type: text
            label: null
        - id: row_proof_of_updates
          cells:
          - component_type: text
            label: Proof of Updates - Roof Only
          - component_type: text
            label: null
        - id: row_secured_community
          cells:
          - component_type: text
            label: Secured Community/Building
          - component_type: text
            label: null
        - id: row_windstorm_mitigation
          cells:
          - component_type: text
            label: Windstorm Loss Mitigation
          - component_type: text
            label: null
    - component_type: footer
      id: pdf_viewer_footer
      bounds:
        x: 256
        y: 924
        width: 1664
        height: 54
      subcomponents:
      - component_type: text
        id: text_page
        label: Page
        bounds:
          x: 880
          y: 936
          width: 30
          height: 20
      - component_type: input
        id: input_page_number
        value: '1'
        bounds:
          x: 912
          y: 936
          width: 24
          height: 20
      - component_type: text
        id: text_total_pages
        label: / 3
        bounds:
          x: 940
          y: 936
          width: 24
          height: 20
      - component_type: button_icon
        id: btn_zoom_out
        bounds:
          x: 980
          y: 932
          width: 28
          height: 28
      - component_type: button_icon
        id: btn_zoom_in
        bounds:
          x: 1020
          y: 932
          width: 28
          height: 28