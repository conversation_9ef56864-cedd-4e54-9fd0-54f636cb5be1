{"enhanced_steps": [{"step_number": 1, "timestamp": "0.0", "instruction": "Started on the \"Guidewire InsuranceNow™\" page at ai.iscs.com/innovation.", "source": "UI Action", "url": "https://ai.iscs.com/innovation", "action_details": {"target_element": "webpage", "input_value": null}}, {"step_number": 2, "timestamp": "1.0", "instruction": "Quick video here for how to locate an American Integrity quote.", "source": "Audio Narration", "url": "https://ai.iscs.com/innovation", "action_details": null}, {"step_number": 3, "timestamp": "8.0", "instruction": "So I'm at the American Integrity homepage.", "source": "Both", "url": "https://drive.google.com/drive/folders/1v773cXo-vfGpoJOYt9CYY5YGcpilE5TL", "action_details": {"target_element": "address_bar", "input_value": "https://drive.google.com/drive/folders/1v773cXo-vfGpoJOYt9CYY5YGcpilE5TL"}}, {"step_number": 4, "timestamp": "9.0", "instruction": "Clicked on the file row for \"Cassidy HO3 AI.pdf\".", "source": "UI Action", "url": "https://drive.google.com/drive/folders/1v773cXo-vfGpoJOYt9CYY5YGcpilE5TL", "action_details": {"target_element": "file_list_table row for \"Cassidy HO3 AI.pdf\"", "input_value": null}}, {"step_number": 5, "timestamp": "10.0", "instruction": "And let's say I wanted to find this quote here.", "source": "Both", "url": "https://drive.google.com/drive/folders/1v773cXo-vfGpoJOYt9CYY5YGcpilE5TL", "action_details": {"target_element": "browser_tab", "input_value": null}}, {"step_number": 6, "timestamp": "10.0", "instruction": "Switched to tab: Guidewire InsuranceNow™", "source": "UI Action", "url": "https://ai.iscs.com/innovation", "action_details": {"target_element": "browser_tab", "input_value": null}}, {"step_number": 7, "timestamp": "17.0", "instruction": "I would highlight this quote number and then I'm going to copy that.", "source": "Both", "url": "https://ai.iscs.com/innovation", "action_details": {"target_element": "address_bar", "input_value": "https://ai.iscs.com/innovation"}}, {"step_number": 8, "timestamp": "21.0", "instruction": "I'm going to paste it in this search bar up here.", "source": "Both", "url": "https://ai.iscs.com/innovation", "action_details": {"target_element": "Search input field", "input_value": "QT-15441432"}}, {"step_number": 9, "timestamp": "22.0", "instruction": "And then when I hit search it will take me, if I'm using that quote number, it will take me right into the quote.", "source": "Both", "url": "https://ai.iscs.com/innovation", "action_details": {"target_element": "btn_search", "input_value": null}}, {"step_number": 10, "timestamp": "29.0", "instruction": "So this is where I was editing and making changes.", "source": "Both", "url": "https://ai.iscs.com/innovation", "action_details": {"target_element": "DOB* input field", "input_value": "05/20/1988"}}, {"step_number": 11, "timestamp": "44.0", "instruction": "This is the first page of a few. We've got policy, dwelling, and then review. And so we can move through our quote here.", "source": "Both", "url": "https://ai.iscs.com/innovation", "action_details": {"target_element": "btn_next_page_footer", "input_value": null}}, {"step_number": 12, "timestamp": "45.0", "instruction": "Clicked the \"NEXT PAGE\" button in the header.", "source": "UI Action", "url": "https://ai.iscs.com/innovation", "action_details": {"target_element": "NEXT PAGE button", "input_value": null}}, {"step_number": 13, "timestamp": "46.0", "instruction": "Clicked the \"NEXT PAGE\" button in the header.", "source": "UI Action", "url": "https://ai.iscs.com/innovation", "action_details": {"target_element": "NEXT PAGE button", "input_value": null}}, {"step_number": 14, "timestamp": "47.0", "instruction": "And then when the quote is filled out, you don't even have to be on the last page. You can just hit the create application button up here to turn it into an application.", "source": "Audio Narration", "url": "https://ai.iscs.com/innovation", "action_details": null}]}