"""Demo script showing the multi-agent workflow sequence."""

import asyncio
import logging
from typing import Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("demo")


class WorkflowDemo:
    """Demonstrates the multi-agent workflow sequence."""
    
    def __init__(self):
        self.step_counter = 0
    
    def log_step(self, message: str):
        """Log a workflow step."""
        self.step_counter += 1
        logger.info(f"Step {self.step_counter}: {message}")
    
    async def simulate_workflow(self, task_id: str = "08/19"):
        """Simulate the complete workflow sequence."""
        
        print("\n" + "="*80)
        print("MULTI-AGENT WORKFLOW SIMULATION")
        print("="*80)
        print(f"Task ID: {task_id}")
        print("Sequence: User → Planner → Salesforce → Document → American Integrity")
        print("="*80 + "\n")
        
        # User initiates workflow
        self.log_step(f"User initiates workflow for task '{task_id}'")
        await asyncio.sleep(1)
        
        # Planner receives request
        self.log_step("Planner Agent receives workflow request")
        self.log_step("Planner Agent analyzes workflow requirements")
        await asyncio.sleep(1)
        
        # Step 1: Salesforce data extraction
        print("\n" + "-"*50)
        print("PHASE 1: SALESFORCE DATA EXTRACTION")
        print("-"*50)
        
        self.log_step("Planner → Salesforce Agent: 'Login & Navigate to Household'")
        await asyncio.sleep(1)
        
        self.log_step("Salesforce Agent: Logging into Salesforce...")
        await asyncio.sleep(2)
        
        self.log_step("Salesforce Agent: Navigating to Tasks section...")
        await asyncio.sleep(1)
        
        self.log_step(f"Salesforce Agent: Searching for task '{task_id}'...")
        await asyncio.sleep(1)
        
        self.log_step("Salesforce Agent: Extracting Account data...")
        await asyncio.sleep(1)
        
        self.log_step("Salesforce Agent: Extracting Property data...")
        await asyncio.sleep(1)
        
        self.log_step("Salesforce Agent: Extracting Opportunity data...")
        await asyncio.sleep(1)
        
        self.log_step("Salesforce Agent: Extracting Claims data...")
        await asyncio.sleep(1)
        
        salesforce_data = {
            "felony": False,
            "foreclosure": False,
            "cancelled_coverage": False,
            "animal_count": 2,
            "dwelling_type": "Single Family Detached",
            "quality_grade": "Standard",
            "pool": True,
            "sinkhole_activity": False,
            "currently_insured": "Yes"
        }
        
        self.log_step("Salesforce Agent → Planner: 'Success, data extracted'")
        print(f"    Extracted data: {len(salesforce_data)} fields")
        await asyncio.sleep(1)
        
        # Step 2: Document processing
        print("\n" + "-"*50)
        print("PHASE 2: DOCUMENT PROCESSING")
        print("-"*50)
        
        self.log_step("Planner → Document Agent: 'Get Quote Number from PDF'")
        await asyncio.sleep(1)
        
        self.log_step("Document Agent: Accessing Google Drive folder...")
        await asyncio.sleep(2)
        
        self.log_step("Document Agent: Opening 'Troyer HO3 AI.pdf'...")
        await asyncio.sleep(2)
        
        self.log_step("Document Agent: Extracting quote number using pattern matching...")
        await asyncio.sleep(1)
        
        quote_number = "Q-789456123"
        self.log_step(f"Document Agent → Planner: 'Success, quote number: {quote_number}'")
        await asyncio.sleep(1)
        
        # Step 3: American Integrity form filling
        print("\n" + "-"*50)
        print("PHASE 3: AMERICAN INTEGRITY FORM FILLING")
        print("-"*50)
        
        self.log_step(f"Planner → American Integrity Agent: 'Login & Search Quote {quote_number}'")
        await asyncio.sleep(1)
        
        self.log_step("American Integrity Agent: Logging into system...")
        await asyncio.sleep(2)
        
        self.log_step(f"American Integrity Agent: Searching for quote '{quote_number}'...")
        await asyncio.sleep(1)
        
        self.log_step("American Integrity Agent: Navigating to Underwriting Questions...")
        await asyncio.sleep(1)
        
        self.log_step("Planner → American Integrity Agent: 'Fill form with Salesforce data'")
        print(f"    Sending complete JSON object with {len(salesforce_data)} data fields")
        await asyncio.sleep(1)
        
        # Simulate filling underwriting questions
        questions = [
            "Felony/fraud convictions",
            "Foreclosure/bankruptcy history", 
            "Fire/liability losses",
            "Flood losses at property",
            "Cancelled/declined coverage",
            "Multiple non-weather losses",
            "Sinkhole investigations",
            "Sinkhole claims",
            "Prior insurance status",
            "Coverage lapses",
            "Animal ownership",
            "Exotic animals",
            "Recreational vehicles",
            "Liability exposures",
            "Property occupancy",
            "Water losses",
            "Foreclosure purchase",
            "Existing damage",
            "Sinkhole disclosures",
            "Pool/spa presence",
            "Unrelated occupants",
            "Business activity",
            "Daycare services",
            "Known sinkhole activity",
            "Vacancy periods",
            "Flood hazard zones",
            "Previous carrier history",
            "Insurance lawsuits",
            "Assignment of benefits"
        ]
        
        for i, question in enumerate(questions, 1):
            self.log_step(f"American Integrity Agent: Filling question {i}/29 - {question}")
            await asyncio.sleep(0.3)
        
        self.log_step("American Integrity Agent: Filling Homeowners General Information...")
        await asyncio.sleep(1)
        
        self.log_step("American Integrity Agent: Filling Dwelling Type...")
        await asyncio.sleep(0.5)
        
        self.log_step("American Integrity Agent: Filling Quality Grade...")
        await asyncio.sleep(0.5)
        
        self.log_step("American Integrity Agent: Saving form...")
        await asyncio.sleep(1)
        
        self.log_step("American Integrity Agent: Navigating to Policy section...")
        await asyncio.sleep(1)
        
        self.log_step("American Integrity Agent → Planner: 'Success, form completed'")
        await asyncio.sleep(1)
        
        # Workflow completion
        print("\n" + "-"*50)
        print("WORKFLOW COMPLETION")
        print("-"*50)
        
        self.log_step(f"Planner → User: 'Task {task_id} Complete'")
        
        final_result = {
            "task_id": task_id,
            "quote_number": quote_number,
            "salesforce_fields_extracted": len(salesforce_data),
            "underwriting_questions_filled": len(questions),
            "homeowner_sections_completed": 2,
            "status": "completed"
        }
        
        print("\n" + "="*80)
        print("WORKFLOW SUMMARY")
        print("="*80)
        print(f"Task ID: {final_result['task_id']}")
        print(f"Quote Number: {final_result['quote_number']}")
        print(f"Salesforce Fields Extracted: {final_result['salesforce_fields_extracted']}")
        print(f"Underwriting Questions Filled: {final_result['underwriting_questions_filled']}")
        print(f"Form Sections Completed: {final_result['homeowner_sections_completed']}")
        print(f"Final Status: {final_result['status'].upper()}")
        print("="*80)
        
        return final_result


async def main():
    """Run the workflow demonstration."""
    demo = WorkflowDemo()
    
    try:
        print("Starting Multi-Agent Workflow Demonstration...")
        print("This simulates the complete insurance workflow automation process.")
        
        result = await demo.simulate_workflow("08/19")
        
        print(f"\nDemo completed successfully!")
        print(f"Total steps executed: {demo.step_counter}")
        
    except KeyboardInterrupt:
        print("\nDemo interrupted by user")
    except Exception as e:
        print(f"Demo error: {str(e)}")


if __name__ == "__main__":
    asyncio.run(main())
