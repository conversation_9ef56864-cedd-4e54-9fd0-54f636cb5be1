[
    "parts=[Part(\n  text=\"\"\"\n    You are a Playwright automation agent integrated with MCP (Model Context Protocol).\n Objective: Your task is to find specific information in Salesforce and a PDF, and then use it to fill out a form in another system.\n\nPrimary Rule: You must open each new website link in a new browser tab. Keep all tabs open until the process is complete.\n\nOpen a chrome browser and follow the below actions\n\n----------------------------------------------------\nStep 1: Get the Source Information from Salesforce \n----------------------------------------------------\n\n\n1.  Open a new tab and paste the link : \"https://hhinsgroup--qburstqa.sandbox.lightning.force.com\"\n2. Log in with the following credentials:  \n   \u2022 Username: <EMAIL>  \n   \u2022 Password: 3Z8~u\\[42  \n3. Go to the **Tasks** section.\n4. Click on the **global search bar**.\n5. From the search dropdown, select **Open Tasks**.\n6. On the **Open Tasks** page, in the **Search for list** field, enter **08/19** and select the result.\n7. In the selected task, click the **Household** field hyperlink.\n   \u2192 This will open the page containing the source information. Keep this tab open.\n\n\n\n----------------------------------------------------\nStep 2: Find the Quote Number in Google Drive\n----------------------------------------------------\n\n1. Open a new tab and paste the link : \"https://drive.google.com/drive/folders/1QGfOmC1gjf72vuXQo2Zhng4o6YbxrMQJ?usp=sharing\"\n2. Open the PDF file named \"Troyer HO3 AI.pdf\".\n3. Locate the \"quote number\" inside this document and copy it.\n\n----------------------------------------------------\nStep 3: Enter the Information into the American Integrity System\n----------------------------------------------------\n\n1. Open a new tab and paste the link : \"https://ai.iscs.com/innovation\"\n2. If you are asked to log in, use these credentials:\n   - Username: AG8529\n   - Password: Potatoes2025!\n3. Search the quote number fetched from the drive (step2)  in American Integrity (Tab title noted as Guidewire InsuranceNow) website\n4. Open the application and locate the section titled Underwriting Questions. Complete this form, then click Next to proceed to next section.\n5. Regardless of the insured/applicant name displayed in the American Integrity application, always proceed using the Salesforce Account/Household/Property/Opportunity/Claims record that was specified in the instructions\n6. Match and transfer information:  \n    \n    For each section  or each underwriting questions  You have to  switch to the Salesforce tab opened in Step 1.\n    Navigate to the specific Salesforce object and section as outlined in the insturctions above\n    Follow the detailed mapping instructions provided for each question to locate the exact field and read its value.\n    Copy the exact answer from the specified Salesforce field and fill it in the underwriting questions (if applicable), or otherwise in the corresponding field of each section\n    If the required subheading is not immediately visible, scroll down on the Salesforce page to locate it.\n    Leave any existing fields in American Integrity unchanged if they are not specified.\n\n\n    1. During the last 5 years, has any applicant been convicted of any degree of the crime of insurance related fraud, bribery, arson or any arson related crime in connection with this or any other property?*\n    - Open the target user's **Account** record in Salesforce in step 1.\n    - Inside the Applicant Information subheading, locate the field \"Felony, insurance fraud, arson, etc..?\".\n    - If value indicates conviction (true/yes), select Yes.\n    - If value indicates no conviction (false/no/empty), select No.\n\n    2. Has the applicant(s) had a personal or business foreclosure, repossession or bankruptcy in the past 5 years?*\n    - Open **Account** record in Salesforce in step 1.\n    - Inside Applicant Information, locate \"Repossession, foreclosure or bankruptcy?\".\n    - If repossession/foreclosure/bankruptcy = Yes, select Yes.\n    - If none/false/no/empty, select No.\n\n    3. Has the applicant(s) had any fire or liability losses within the past 5 years?*\n    - Open **Claim** record in Salesforce in step 1.\n    - Inside Loss Details, check \"Loss Type\" and \"Loss Date\".\n    - If Loss Type = Fire AND Loss Date within last 5 years, select Yes.\n    - Otherwise, select No.\n\n    4. Has the applicant(s) ever had a flood loss at the location stated in this application?*\n    - Open **Property** record in Salesforce in step 1.\n    - Inside Underwriting Information, locate \"Any flood losses at this property?\".\n    - If Yes, select Yes.\n    - If No/empty, select No.\n\n    5. Has the applicant(s) been cancelled, declined or non-renewed by any property insurance carrier in the past 3 years?*\n    - Open **Account** record in Salesforce in step 1.\n    - Inside Applicant Information, locate \"Cxled, NonRenewed or Declined Coverage?\".\n    - If cancellation/decline/non-renewal = Yes, select Yes.\n    - If none/false/no/empty, select No.\n\n    6. Has the applicant(s) had more than 1 non-weather related losses within the past 3 years?*\n    - Open **Claim** record in Salesforce in step 1.\n    - Inside Loss Details, check \"Loss Type\" and \"Loss Date\".\n    - Count all losses NOT Flood, Lightning, Wind within last 3 years.\n    - If >1 non-weather loss = Yes.\n    - If \u22641 non-weather loss = No.\n\n    7. Has the applicant(s), or any person who will be an insured under this policy ever requested a sinkhole investigation, ground study, and/or sinkhole inspection?*\n    - Open **Property** record in Salesforce in step 1.\n    - Inside Underwriting Information, locate \"Has property ever had sinkhole activity?\".\n    - If Yes, select Yes.\n    - If No/empty, select No.\n\n    8. Has the applicant(s) and/or additional insureds ever submitted a claim for sinkhole damage/loss?*\n    - Open **Property** record in Salesforce in step 1.\n    - Inside Underwriting Information, locate \"Any sinkhole claims?\".\n    - If Yes, select Yes.\n    - If No/empty, select No.\n\n    9. Does the applicant(s) have prior insurance? (If property is a new purchase or new construction, answer \"Yes\").*\n    - Open **Opportunity** record in Salesforce in step 1.\n    - Inside Currently Owned, locate \"Currently Insured\".\n    - Apply rules:\n    - New Purchase = Yes\n    - Currently Insured = Yes\n    - Never Insured = No\n    - Lapsed = No\n    - Cancelled = No\n\n    10. Has there been a lapse in continuous homeowners coverage during the past year?*\n    - Open **Opportunity** record in Salesforce in step 1.\n    - Inside Currently Owned, locate \"Any lapse in coverage >30 days?\".\n    - If Yes, select Yes.\n    - If No/empty, select No.\n\n    11. Does the applicant(s)/occupant(s) own or care for any animals?*\n    - Open **Account** record in Salesforcein step 1.\n    - Inside **Account** Information, locate \"Count of Animals\".\n    - If 0 = No.\n    - If >0 = Yes.\n\n    12. Does the applicant(s)/occupant(s) have any non-domesticated, exotic animals?*\n    - Open **Property** record in Salesforce in step 1.\n    - Inside Animals, locate \"Species\".\n    - If Other = Yes.\n    - Else = No.\n\n    13. Does the applicant(s)/occupant(s) own recreational vehicles (ATV\u2019s, etc.)?*\n    - Open **Property** record in Salesforce in step 1.\n    - Inside Fun Stuff, locate \"ATV?\".\n    - If Yes, select Yes.\n    - If No/empty, select No.\n\n    14. Does the insured location have excessive/unusual liability exposures?*\n    - Open **Property** record in Salesforce in step 1.\n    - Check Fun Stuff and Animals:\n    - Diving Board or slide?\n    - Pool fully fenced/walled/screened?\n    - Vicious or Biting History?\n    - Dog Breed?\n    - Skateboard/bicycle ramps?\n    - If Diving Board=No, Pool Fence=Yes, Vicious=Biting=No, Dog Breed \u2260 Pit Bull/Staffordshire/Wolf/Wolf hybrid, Ramps=No \u2192 Select No.\n    - Else \u2192 Select Yes.\n\n    15. Will the applicant(s) occupy the property within 30 days of policy effective date?*\n    - Open **Opportunity** record in Salesforce in step 1.\n    - Inside Occupancy, locate \"Home vacant for >30 days after closing?\".\n    - If vacancy >30 days = No.\n    - If occupancy within 30 days = Yes.\n\n    16. Has the applicant(s) had 1+ non-weather water losses in past 3 years?*\n    - give as No.\n\n    17. Was the property a short-sale or foreclosure prior to purchase?*\n    - Open **Account** record in Salesforce in step 1.\n    - Inside Applicant Information, locate \"Repossession, foreclosure or bankruptcy?\".\n    - If foreclosure = Yes.\n    - Else = No.\n\n    18. Does the insured location have existing/unrepaired damage?*\n    - Open **Property** record in Salesforce in step 1.\n    - Inside Underwriting Information, locate \"Existing Damage at Residence?\".\n    - If Yes, select Yes.\n    - If No/empty, select No.\n\n    19. At time of purchase/building, were there disclosures concerning sinkhole activity?*\n    - Open **Property** record in Salesforce in step 1.\n    - Inside Underwriting Information, locate \"Has property ever had sinkhole activity?\".\n    - If Yes, select Yes.\n    - If No/empty, select No.\n\n    20. Does the insured location have a pool, hot tub, or spa?*\n    - Open **Property** record in Salesforce in step 1.\n    - Inside Fun Stuff, locate \"Pool?\".\n    - If Yes, select Yes.\n    - If No/empty, select No.\n\n\n    21. Is the insured location occupied by 3+ unrelated individuals?*\n    - Default to No (no Salesforce mapping).\n\n    22. Is there any business activity on the premises?*\n    - Open **Property** record in Salesforce in step 1.\n    - Inside Underwriting Information, locate \"Any business conducted at the property?\".\n    - If Yes, select Yes.\n    - If No/empty, select No.\n\n    23. Is there child/adult day care on premises?*\n    - Open **Property** record in Salesforce in step 1.\n    - Inside Underwriting Information, locate \"Daycare or rehab services conducted?\".\n    - If Yes, select Yes.\n    - If No/empty, select No.\n\n    24. Does the property have any known sinkhole activity?*\n    - Open **Property** record in Salesforce in step 1.\n    - Inside Underwriting Information, locate \"Has property ever had sinkhole activity?\".\n    - If Yes, select Yes.\n    - If No/empty, select No.\n\n    25. Has the insured location been vacant/unoccupied 30+ days prior to purchase?*\n    - Open **Opportunity** record in Salesforce in step 1.\n    - Inside Occupancy, locate \"Home vacant for >30 days after closing?\".\n    - If Yes, select Yes.\n    - If No/Unknown, select No.\n\n    26. Is the insured location in a Special Flood Hazard Area?*\n    - Open **Property** record in Salesforce in step 1.\n    - Inside EC & Flood Information, locate \"Hazardous Flood Zone?\".\n    - If No, select No.\n    - If Zone A or Zone V indicated, follow picklist options.\n\n    27. Has the applicant ever been insured with American Integrity?*\n    - Open **Opportunity** record in Salesforce in step 1.\n    - Inside Quote(s) to Bind, locate \"Have you ever been insured with this carrier before?\".\n    - If Yes, select Yes.\n    - If No/empty, select No.\n\n    28. Has the prospective insured ever been party to a lawsuit against an insurance company?*\n    - Open **Account** record in Salesforce in step 1.\n    - Inside Applicant Information, locate \"Ever sued your insurance carrier?\".\n    - If Yes, select Yes.\n    - If No/empty, select No.\n\n    29. Has the prospective insured had an assignment of benefits claim that resulted in a lawsuit?*\n    - Open **Account** record in Salesforce in step 1.\n    - Inside Applicant Information, locate \"Ever signed an assignment of benefits?\".\n    - If Yes, select Yes.\n    - If No/empty, select No.\n\nAfter transferring all available information in the underwriting question then click **Next Page** Button\n\n    ##Homeowners General Information\n\n    ### 1. Dwelling Type*\n    - Open the target user's **Property** record in Salesforce in step 1.\n    - Inside the Property Information subheading, locate the field \"Dwelling Type\".\n    - If value = \"Single Family Detached\", select Single Family.\n    - If value = \"Duplex\", select Duplex.  \n    - If value = \"Townhouse\", select Rowhouse/Townhouse.\n    - If any other dwelling type appears, task back to producer.\n\n    ## Replacement Cost Estimator\n\n    ### 2. Quality Grade*\n    - Open **Property** record in Salesforce in step 1.\n    - Inside Property Information, locate \"Quality Grade\".\n    - If value = \"Premium\", select Premium (defaults to Above Average).\n    - If value = \"Custom\", select Custom.\n    - If value = \"Above Average\", select Above Average.\n    - If value = \"Standard\", select Standard.\n    - If value = \"Economy\", select Economy.\n\n\n\n\n6. After transferring all available information from Salesforce into the Underwriting Questions form, click **\"SAVE\"** to complete the process and click **Next Page** Button  until it goes back to the **Policy** section in the sidebar.  \n\n\n    \"\"\"\n)] role='user'"
]
[
    "parts=[Part(\n  text=\"\"\"\n    You are a Playwright automation agent integrated with MCP (Model Context Protocol).\n Objective: Your task is to find specific information in Salesforce and a PDF, and then use it to fill out a form in another system.\n\nPrimary Rule: You must open each new website link in a new browser tab. Keep all tabs open until the process is complete.\n\nOpen a chrome browser and follow the below actions\n\n----------------------------------------------------\nStep 1: Get the Source Information from Salesforce \n----------------------------------------------------\n\n\n1.  Open a new tab and paste the link : \"https://hhinsgroup--qburstqa.sandbox.lightning.force.com\"\n2. Log in with the following credentials:  \n   \u2022 Username: <EMAIL>  \n   \u2022 Password: 3Z8~u\\[42  \n3. Go to the **Tasks** section.\n4. Click on the **global search bar**.\n5. From the search dropdown, select **Open Tasks**.\n6. On the **Open Tasks** page, in the **Search for list** field, enter **08/19** and select the result.\n7. In the selected task, click the **Household** field hyperlink.\n   \u2192 This will open the page containing the source information. Keep this tab open.\n\n\n\n----------------------------------------------------\nStep 2: Find the Quote Number in Google Drive\n----------------------------------------------------\n\n1. Open a new tab and paste the link : \"https://drive.google.com/drive/folders/1QGfOmC1gjf72vuXQo2Zhng4o6YbxrMQJ?usp=sharing\"\n2. Open the PDF file named \"Troyer HO3 AI.pdf\".\n3. Locate the \"quote number\" inside this document and copy it.\n\n----------------------------------------------------\nStep 3: Enter the Information into the American Integrity System\n----------------------------------------------------\n\n1. Open a new tab and paste the link : \"https://ai.iscs.com/innovation\"\n2. If you are asked to log in, use these credentials:\n   - Username: AG8529\n   - Password: Potatoes2025!\n3. Search the quote number fetched from the drive (step2)  in American Integrity (Tab title noted as Guidewire InsuranceNow) website\n4. Open the application and locate the section titled Underwriting Questions. Complete this form, then click Next to proceed to next section.\n5. Regardless of the insured/applicant name displayed in the American Integrity application, always proceed using the Salesforce Account/Household/Property/Opportunity/Claims record that was specified in the instructions\n6. Match and transfer information:  \n    \n    For each section  or each underwriting questions  You have to  switch to the Salesforce tab opened in Step 1.\n    Navigate to the specific Salesforce object and section as outlined in the insturctions above\n    Follow the detailed mapping instructions provided for each question to locate the exact field and read its value.\n    Copy the exact answer from the specified Salesforce field and fill it in the underwriting questions (if applicable), or otherwise in the corresponding field of each section\n    If the required subheading is not immediately visible, scroll down on the Salesforce page to locate it.\n    Leave any existing fields in American Integrity unchanged if they are not specified.\n\n\n    1. During the last 5 years, has any applicant been convicted of any degree of the crime of insurance related fraud, bribery, arson or any arson related crime in connection with this or any other property?*\n    - Open the target user's **Account** record in Salesforce in step 1.\n    - Inside the Applicant Information subheading, locate the field \"Felony, insurance fraud, arson, etc..?\".\n    - If value indicates conviction (true/yes), select Yes.\n    - If value indicates no conviction (false/no/empty), select No.\n\n    2. Has the applicant(s) had a personal or business foreclosure, repossession or bankruptcy in the past 5 years?*\n    - Open **Account** record in Salesforce in step 1.\n    - Inside Applicant Information, locate \"Repossession, foreclosure or bankruptcy?\".\n    - If repossession/foreclosure/bankruptcy = Yes, select Yes.\n    - If none/false/no/empty, select No.\n\n    3. Has the applicant(s) had any fire or liability losses within the past 5 years?*\n    - Open **Claim** record in Salesforce in step 1.\n    - Inside Loss Details, check \"Loss Type\" and \"Loss Date\".\n    - If Loss Type = Fire AND Loss Date within last 5 years, select Yes.\n    - Otherwise, select No.\n\n    4. Has the applicant(s) ever had a flood loss at the location stated in this application?*\n    - Open **Property** record in Salesforce in step 1.\n    - Inside Underwriting Information, locate \"Any flood losses at this property?\".\n    - If Yes, select Yes.\n    - If No/empty, select No.\n\n    5. Has the applicant(s) been cancelled, declined or non-renewed by any property insurance carrier in the past 3 years?*\n    - Open **Account** record in Salesforce in step 1.\n    - Inside Applicant Information, locate \"Cxled, NonRenewed or Declined Coverage?\".\n    - If cancellation/decline/non-renewal = Yes, select Yes.\n    - If none/false/no/empty, select No.\n\n    6. Has the applicant(s) had more than 1 non-weather related losses within the past 3 years?*\n    - Open **Claim** record in Salesforce in step 1.\n    - Inside Loss Details, check \"Loss Type\" and \"Loss Date\".\n    - Count all losses NOT Flood, Lightning, Wind within last 3 years.\n    - If >1 non-weather loss = Yes.\n    - If \u22641 non-weather loss = No.\n\n    7. Has the applicant(s), or any person who will be an insured under this policy ever requested a sinkhole investigation, ground study, and/or sinkhole inspection?*\n    - Open **Property** record in Salesforce in step 1.\n    - Inside Underwriting Information, locate \"Has property ever had sinkhole activity?\".\n    - If Yes, select Yes.\n    - If No/empty, select No.\n\n    8. Has the applicant(s) and/or additional insureds ever submitted a claim for sinkhole damage/loss?*\n    - Open **Property** record in Salesforce in step 1.\n    - Inside Underwriting Information, locate \"Any sinkhole claims?\".\n    - If Yes, select Yes.\n    - If No/empty, select No.\n\n    9. Does the applicant(s) have prior insurance? (If property is a new purchase or new construction, answer \"Yes\").*\n    - Open **Opportunity** record in Salesforce in step 1.\n    - Inside Currently Owned, locate \"Currently Insured\".\n    - Apply rules:\n    - New Purchase = Yes\n    - Currently Insured = Yes\n    - Never Insured = No\n    - Lapsed = No\n    - Cancelled = No\n\n    10. Has there been a lapse in continuous homeowners coverage during the past year?*\n    - Open **Opportunity** record in Salesforce in step 1.\n    - Inside Currently Owned, locate \"Any lapse in coverage >30 days?\".\n    - If Yes, select Yes.\n    - If No/empty, select No.\n\n    11. Does the applicant(s)/occupant(s) own or care for any animals?*\n    - Open **Account** record in Salesforcein step 1.\n    - Inside **Account** Information, locate \"Count of Animals\".\n    - If 0 = No.\n    - If >0 = Yes.\n\n    12. Does the applicant(s)/occupant(s) have any non-domesticated, exotic animals?*\n    - Open **Property** record in Salesforce in step 1.\n    - Inside Animals, locate \"Species\".\n    - If Other = Yes.\n    - Else = No.\n\n    13. Does the applicant(s)/occupant(s) own recreational vehicles (ATV\u2019s, etc.)?*\n    - Open **Property** record in Salesforce in step 1.\n    - Inside Fun Stuff, locate \"ATV?\".\n    - If Yes, select Yes.\n    - If No/empty, select No.\n\n    14. Does the insured location have excessive/unusual liability exposures?*\n    - Open **Property** record in Salesforce in step 1.\n    - Check Fun Stuff and Animals:\n    - Diving Board or slide?\n    - Pool fully fenced/walled/screened?\n    - Vicious or Biting History?\n    - Dog Breed?\n    - Skateboard/bicycle ramps?\n    - If Diving Board=No, Pool Fence=Yes, Vicious=Biting=No, Dog Breed \u2260 Pit Bull/Staffordshire/Wolf/Wolf hybrid, Ramps=No \u2192 Select No.\n    - Else \u2192 Select Yes.\n\n    15. Will the applicant(s) occupy the property within 30 days of policy effective date?*\n    - Open **Opportunity** record in Salesforce in step 1.\n    - Inside Occupancy, locate \"Home vacant for >30 days after closing?\".\n    - If vacancy >30 days = No.\n    - If occupancy within 30 days = Yes.\n\n    16. Has the applicant(s) had 1+ non-weather water losses in past 3 years?*\n    - give as No.\n\n    17. Was the property a short-sale or foreclosure prior to purchase?*\n    - Open **Account** record in Salesforce in step 1.\n    - Inside Applicant Information, locate \"Repossession, foreclosure or bankruptcy?\".\n    - If foreclosure = Yes.\n    - Else = No.\n\n    18. Does the insured location have existing/unrepaired damage?*\n    - Open **Property** record in Salesforce in step 1.\n    - Inside Underwriting Information, locate \"Existing Damage at Residence?\".\n    - If Yes, select Yes.\n    - If No/empty, select No.\n\n    19. At time of purchase/building, were there disclosures concerning sinkhole activity?*\n    - Open **Property** record in Salesforce in step 1.\n    - Inside Underwriting Information, locate \"Has property ever had sinkhole activity?\".\n    - If Yes, select Yes.\n    - If No/empty, select No.\n\n    20. Does the insured location have a pool, hot tub, or spa?*\n    - Open **Property** record in Salesforce in step 1.\n    - Inside Fun Stuff, locate \"Pool?\".\n    - If Yes, select Yes.\n    - If No/empty, select No.\n\n\n    21. Is the insured location occupied by 3+ unrelated individuals?*\n    - Default to No (no Salesforce mapping).\n\n    22. Is there any business activity on the premises?*\n    - Open **Property** record in Salesforce in step 1.\n    - Inside Underwriting Information, locate \"Any business conducted at the property?\".\n    - If Yes, select Yes.\n    - If No/empty, select No.\n\n    23. Is there child/adult day care on premises?*\n    - Open **Property** record in Salesforce in step 1.\n    - Inside Underwriting Information, locate \"Daycare or rehab services conducted?\".\n    - If Yes, select Yes.\n    - If No/empty, select No.\n\n    24. Does the property have any known sinkhole activity?*\n    - Open **Property** record in Salesforce in step 1.\n    - Inside Underwriting Information, locate \"Has property ever had sinkhole activity?\".\n    - If Yes, select Yes.\n    - If No/empty, select No.\n\n    25. Has the insured location been vacant/unoccupied 30+ days prior to purchase?*\n    - Open **Opportunity** record in Salesforce in step 1.\n    - Inside Occupancy, locate \"Home vacant for >30 days after closing?\".\n    - If Yes, select Yes.\n    - If No/Unknown, select No.\n\n    26. Is the insured location in a Special Flood Hazard Area?*\n    - Open **Property** record in Salesforce in step 1.\n    - Inside EC & Flood Information, locate \"Hazardous Flood Zone?\".\n    - If No, select No.\n    - If Zone A or Zone V indicated, follow picklist options.\n\n    27. Has the applicant ever been insured with American Integrity?*\n    - Open **Opportunity** record in Salesforce in step 1.\n    - Inside Quote(s) to Bind, locate \"Have you ever been insured with this carrier before?\".\n    - If Yes, select Yes.\n    - If No/empty, select No.\n\n    28. Has the prospective insured ever been party to a lawsuit against an insurance company?*\n    - Open **Account** record in Salesforce in step 1.\n    - Inside Applicant Information, locate \"Ever sued your insurance carrier?\".\n    - If Yes, select Yes.\n    - If No/empty, select No.\n\n    29. Has the prospective insured had an assignment of benefits claim that resulted in a lawsuit?*\n    - Open **Account** record in Salesforce in step 1.\n    - Inside Applicant Information, locate \"Ever signed an assignment of benefits?\".\n    - If Yes, select Yes.\n    - If No/empty, select No.\n\nAfter transferring all available information in the underwriting question then click **Next Page** Button\n\n    ##Homeowners General Information\n\n    ### 1. Dwelling Type*\n    - Open the target user's **Property** record in Salesforce in step 1.\n    - Inside the Property Information subheading, locate the field \"Dwelling Type\".\n    - If value = \"Single Family Detached\", select Single Family.\n    - If value = \"Duplex\", select Duplex.  \n    - If value = \"Townhouse\", select Rowhouse/Townhouse.\n    - If any other dwelling type appears, task back to producer.\n\n    ## Replacement Cost Estimator\n\n    ### 2. Quality Grade*\n    - Open **Property** record in Salesforce in step 1.\n    - Inside Property Information, locate \"Quality Grade\".\n    - If value = \"Premium\", select Premium (defaults to Above Average).\n    - If value = \"Custom\", select Custom.\n    - If value = \"Above Average\", select Above Average.\n    - If value = \"Standard\", select Standard.\n    - If value = \"Economy\", select Economy.\n\n\n\n\n6. After transferring all available information from Salesforce into the Underwriting Questions form, click **\"SAVE\"** to complete the process and click **Next Page** Button  until it goes back to the **Policy** section in the sidebar.  \n\n\n    \"\"\"\n)] role='user'",
    "parts=[Part(\n  function_response=FunctionResponse(\n    id='call_Jssz64zzepWCAB65y8SpmUvG',\n    name='browser_tabs',\n    response={\n      'result': CallToolResult(\n        content=[\n          TextContent(\n            text=\"\"\"### Open tabs\n- 0: [] (about:blank)\n- 1: (current) [] (about:blank)\n\"\"\",\n            type='text'\n          ),\n        ],\n        isError=False\n      )\n    }\n  )\n)] role='user'"
]
