"""Configuration settings for the multi-agent system."""

import os
from typing import Dict, Any

# Agent Configuration
AGENT_PORTS = {
    "planner": 8001,
    "salesforce": 8002,
    "document": 8003,
    "american_integrity": 8004
}

AGENT_URLS = {
    "planner": f"http://localhost:{AGENT_PORTS['planner']}",
    "salesforce": f"http://localhost:{AGENT_PORTS['salesforce']}",
    "document": f"http://localhost:{AGENT_PORTS['document']}",
    "american_integrity": f"http://localhost:{AGENT_PORTS['american_integrity']}"
}

# Salesforce Configuration
SALESFORCE_CONFIG = {
    "url": "https://hhinsgroup--qburstqa.sandbox.lightning.force.com",
    "username": "<EMAIL>",
    "password": "3Z8~u\\[42",
    "search_term": "08/19"
}

# Google Drive Configuration
GOOGLE_DRIVE_CONFIG = {
    "url": "https://drive.google.com/drive/folders/1QGfOmC1gjf72vuXQo2Zhng4o6YbxrMQJ?usp=sharing",
    "pdf_name": "Troyer HO3 AI.pdf"
}

# American Integrity Configuration
AMERICAN_INTEGRITY_CONFIG = {
    "url": "https://ai.iscs.com/innovation",
    "username": "AG8529",
    "password": "Potatoes2025!"
}

# MCP Tool Configuration
MCP_CONFIG = {
    "playwright_server_path": "npx",
    "playwright_server_args": ["@modelcontextprotocol/server-playwright"]
}

# Logging Configuration
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
}
