# playwright_runner.py

import asyncio
import sys
import os
import logging
import json
import openai # Added for OpenAI summarization
from contextlib import asynccontextmanager
from google.adk.agents.llm_agent import Agent
from google.adk.tools.mcp_tool.mcp_toolset import (
    MCPToolset,
    StdioConnectionParams,
    StdioServerParameters,
)
import litellm
import datetime
from litellm.integrations.custom_logger import CustomLogger
from google.adk.agents.invocation_context import InvocationContext
from google.adk.events import Event, EventActions
from google.adk.sessions import InMemorySessionService
from google.adk.runners import Runner
from google.genai import types
from pydantic import BaseModel, Field
from google.genai.types import Schema
from google.adk.models.lite_llm import LiteLlm

from google.adk.agents.callback_context import CallbackContext
from google.adk.models import LlmRequest, LlmResponse
from typing import Optional

# Configure logging to suppress non-critical warnings
logging.getLogger("google.adk.tools.mcp_tool").setLevel(logging.ERROR)

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# --- IMPORTANT ---
# Make sure your OpenAI API key is set as an environment variable
# For example, in your terminal: export OPENAI_API_KEY='your_key_here'
if "OPENAI_API_KEY" not in os.environ:
    logging.warning("OPENAI_API_KEY environment variable not set. Summarization will fail.")
else:
    openai.api_key = os.environ["OPENAI_API_KEY"]


litellm._turn_on_debug()
class PlaywrightAgent:
    def __init__(self, model="gemini-2.5-pro"):
        # self.model = model
        self.model = LiteLlm(model="openai/gpt-5-2025-08-07")
        self.agent = None
        self.session_service = InMemorySessionService()
        self.runner = None
        self.mcp_toolset = None


    # def truncate_context_callback(
    #     self,
    #     callback_context: CallbackContext,
    #     llm_request: LlmRequest
    # ) -> Optional[LlmResponse]:
    #     """
    #     Truncate context if it gets too long.
    #     When truncating, summarize the removed part using OpenAI and insert
    #     the summary back into the context.
    #     """
    #     max_messages = 31  # The maximum number of messages to keep in context

    #     if len(llm_request.contents) > max_messages:
    #         print(f"Context length ({len(llm_request.contents)}) exceeds max ({max_messages}). Truncating and summarizing...")

    #         # Always keep the first message (system prompt)
    #         system_message = llm_request.contents[0]

    #         # We want to keep max_messages. One is the system prompt, one will be our new summary,
    #         # so we keep the remaining (max_messages - 2) from the end.
    #         num_recent_to_keep = max_messages - 2
            
    #         # Ensure we don't try to keep a negative number of messages
    #         if num_recent_to_keep < 1:
    #             num_recent_to_keep = 1

    #         # Identify the split point
    #         split_index = len(llm_request.contents) - num_recent_to_keep
            
    #         # Messages to be summarized and removed
    #         messages_to_summarize = llm_request.contents[1:split_index]
            
    #         # Recent messages to keep
    #         recent_messages = llm_request.contents[split_index:]

    #         summary_text = ""
    #         try:
    #             # Format the messages for the summarization prompt
    #             history_to_summarize_str = "\n".join(
    #                 f"- {msg.role}: {part.text}"
    #                 for msg in messages_to_summarize
    #                 if msg.parts for part in msg.parts if hasattr(part, 'text')
    #             )

    #             if history_to_summarize_str:
    #                 # Use OpenAI to summarize the truncated content
    #                 client = openai.OpenAI()
    #                 prompt = f"""The following is a truncated section of a conversation history between a user and a web automation AI agent. Please provide a concise, one-paragraph summary of the key actions taken, tools used, and information gathered in this section.

    #                 --- BEGIN TRUNCATED HISTORY ---
    #                 {history_to_summarize_str}
    #                 --- END TRUNCATED HISTORY ---

    #                 Summary:"""

    #                 response = client.chat.completions.create(
    #                     model="gpt-4o-mini",  # Efficient model for summarization
    #                     messages=[{"role": "user", "content": prompt}],
    #                     temperature=0.2,
    #                 )
    #                 summary_text = response.choices[0].message.content
    #                 print(f"\nGenerated Summary:\n{summary_text}\n")

    #                 # Create the new summary message to insert into the context
    #                 summary_message_content = (
    #                     f"[Context automatically summarized to save space. "
    #                     f"The conversation from index 1 to {split_index - 1} was summarized as follows: {summary_text}]"
    #                 )
    #                 summary_message = types.Content(
    #                     role="model",  # Use 'model' or 'system' role for automated messages
    #                     parts=[types.Part(text=summary_message_content)]
    #                 )

    #                 # Reconstruct the llm_request contents
    #                 llm_request.contents = [system_message, summary_message] + recent_messages
    #             else:
    #                 # If there's nothing to summarize, just do a simple trim
    #                 llm_request.contents = [system_message] + recent_messages

    #         except Exception as e:
    #             logging.error(f"Failed to summarize context with OpenAI: {e}. Falling back to simple truncation.")
    #             # Fallback: If summarization fails, revert to the old simple truncation
    #             # Keep the system message and the last (max_messages - 1) messages
    #             llm_request.contents = [system_message] + llm_request.contents[-(max_messages - 1):]

    #     # Log the final state of the context before sending to the LLM
    #     with open("request_history_final.txt", "a") as f:
    #         # Create a serializable version of the contents
    #         serializable_contents = [
    #             {
    #                 "role": content.role,
    #                 # FIX: Changed part.to_dict() to part.__dict__
    #                 "parts": [part.__dict__ for part in content.parts]
    #             }
    #             for content in llm_request.contents
    #         ]
    #         f.write(json.dumps(serializable_contents, indent=4, default=str))
    #         f.write("\n---\n")

    #     return None  # Allow the main model call to proceed

    def truncate_context_callback(
        self,
        callback_context: CallbackContext, 
        llm_request: LlmRequest
    ) -> Optional[LlmResponse]:
        """Truncate context each time before LLM processes requests (including tool calls)"""
        
        # llm_request.contents = [msg for msg in llm_request.contents if getattr(msg, "role", None) != "model"]

    # Strategy 1: Keep only recent messages (sliding window)
        max_messages = 31  # Adjust based on your needs
        reduce_by = 6
        if len(llm_request.contents) > max_messages:
            # Always keep the system message at index 0
            system_message = [llm_request.contents[0]]
            
            # Keep only the last (max_messages - 1) messages from the rest
            recent_messages = llm_request.contents[-(max_messages - 1 - reduce_by):]
            # recent_messages = llm_request.contents[-6:]
            
            llm_request.contents = system_message + recent_messages

        print("system_messages : ",llm_request.contents)
        # parts_as_dicts = [part.__dict__ for part in event.content.parts]

                    # Now, use json.dumps() ollm_request.contentsn the list of dictionaries
        with open("request_history_15.txt", "a") as f:
            f.write(json.dumps(llm_request.contents, indent=4, default=str))
            f.write("\n")
       
    
        return None  # Allow model call to proceed

    
    def after_model_callback(
        callback_context: CallbackContext,
        llm_response: LlmResponse
    ) ->  Optional[LlmResponse]:
        if "history" not in callback_context.state:
            callback_context.state["history"] = []

        # Find the last user prompt from contents
        # last_user_content = next((c for c in reversed(llm_request.contents) if c.role == "user"), None)
        # user_prompt = last_user_content.parts[0].text if last_user_content and last_user_content.parts else ""

        print("con: >", llm_response.content)
        history_entry = {
            "timestamp": datetime.datetime.now().isoformat(),
            "session_id": callback_context.state.get("session_id") ,
            "agent_name": callback_context.agent_name,
            "model_response": llm_response.content.parts[0].text if llm_response else None
        }

        callback_context.state["history"].append(history_entry)

        # Save to a file
        with open(f"session_{callback_context.state.get("session_id") }_history.json", "a") as f:
            json.dump(history_entry, f)
            f.write("\n")  # Append newline for readability

        return None

    async def __aenter__(self):
        """Async context manager entry"""
        await self.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit with proper cleanup"""
        await self.cleanup()

    async def initialize(self):
        """Initialize the agent and its components"""
        self.mcp_toolset = MCPToolset(
            connection_params=StdioConnectionParams(
                server_params=StdioServerParameters(
                    command="npx",
                    args=["@playwright/mcp@latest"]
                ),
                timeout=60000.0
            )
        )

        # Initialize the MCP toolset
        try:
            # If MCPToolset has an async initialization method, use it
            if hasattr(self.mcp_toolset, 'initialize'):
                await self.mcp_toolset.initialize()
        except Exception as e:
            logging.warning(f"MCPToolset initialization warning: {e}")

        self.agent = Agent(
            name="playwright_agent",
            model=self.model,
            description="Browser automation agent with Playwright",
            instruction="""
            You are a browser automation agent powered by Playwright. Your job is to interact with web pages just like a human would, using Playwright commands. You can open URLs, click buttons, fill input fields, take screenshots, wait for elements, and extract content.          
            Always ensure proper error handling and wait for elements to be ready before interacting with them.
            """,
            tools=[self.mcp_toolset],
            before_model_callback=self.truncate_context_callback,
            # after_model_callback=PlaywrightAgent.after_model_callback

        )

        self.runner = Runner(
            agent=self.agent,
            app_name="playwright_app",
            session_service=self.session_service
        )



    async def cleanup(self):
        """Clean up resources properly"""
        try:
            # Close MCP toolset if it has a close method
            if self.mcp_toolset and hasattr(self.mcp_toolset, 'close'):
                await self.mcp_toolset.close()

            # Add small delay to ensure proper cleanup
            await asyncio.sleep(0.1)

        except Exception as e:
            logging.error(f"Error during cleanup: {e}")



    async def invoke_user_input(self, user_input: str):
        """Process user input through the agent"""
        try:
            # Create session
            await self.session_service.create_session(
                app_name="playwright_app",
                user_id="user1",
                session_id="sess1"
            )

            content = types.Content(
                parts=[types.Part(text=user_input)],
                role="user"
            )

            # Create async generator for events
            events_async = self.runner.run_async(
                user_id="user1",
                session_id="sess1",
                new_message=content
            )

            result = None

            try:
                async for event in events_async:
                    # print("event",event)
                    parts_as_dicts = [part.__dict__ for part in event.content.parts]

                    # Now, use json.dumps() on the list of dictionaries
                    with open("session_123_history.txt", "a") as f:
                        f.write(json.dumps(parts_as_dicts, indent=4, default=str))
                        f.write("\n")

                    if event.is_final_response() and event.content.parts:
                        result = {
                            "status": True,
                            "message": "Data retrieved successfully",
                            "data": event.content.parts[0].text
                        }
                        break

            except Exception as e:
                logging.error(f"Error processing events: {e}")
                result = {
                    "status": False,
                    "message": "Failed when using MCP",
                    "data": str(e)
                }

            finally:
                # Properly close the async generator
                try:
                    await events_async.aclose()
                except Exception as e:
                    logging.warning(f"Warning closing events generator: {e}")

            return result if result else {
                "status": False,
                "message": "No response received",
                "data": None
            }

        except Exception as e:
            logging.error(f"Error in invoke_user_input: {e}")
            return {
                "status": False,
                "message": f"Failed to process request: {str(e)}",
                "data": None
            }

async def main():
    """Main function with proper async context management"""
    user_instruction =  """
    You are a Playwright automation agent integrated with MCP (Model Context Protocol).
 Objective: Your task is to find specific information in Salesforce and a PDF, and then use it to fill out a form in another system.

Primary Rule: You must open each new website link in a new browser tab. Keep all tabs open until the process is complete.

Open a chrome browser and follow the below actions

----------------------------------------------------
Step 1: Get the Source Information from Salesforce 
----------------------------------------------------


1.  Open a new tab and paste the link : "https://hhinsgroup--qburstqa.sandbox.lightning.force.com"
2. Log in with the following credentials:  
   • Username: <EMAIL>  
   • Password: 3Z8~u\[42  
3. Go to the **Tasks** section.
4. Click on the **global search bar**.
5. From the search dropdown, select **Open Tasks**.
6. On the **Open Tasks** page, in the **Search for list** field, enter **08/19** and select the result.
7. In the selected task, click the **Household** field hyperlink.
   → This will open the page containing the source information. Keep this tab open.



----------------------------------------------------
Step 2: Find the Quote Number in Google Drive
----------------------------------------------------

1. Open a new tab and paste the link : "https://drive.google.com/drive/folders/1QGfOmC1gjf72vuXQo2Zhng4o6YbxrMQJ?usp=sharing"
2. Open the PDF file named "Troyer HO3 AI.pdf".
3. Locate the "quote number" inside this document and copy it.

----------------------------------------------------
Step 3: Enter the Information into the American Integrity System
----------------------------------------------------

1. Open a new tab and paste the link : "https://ai.iscs.com/innovation"
2. If you are asked to log in, use these credentials:
   - Username: AG8529
   - Password: Potatoes2025!
3. Search the quote number fetched from the drive (step2)  in American Integrity (Tab title noted as Guidewire InsuranceNow) website
4. Open the application and locate the section titled Underwriting Questions. Complete this form, then click Next to proceed to next section.
5. Regardless of the insured/applicant name displayed in the American Integrity application, always proceed using the Salesforce Account/Household/Property/Opportunity/Claims record that was specified in the instructions
6. Match and transfer information:  
    
    For each section  or each underwriting questions  You have to  switch to the Salesforce tab opened in Step 1.
    Navigate to the specific Salesforce object and section as outlined in the insturctions above
    Follow the detailed mapping instructions provided for each question to locate the exact field and read its value.
    Copy the exact answer from the specified Salesforce field and fill it in the underwriting questions (if applicable), or otherwise in the corresponding field of each section
    If the required subheading is not immediately visible, scroll down on the Salesforce page to locate it.
    Leave any existing fields in American Integrity unchanged if they are not specified.


    1. During the last 5 years, has any applicant been convicted of any degree of the crime of insurance related fraud, bribery, arson or any arson related crime in connection with this or any other property?*
    - Open the target user's **Account** record in Salesforce in step 1.
    - Inside the Applicant Information subheading, locate the field "Felony, insurance fraud, arson, etc..?".
    - If value indicates conviction (true/yes), select Yes.
    - If value indicates no conviction (false/no/empty), select No.

    2. Has the applicant(s) had a personal or business foreclosure, repossession or bankruptcy in the past 5 years?*
    - Open **Account** record in Salesforce in step 1.
    - Inside Applicant Information, locate "Repossession, foreclosure or bankruptcy?".
    - If repossession/foreclosure/bankruptcy = Yes, select Yes.
    - If none/false/no/empty, select No.

    3. Has the applicant(s) had any fire or liability losses within the past 5 years?*
    - Open **Claim** record in Salesforce in step 1.
    - Inside Loss Details, check "Loss Type" and "Loss Date".
    - If Loss Type = Fire AND Loss Date within last 5 years, select Yes.
    - Otherwise, select No.

    4. Has the applicant(s) ever had a flood loss at the location stated in this application?*
    - Open **Property** record in Salesforce in step 1.
    - Inside Underwriting Information, locate "Any flood losses at this property?".
    - If Yes, select Yes.
    - If No/empty, select No.

    5. Has the applicant(s) been cancelled, declined or non-renewed by any property insurance carrier in the past 3 years?*
    - Open **Account** record in Salesforce in step 1.
    - Inside Applicant Information, locate "Cxled, NonRenewed or Declined Coverage?".
    - If cancellation/decline/non-renewal = Yes, select Yes.
    - If none/false/no/empty, select No.

    6. Has the applicant(s) had more than 1 non-weather related losses within the past 3 years?*
    - Open **Claim** record in Salesforce in step 1.
    - Inside Loss Details, check "Loss Type" and "Loss Date".
    - Count all losses NOT Flood, Lightning, Wind within last 3 years.
    - If >1 non-weather loss = Yes.
    - If ≤1 non-weather loss = No.

    7. Has the applicant(s), or any person who will be an insured under this policy ever requested a sinkhole investigation, ground study, and/or sinkhole inspection?*
    - Open **Property** record in Salesforce in step 1.
    - Inside Underwriting Information, locate "Has property ever had sinkhole activity?".
    - If Yes, select Yes.
    - If No/empty, select No.

    8. Has the applicant(s) and/or additional insureds ever submitted a claim for sinkhole damage/loss?*
    - Open **Property** record in Salesforce in step 1.
    - Inside Underwriting Information, locate "Any sinkhole claims?".
    - If Yes, select Yes.
    - If No/empty, select No.

    9. Does the applicant(s) have prior insurance? (If property is a new purchase or new construction, answer "Yes").*
    - Open **Opportunity** record in Salesforce in step 1.
    - Inside Currently Owned, locate "Currently Insured".
    - Apply rules:
    - New Purchase = Yes
    - Currently Insured = Yes
    - Never Insured = No
    - Lapsed = No
    - Cancelled = No

    10. Has there been a lapse in continuous homeowners coverage during the past year?*
    - Open **Opportunity** record in Salesforce in step 1.
    - Inside Currently Owned, locate "Any lapse in coverage >30 days?".
    - If Yes, select Yes.
    - If No/empty, select No.

    11. Does the applicant(s)/occupant(s) own or care for any animals?*
    - Open **Account** record in Salesforcein step 1.
    - Inside **Account** Information, locate "Count of Animals".
    - If 0 = No.
    - If >0 = Yes.

    12. Does the applicant(s)/occupant(s) have any non-domesticated, exotic animals?*
    - Open **Property** record in Salesforce in step 1.
    - Inside Animals, locate "Species".
    - If Other = Yes.
    - Else = No.

    13. Does the applicant(s)/occupant(s) own recreational vehicles (ATV’s, etc.)?*
    - Open **Property** record in Salesforce in step 1.
    - Inside Fun Stuff, locate "ATV?".
    - If Yes, select Yes.
    - If No/empty, select No.

    14. Does the insured location have excessive/unusual liability exposures?*
    - Open **Property** record in Salesforce in step 1.
    - Check Fun Stuff and Animals:
    - Diving Board or slide?
    - Pool fully fenced/walled/screened?
    - Vicious or Biting History?
    - Dog Breed?
    - Skateboard/bicycle ramps?
    - If Diving Board=No, Pool Fence=Yes, Vicious=Biting=No, Dog Breed ≠ Pit Bull/Staffordshire/Wolf/Wolf hybrid, Ramps=No → Select No.
    - Else → Select Yes.

    15. Will the applicant(s) occupy the property within 30 days of policy effective date?*
    - Open **Opportunity** record in Salesforce in step 1.
    - Inside Occupancy, locate "Home vacant for >30 days after closing?".
    - If vacancy >30 days = No.
    - If occupancy within 30 days = Yes.

    16. Has the applicant(s) had 1+ non-weather water losses in past 3 years?*
    - give as No.

    17. Was the property a short-sale or foreclosure prior to purchase?*
    - Open **Account** record in Salesforce in step 1.
    - Inside Applicant Information, locate "Repossession, foreclosure or bankruptcy?".
    - If foreclosure = Yes.
    - Else = No.

    18. Does the insured location have existing/unrepaired damage?*
    - Open **Property** record in Salesforce in step 1.
    - Inside Underwriting Information, locate "Existing Damage at Residence?".
    - If Yes, select Yes.
    - If No/empty, select No.

    19. At time of purchase/building, were there disclosures concerning sinkhole activity?*
    - Open **Property** record in Salesforce in step 1.
    - Inside Underwriting Information, locate "Has property ever had sinkhole activity?".
    - If Yes, select Yes.
    - If No/empty, select No.

    20. Does the insured location have a pool, hot tub, or spa?*
    - Open **Property** record in Salesforce in step 1.
    - Inside Fun Stuff, locate "Pool?".
    - If Yes, select Yes.
    - If No/empty, select No.


    21. Is the insured location occupied by 3+ unrelated individuals?*
    - Default to No (no Salesforce mapping).

    22. Is there any business activity on the premises?*
    - Open **Property** record in Salesforce in step 1.
    - Inside Underwriting Information, locate "Any business conducted at the property?".
    - If Yes, select Yes.
    - If No/empty, select No.

    23. Is there child/adult day care on premises?*
    - Open **Property** record in Salesforce in step 1.
    - Inside Underwriting Information, locate "Daycare or rehab services conducted?".
    - If Yes, select Yes.
    - If No/empty, select No.

    24. Does the property have any known sinkhole activity?*
    - Open **Property** record in Salesforce in step 1.
    - Inside Underwriting Information, locate "Has property ever had sinkhole activity?".
    - If Yes, select Yes.
    - If No/empty, select No.

    25. Has the insured location been vacant/unoccupied 30+ days prior to purchase?*
    - Open **Opportunity** record in Salesforce in step 1.
    - Inside Occupancy, locate "Home vacant for >30 days after closing?".
    - If Yes, select Yes.
    - If No/Unknown, select No.

    26. Is the insured location in a Special Flood Hazard Area?*
    - Open **Property** record in Salesforce in step 1.
    - Inside EC & Flood Information, locate "Hazardous Flood Zone?".
    - If No, select No.
    - If Zone A or Zone V indicated, follow picklist options.

    27. Has the applicant ever been insured with American Integrity?*
    - Open **Opportunity** record in Salesforce in step 1.
    - Inside Quote(s) to Bind, locate "Have you ever been insured with this carrier before?".
    - If Yes, select Yes.
    - If No/empty, select No.

    28. Has the prospective insured ever been party to a lawsuit against an insurance company?*
    - Open **Account** record in Salesforce in step 1.
    - Inside Applicant Information, locate "Ever sued your insurance carrier?".
    - If Yes, select Yes.
    - If No/empty, select No.

    29. Has the prospective insured had an assignment of benefits claim that resulted in a lawsuit?*
    - Open **Account** record in Salesforce in step 1.
    - Inside Applicant Information, locate "Ever signed an assignment of benefits?".
    - If Yes, select Yes.
    - If No/empty, select No.

After transferring all available information in the underwriting question then click **Next Page** Button

    ##Homeowners General Information

    ### 1. Dwelling Type*
    - Open the target user's **Property** record in Salesforce in step 1.
    - Inside the Property Information subheading, locate the field "Dwelling Type".
    - If value = "Single Family Detached", select Single Family.
    - If value = "Duplex", select Duplex.  
    - If value = "Townhouse", select Rowhouse/Townhouse.
    - If any other dwelling type appears, task back to producer.

    ## Replacement Cost Estimator

    ### 2. Quality Grade*
    - Open **Property** record in Salesforce in step 1.
    - Inside Property Information, locate "Quality Grade".
    - If value = "Premium", select Premium (defaults to Above Average).
    - If value = "Custom", select Custom.
    - If value = "Above Average", select Above Average.
    - If value = "Standard", select Standard.
    - If value = "Economy", select Economy.




6. After transferring all available information from Salesforce into the Underwriting Questions form, click **"SAVE"** to complete the process and click **Next Page** Button  until it goes back to the **Policy** section in the sidebar.  


    """




    try:
        # Use async context manager for proper resource management
        async with PlaywrightAgent() as agent:
            result = await agent.invoke_user_input(user_instruction)
            print("\n✅ Result:", result)

    except Exception as e:
        print(f"\n❌ Main error: {e}")
        import traceback
        traceback.print_exc()

# Alternative approach without context manager
async def main_simple():
    """Alternative approach with manual cleanup"""
    agent = None
    try:
        agent = PlaywrightAgent()
        await agent.initialize()

        user_instruction = "Navigate to https://example.com and take a screenshot"

        result = await agent.invoke_user_input(user_instruction)
        print("\n✅ Result:", result)

    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()

    finally:
        if agent:
            await agent.cleanup()
            # Add extra delay to ensure all resources are freed
            await asyncio.sleep(0.5)

if __name__ == "__main__":
    # Choose which approach to use
    # asyncio.run(main())  # With context manager
    asyncio.run(main())  # Without context manager