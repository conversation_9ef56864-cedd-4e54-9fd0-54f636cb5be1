[{"image_name": "frame_0000_000.jpg", "error": "Generated content is not valid YAML. Error: mapping values are not allowed here\n  in \"<unicode string>\", line 204, column 29:\n          label: ADVANCED SEARCH:\n                                ^", "tokens": 2217, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-09-04T16:22:54.078107", "attempt": 1}, {"image_name": "frame_0000_000.jpg", "error": "Generated content is not valid YAML. Error: mapping values are not allowed here\n  in \"<unicode string>\", line 108, column 33:\n              label: ADVANCED SEARCH:\n                                    ^", "tokens": 2217, "wait_time": 30, "api_key_rotation": false, "timestamp": "2025-09-04T16:23:55.539452", "attempt": 2}, {"image_name": "frame_0000_000.jpg", "error": "Generated content is not valid YAML. Error: mapping values are not allowed here\n  in \"<unicode string>\", line 149, column 33:\n              label: ADVANCED SEARCH:\n                                    ^", "tokens": 2233, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-09-04T16:31:03.913897", "attempt": 3}, {"image_name": "frame_0000_000.jpg", "error": "Generated content is not valid YAML. Error: mapping values are not allowed here\n  in \"<unicode string>\", line 110, column 33:\n              label: ADVANCED SEARCH:\n                                    ^", "tokens": 2233, "wait_time": 30, "api_key_rotation": false, "timestamp": "2025-09-04T16:32:02.534717", "attempt": 4}, {"image_name": "frame_0000_000.jpg", "error": "Generated content is not valid YAML. Error: mapping values are not allowed here\n  in \"<unicode string>\", line 210, column 33:\n              label: ADVANCED SEARCH:\n                                    ^", "tokens": 2233, "wait_time": 45, "api_key_rotation": false, "timestamp": "2025-09-04T16:33:19.098818", "attempt": 5}, {"image_name": "frame_0000_000.jpg", "error": "Generated content is not valid YAML. Error: mapping values are not allowed here\n  in \"<unicode string>\", line 149, column 33:\n              label: ADVANCED SEARCH:\n                                    ^", "tokens": 2233, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-09-04T16:35:06.511356", "attempt": 6}, {"image_name": "frame_0000_000.jpg", "error": "Generated content is not valid YAML. Error: mapping values are not allowed here\n  in \"<unicode string>\", line 210, column 33:\n              label: ADVANCED SEARCH:\n                                    ^", "tokens": 2233, "wait_time": 30, "api_key_rotation": false, "timestamp": "2025-09-04T16:36:05.666224", "attempt": 7}, {"image_name": "frame_0000_000.jpg", "error": "Generated content is not valid YAML. Error: mapping values are not allowed here\n  in \"<unicode string>\", line 110, column 33:\n              label: ADVANCED SEARCH:\n                                    ^", "tokens": 2233, "wait_time": 45, "api_key_rotation": false, "timestamp": "2025-09-04T16:37:17.356994", "attempt": 8}, {"image_name": "frame_0000_000.jpg", "success": "Able to generate UI elements", "tokens": 2252, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-04T16:50:45.143825", "attempt": 9}, {"image_name": "frame_0001_000.jpg", "success": "Able to generate UI elements", "tokens": 5368, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-04T16:56:03.128439", "attempt": 1}, {"image_name": "frame_0002_000.jpg", "success": "Able to generate UI elements", "tokens": 8546, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-04T16:56:50.982410", "attempt": 1}, {"image_name": "frame_0003_001.jpg", "success": "Able to generate UI elements", "tokens": 11721, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-04T16:57:42.971454", "attempt": 1}, {"image_name": "frame_0004_001.jpg", "success": "Able to generate UI elements", "tokens": 14891, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-04T16:59:19.876313", "attempt": 1}, {"image_name": "frame_0005_001.jpg", "success": "Able to generate UI elements", "tokens": 18065, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-04T17:03:40.140560", "attempt": 1}, {"image_name": "frame_0006_002.jpg", "success": "Able to generate UI elements", "tokens": 21240, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-04T17:04:24.154303", "attempt": 1}, {"image_name": "frame_0007_002.jpg", "success": "Able to generate UI elements", "tokens": 24415, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-04T17:05:56.687008", "attempt": 1}, {"image_name": "frame_0008_003.jpg", "success": "Able to generate UI elements", "tokens": 27590, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-04T17:06:34.654070", "attempt": 1}, {"image_name": "frame_0009_003.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 2\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 48\n}\n]", "tokens": null, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-09-04T17:07:12.561036", "attempt": 1}, {"image_name": "frame_0009_003.jpg", "success": "Able to generate UI elements", "tokens": 30765, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-04T17:08:26.917755", "attempt": 2}, {"image_name": "frame_0010_003.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 50\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 22\n}\n]", "tokens": null, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-09-04T17:08:38.383137", "attempt": 1}, {"image_name": "frame_0010_003.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 50\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 4\n}\n]", "tokens": null, "wait_time": 30, "api_key_rotation": false, "timestamp": "2025-09-04T17:08:56.076663", "attempt": 2}, {"image_name": "frame_0031_012.jpg", "success": "Able to generate UI elements", "tokens": 2258, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-04T17:22:24.201076", "attempt": 1}, {"image_name": "frame_0098_038.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 50\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 48\n}\n]", "tokens": null, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-09-04T17:24:12.971005", "attempt": 1}, {"image_name": "frame_0098_038.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 50\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 53\n}\n]", "tokens": null, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-09-04T17:28:07.389794", "attempt": 2}, {"image_name": "frame_0098_038.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 50\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 31\n}\n]", "tokens": null, "wait_time": 30, "api_key_rotation": false, "timestamp": "2025-09-04T17:28:29.868328", "attempt": 3}, {"image_name": "frame_0098_038.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 50\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 58\n}\n]", "tokens": null, "wait_time": 45, "api_key_rotation": false, "timestamp": "2025-09-04T17:29:02.868246", "attempt": 4}, {"image_name": "frame_0098_038.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 50\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n}\n]", "tokens": null, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-09-04T17:31:00.263329", "attempt": 5}, {"image_name": "frame_0098_038.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 50\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 42\n}\n]", "tokens": null, "wait_time": 30, "api_key_rotation": false, "timestamp": "2025-09-04T17:31:18.274134", "attempt": 6}, {"image_name": "frame_0098_038.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 50\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 9\n}\n]", "tokens": null, "wait_time": 45, "api_key_rotation": false, "timestamp": "2025-09-04T17:31:51.221098", "attempt": 7}, {"image_name": "frame_0098_038.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 2\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 41\n}\n]", "tokens": null, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-09-04T17:33:19.466863", "attempt": 8}, {"image_name": "frame_0098_038.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 50\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 56\n}\n]", "tokens": null, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-09-04T17:37:05.013201", "attempt": 9}, {"image_name": "frame_0098_038.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 50\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 13\n}\n]", "tokens": null, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-09-04T17:37:47.552416", "attempt": 10}, {"image_name": "frame_0098_038.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 50\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 54\n}\n]", "tokens": null, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-09-04T17:39:06.757210", "attempt": 11}, {"image_name": "frame_0098_038.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 50\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 36\n}\n]", "tokens": null, "wait_time": 30, "api_key_rotation": false, "timestamp": "2025-09-04T17:39:24.763261", "attempt": 12}, {"image_name": "frame_0098_038.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 50\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 3\n}\n]", "tokens": null, "wait_time": 45, "api_key_rotation": false, "timestamp": "2025-09-04T17:39:57.827908", "attempt": 13}, {"image_name": "frame_0098_038.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 2\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 34\n}\n]", "tokens": null, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-09-04T17:45:26.252155", "attempt": 14}, {"image_name": "frame_0098_038.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 2\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 34\n}\n]", "tokens": null, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-09-04T17:46:26.485237", "attempt": 15}, {"image_name": "frame_0098_038.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 2\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 41\n}\n]", "tokens": null, "wait_time": 30, "api_key_rotation": false, "timestamp": "2025-09-04T17:47:19.979897", "attempt": 16}, {"image_name": "frame_0098_038.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 2\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 19\n}\n]", "tokens": null, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-09-04T17:51:41.798698", "attempt": 17}, {"image_name": "frame_0098_038.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 2\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 6\n}\n]", "tokens": null, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-09-04T17:52:54.473660", "attempt": 18}, {"image_name": "frame_0098_038.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 2\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 17\n}\n]", "tokens": null, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-09-04T18:01:43.682067", "attempt": 19}, {"image_name": "frame_0098_038.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 2\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 59\n}\n]", "tokens": null, "wait_time": 30, "api_key_rotation": false, "timestamp": "2025-09-04T18:02:01.243189", "attempt": 20}, {"image_name": "frame_0098_038.jpg", "error": "Generated content is not valid YAML. Error: mapping values are not allowed here\n  in \"<unicode string>\", line 317, column 29:\n          label: ADVANCED SEARCH:\n                                ^", "tokens": 6602, "wait_time": 45, "api_key_rotation": false, "timestamp": "2025-09-04T18:04:44.629134", "attempt": 21}, {"image_name": "frame_0098_038.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 2\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 2\n}\n]", "tokens": null, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-09-04T18:08:58.343880", "attempt": 22}, {"image_name": "frame_0098_038.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 2\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 44\n}\n]", "tokens": null, "wait_time": 30, "api_key_rotation": false, "timestamp": "2025-09-04T18:09:16.316817", "attempt": 23}, {"image_name": "frame_0098_038.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 2\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 51\n}\n]", "tokens": null, "wait_time": 45, "api_key_rotation": false, "timestamp": "2025-09-04T18:10:10.085715", "attempt": 24}, {"image_name": "frame_0098_038.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 2\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 57\n}\n]", "tokens": null, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-09-04T18:14:03.769945", "attempt": 25}, {"image_name": "frame_0098_038.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 2\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 24\n}\n]", "tokens": null, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-09-04T18:15:36.292145", "attempt": 26}, {"image_name": "frame_0098_038.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 2\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 5\n}\n]", "tokens": null, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-09-04T18:15:55.860253", "attempt": 27}, {"image_name": "frame_0098_038.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 2\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 19\n}\n]", "tokens": null, "wait_time": 30, "api_key_rotation": false, "timestamp": "2025-09-04T18:16:41.471527", "attempt": 28}, {"image_name": "frame_0098_038.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 2\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 26\n}\n]", "tokens": null, "wait_time": 45, "api_key_rotation": false, "timestamp": "2025-09-04T18:17:35.012458", "attempt": 29}, {"image_name": "frame_0098_038.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 2\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 8\n}\n]", "tokens": null, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-09-04T18:26:52.106328", "attempt": 30}, {"image_name": "frame_0098_038.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 2\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 14\n}\n]", "tokens": null, "wait_time": 30, "api_key_rotation": false, "timestamp": "2025-09-04T18:27:46.815117", "attempt": 31}, {"image_name": "frame_0010_003.jpg", "error": "Generated content is not valid YAML. Error: mapping values are not allowed here\n  in \"<unicode string>\", line 107, column 29:\n          label: ADVANCED SEARCH:\n                                ^", "tokens": 6602, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-09-05T08:31:53.197826", "attempt": 3}, {"image_name": "frame_0010_003.jpg", "success": "Able to generate UI elements", "tokens": 6602, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T08:32:53.159860", "attempt": 4}, {"image_name": "frame_0011_004.jpg", "success": "Able to generate UI elements", "tokens": 9623, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T08:33:41.453570", "attempt": 1}, {"image_name": "frame_0012_004.jpg", "success": "Able to generate UI elements", "tokens": 12644, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T08:34:27.018234", "attempt": 1}, {"image_name": "frame_0013_005.jpg", "success": "Able to generate UI elements", "tokens": 15665, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T08:35:21.851860", "attempt": 1}, {"image_name": "frame_0015_005.jpg", "success": "Able to generate UI elements", "tokens": 18686, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T08:36:04.370230", "attempt": 1}, {"image_name": "frame_0016_006.jpg", "success": "Able to generate UI elements", "tokens": 21707, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T08:36:38.763266", "attempt": 1}, {"image_name": "frame_0017_006.jpg", "success": "Able to generate UI elements", "tokens": 24728, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T08:37:13.164364", "attempt": 1}, {"image_name": "frame_0018_007.jpg", "success": "Able to generate UI elements", "tokens": 27748, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T08:38:03.005403", "attempt": 1}, {"image_name": "frame_0019_007.jpg", "success": "Able to generate UI elements", "tokens": 33177, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T08:38:54.670415", "attempt": 1}, {"image_name": "frame_0020_007.jpg", "success": "Able to generate UI elements", "tokens": 38605, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T08:39:51.985721", "attempt": 1}, {"image_name": "frame_0021_008.jpg", "success": "Able to generate UI elements", "tokens": 44038, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T08:40:43.646729", "attempt": 1}, {"image_name": "frame_0022_008.jpg", "success": "Able to generate UI elements", "tokens": 49471, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T08:41:31.227613", "attempt": 1}, {"image_name": "frame_0023_009.jpg", "success": "Able to generate UI elements", "tokens": 54838, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T08:42:26.063524", "attempt": 1}, {"image_name": "frame_0024_009.jpg", "success": "Able to generate UI elements", "tokens": 60044, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T08:43:25.804324", "attempt": 1}, {"image_name": "frame_0025_009.jpg", "success": "Able to generate UI elements", "tokens": 64540, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T08:44:20.145770", "attempt": 1}, {"image_name": "frame_0026_010.jpg", "success": "Able to generate UI elements", "tokens": 69897, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T08:45:25.158106", "attempt": 1}, {"image_name": "frame_0027_010.jpg", "success": "Able to generate UI elements", "tokens": 75368, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T08:46:21.329231", "attempt": 1}, {"image_name": "frame_0028_011.jpg", "success": "Able to generate UI elements", "tokens": 80839, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T08:47:20.364530", "attempt": 1}, {"image_name": "frame_0030_011.jpg", "success": "Able to generate UI elements", "tokens": 86196, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T08:48:19.692793", "attempt": 1}, {"image_name": "frame_0032_012.jpg", "success": "Able to generate UI elements", "tokens": 91673, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T08:48:57.523014", "attempt": 1}, {"image_name": "frame_0033_012.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerMinute-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 125000\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n}\n]", "tokens": null, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-09-05T08:49:00.648178", "attempt": 1}, {"image_name": "frame_0033_012.jpg", "success": "Able to generate UI elements", "tokens": 94694, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T08:50:01.270835", "attempt": 2}, {"image_name": "frame_0034_013.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerMinute-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 125000\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 56\n}\n]", "tokens": null, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-09-05T08:50:04.025246", "attempt": 1}, {"image_name": "frame_0034_013.jpg", "success": "Able to generate UI elements", "tokens": 97715, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T08:50:56.625741", "attempt": 2}, {"image_name": "frame_0035_013.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerMinute-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 125000\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 1\n}\n]", "tokens": null, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-09-05T08:50:59.459819", "attempt": 1}, {"image_name": "frame_0035_013.jpg", "success": "Able to generate UI elements", "tokens": 100736, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T08:51:58.796766", "attempt": 2}, {"image_name": "frame_0036_014.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerMinute-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 125000\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 59\n}\n]", "tokens": null, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-09-05T08:52:01.695909", "attempt": 1}, {"image_name": "frame_0036_014.jpg", "success": "Able to generate UI elements", "tokens": 104203, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T08:53:00.225755", "attempt": 2}, {"image_name": "frame_0037_014.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerMinute-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 125000\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 57\n}\n]", "tokens": null, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-09-05T08:53:03.166335", "attempt": 1}, {"image_name": "frame_0037_014.jpg", "success": "Able to generate UI elements", "tokens": 40067, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T09:59:05.170650", "attempt": 2}, {"image_name": "frame_0038_014.jpg", "success": "Able to generate UI elements", "tokens": 43161, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T09:59:43.795185", "attempt": 1}, {"image_name": "frame_0039_015.jpg", "success": "Able to generate UI elements", "tokens": 46326, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T10:00:39.164928", "attempt": 1}, {"image_name": "frame_0040_015.jpg", "success": "Able to generate UI elements", "tokens": 49355, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T10:01:17.467090", "attempt": 1}, {"image_name": "frame_0045_017.jpg", "success": "Able to generate UI elements", "tokens": 52383, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T10:02:22.887120", "attempt": 1}, {"image_name": "frame_0046_018.jpg", "success": "Able to generate UI elements", "tokens": 59318, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T10:03:27.240941", "attempt": 1}, {"image_name": "frame_0047_018.jpg", "success": "Able to generate UI elements", "tokens": 66253, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T10:04:30.830440", "attempt": 1}, {"image_name": "frame_0048_018.jpg", "success": "Able to generate UI elements", "tokens": 73188, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T10:05:34.412254", "attempt": 1}, {"image_name": "frame_0049_019.jpg", "success": "Able to generate UI elements", "tokens": 80123, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T10:06:39.695071", "attempt": 1}, {"image_name": "frame_0050_019.jpg", "success": "Able to generate UI elements", "tokens": 87058, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T10:07:43.103922", "attempt": 1}, {"image_name": "frame_0051_020.jpg", "success": "Able to generate UI elements", "tokens": 93988, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T10:08:52.243541", "attempt": 1}, {"image_name": "frame_0052_020.jpg", "success": "Able to generate UI elements", "tokens": 100923, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T10:09:58.348787", "attempt": 1}, {"image_name": "frame_0053_020.jpg", "success": "Able to generate UI elements", "tokens": 107858, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T10:11:03.747156", "attempt": 1}, {"image_name": "frame_0054_021.jpg", "success": "Able to generate UI elements", "tokens": 114793, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T10:12:23.510070", "attempt": 1}, {"image_name": "frame_0055_021.jpg", "success": "Able to generate UI elements", "tokens": 121728, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T10:13:30.929167", "attempt": 1}, {"image_name": "frame_0056_022.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerMinute-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 125000\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 25\n}\n]", "tokens": null, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-09-05T10:13:34.730701", "attempt": 1}, {"image_name": "frame_0056_022.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerMinute-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 125000\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 7\n}\n]", "tokens": null, "wait_time": 30, "api_key_rotation": false, "timestamp": "2025-09-05T10:13:53.006555", "attempt": 2}, {"image_name": "frame_0056_022.jpg", "success": "Able to generate UI elements", "tokens": 65065, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T10:37:02.817454", "attempt": 3}, {"image_name": "frame_0057_022.jpg", "success": "Able to generate UI elements", "tokens": 72373, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T10:38:17.713310", "attempt": 1}, {"image_name": "frame_0058_022.jpg", "success": "Able to generate UI elements", "tokens": 79681, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T10:39:34.216507", "attempt": 1}, {"image_name": "frame_0059_023.jpg", "success": "Able to generate UI elements", "tokens": 86994, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T10:40:47.897135", "attempt": 1}, {"image_name": "frame_0060_023.jpg", "success": "Able to generate UI elements", "tokens": 94302, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T10:42:03.486312", "attempt": 1}, {"image_name": "frame_0061_023.jpg", "success": "Able to generate UI elements", "tokens": 101615, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T10:43:12.955607", "attempt": 1}, {"image_name": "frame_0062_024.jpg", "success": "Able to generate UI elements", "tokens": 108928, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T10:44:22.445738", "attempt": 1}, {"image_name": "frame_0063_024.jpg", "success": "Able to generate UI elements", "tokens": 116236, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T10:45:30.987319", "attempt": 1}, {"image_name": "frame_0064_025.jpg", "success": "Able to generate UI elements", "tokens": 123549, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T10:46:41.556208", "attempt": 1}, {"image_name": "frame_0065_025.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerMinute-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 125000\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 15\n}\n]", "tokens": null, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-09-05T10:46:44.923645", "attempt": 1}, {"image_name": "frame_0065_025.jpg", "success": "Able to generate UI elements", "tokens": 130857, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T10:48:25.712226", "attempt": 2}, {"image_name": "frame_0066_025.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerMinute-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 125000\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 31\n}\n]", "tokens": null, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-09-05T10:48:28.685206", "attempt": 1}, {"image_name": "frame_0066_025.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerMinute-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 125000\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 13\n}\n]", "tokens": null, "wait_time": 30, "api_key_rotation": false, "timestamp": "2025-09-05T10:48:46.582741", "attempt": 2}, {"image_name": "frame_0066_025.jpg", "success": "Able to generate UI elements", "tokens": 138165, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T10:51:53.365229", "attempt": 3}, {"image_name": "frame_0067_026.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerMinute-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 125000\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 4\n}\n]", "tokens": null, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-09-05T10:51:56.294135", "attempt": 1}, {"image_name": "frame_0067_026.jpg", "success": "Able to generate UI elements", "tokens": 67880, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T11:09:15.005234", "attempt": 2}, {"image_name": "frame_0068_026.jpg", "success": "Able to generate UI elements", "tokens": 75478, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T11:10:29.316585", "attempt": 1}, {"image_name": "frame_0069_027.jpg", "success": "Able to generate UI elements", "tokens": 82475, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T11:11:50.075312", "attempt": 1}, {"image_name": "frame_0070_027.jpg", "success": "Able to generate UI elements", "tokens": 89420, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T11:13:12.572770", "attempt": 1}, {"image_name": "frame_0071_027.jpg", "success": "Able to generate UI elements", "tokens": 96362, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T11:14:41.710238", "attempt": 1}, {"image_name": "frame_0072_028.jpg", "success": "Able to generate UI elements", "tokens": 103634, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T11:16:52.731682", "attempt": 1}, {"image_name": "frame_0073_028.jpg", "success": "Able to generate UI elements", "tokens": 110574, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T11:18:02.359731", "attempt": 1}, {"image_name": "frame_0074_029.jpg", "success": "Able to generate UI elements", "tokens": 117641, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T11:19:17.115936", "attempt": 1}, {"image_name": "frame_0075_029.jpg", "success": "Able to generate UI elements", "tokens": 124716, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T11:20:27.328545", "attempt": 1}, {"image_name": "frame_0076_029.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerMinute-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 125000\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 30\n}\n]", "tokens": null, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-09-05T11:20:30.347300", "attempt": 1}, {"image_name": "frame_0076_029.jpg", "success": "Able to generate UI elements", "tokens": 131662, "wait_time": 0, "api_key_rotation": false, "timestamp": "2025-09-05T11:22:30.079565", "attempt": 2}, {"image_name": "frame_0077_030.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerMinute-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 125000\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 27\n}\n]", "tokens": null, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-09-05T11:22:33.379025", "attempt": 1}, {"image_name": "frame_0077_030.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerMinute-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 125000\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 19\n}\n]", "tokens": null, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-09-05T12:57:41.021639", "attempt": 2}, {"image_name": "frame_0077_030.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerMinute-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 125000\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 1\n}\n]", "tokens": null, "wait_time": 30, "api_key_rotation": false, "timestamp": "2025-09-05T12:57:58.886096", "attempt": 3}, {"image_name": "frame_0077_030.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 2\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 28\n}\n]", "tokens": null, "wait_time": 45, "api_key_rotation": false, "timestamp": "2025-09-05T12:58:31.626803", "attempt": 4}, {"image_name": "frame_0077_030.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerMinute-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 125000\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 26\n}\n]", "tokens": null, "wait_time": 15, "api_key_rotation": false, "timestamp": "2025-09-05T13:13:33.480560", "attempt": 5}, {"image_name": "frame_0077_030.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerMinute-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 125000\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 9\n}\n]", "tokens": null, "wait_time": 30, "api_key_rotation": false, "timestamp": "2025-09-05T13:13:51.231206", "attempt": 6}, {"image_name": "frame_0077_030.jpg", "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 2\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 36\n}\n]", "tokens": null, "wait_time": 45, "api_key_rotation": false, "timestamp": "2025-09-05T13:14:24.002674", "attempt": 7}]