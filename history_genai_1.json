[{"role": "model", "parts": ["```yaml\nbrowser_component:\n  tab_title: HH Insurance Propos...\n  url: ai.iscs.com/innovation\n  address_bar_focused: false\n  tab_state: idle\nwebpage:\n  header:\n  - component_type: header\n    id: main_header\n    bounds: {x: 0, y: 80, width: 1920, height: 55}\n    subcomponents:\n    - component_type: image\n      id: logo_american_integrity\n      label: AMERICAN INTEGRITY\n      bounds: {x: 24, y: 99, width: 135, height: 21}\n      state: null\n      value: null\n    - component_type: navigation\n      id: main_navigation\n      bounds: {x: 1290, y: 90, width: 580, height: 45}\n      subcomponents:\n      - component_type: link\n        id: nav_home\n        label: Home\n        bounds: {x: 1295, y: 110, width: 40, height: 20}\n        state: active\n        value: null\n      - component_type: link\n        id: nav_quote_policy\n        label: Quote/Policy\n        bounds: {x: 1365, y: 110, width: 85, height: 20}\n        state: null\n        value: null\n      - component_type: link\n        id: nav_claims\n        label: Claims\n        bounds: {x: 1480, y: 110, width: 50, height: 20}\n        state: null\n        value: null\n      - component_type: link\n        id: nav_cabinets\n        label: Cabinets\n        bounds: {x: 1560, y: 110, width: 65, height: 20}\n        state: null\n        value: null\n      - component_type: link\n        id: nav_support\n        label: Support\n        bounds: {x: 1655, y: 110, width: 55, height: 20}\n        state: null\n        value: null\n      - component_type: button\n        id: btn_more\n        label: '... MORE'\n        bounds: {x: 1740, y: 108, width: 60, height: 25}\n        state: null\n        value: null\n  sidebar:\n  - component_type: sidebar\n    id: left_sidebar\n    bounds: {x: 0, y: 135, width: 250, height: 850}\n    subcomponents:\n    - component_type: input\n      id: input_search\n      label: Search\n      bounds: {x: 15, y: 155, width: 185, height: 30}\n      state: null\n      value: ''\n    - component_type: button\n      id: btn_search\n      label: null\n      bounds: {x: 200, y: 155, width: 30, height: 30}\n      state: null\n      value: null\n    - component_type: text\n      id: text_advanced_search\n      label: 'ADVANCED SEARCH:'\n      bounds: {x: 15, y: 195, width: 110, height: 15}\n      state: null\n      value: null\n    - component_type: link\n      id: link_policy\n      label: POLICY\n      bounds: {x: 130, y: 195, width: 45, height: 15}\n      state: null\n      value: null\n    - component_type: link\n      id: link_claims\n      label: CLAIMS\n      bounds: {x: 180, y: 195, width: 45, height: 15}\n      state: null\n      value: null\n    - component_type: navigation\n      id: sidebar_nav\n      bounds: {x: 0, y: 220, width: 250, height: 120}\n      subcomponents:\n      - component_type: link\n        id: nav_news\n        label: News\n        bounds: {x: 0, y: 225, width: 250, height: 40}\n        state: selected\n        value: null\n      - component_type: link\n        id: nav_inbox\n        label: Inbox\n        bounds: {x: 0, y: 265, width: 250, height: 40}\n        state: null\n        value: null\n        subcomponents:\n        - component_type: badge\n          id: badge_inbox_count\n          label: '152'\n          bounds: {x: 210, y: 275, width: 25, height: 20}\n          state: null\n          value: null\n      - component_type: link\n        id: nav_recent_list\n        label: Recent List\n        bounds: {x: 0, y: 305, width: 250, height: 40}\n        state: null\n        value: null\n  - component_type: sidebar\n    id: right_sidebar\n    bounds: {x: 1875, y: 135, width: 45, height: 850}\n    subcomponents:\n    - component_type: button\n      id: btn_quick_quote\n      label: WTRCRAFT QUICK QT\n      bounds: {x: 1880, y: 145, width: 35, height: 55}\n      state: null\n      value: null\n    - component_type: button\n      id: btn_new_quote\n      label: NEW QUOTE\n      bounds: {x: 1880, y: 210, width: 35, height: 55}\n      state: null\n      value: null\n  main_content:\n  - component_type: container\n    id: main_content_area\n    bounds: {x: 250, y: 135, width: 1625, height: 850}\n    subcomponents:\n    - component_type: text\n      id: title_news_announcements\n      label: News & Announcements\n      bounds: {x: 270, y: 155, width: 200, height: 20}\n      state: null\n      value: null\n    - component_type: container\n      id: webinar_section\n      bounds: {x: 270, y: 200, width: 1580, height: 380}\n      subcomponents:\n      - component_type: text\n        id: webinar_title\n        label: Navigating Challenges in the National Insurance Market Webinar\n        bounds: {x: 490, y: 250, width: 940, height: 25}\n        state: null\n        value: null\n      - component_type: text\n        id: webinar_date\n        label: Thursday, June 12 at 3:00 - 4:00pm EST\n        bounds: {x: 790, y: 285, width: 340, height: 20}\n        state: null\n        value: null\n      - component_type: text\n        id: webinar_intro_text\n        label: Please join our CEO Bob Ritchie and special guest, Mark Friedlander of the\n          Insurance Information Institute for an insightful discussion on the latest\n          market trends, their impact, and solutions for navigating this complex landscape.\n        bounds: {x: 310, y: 320, width: 1300, height: 35}\n        state: null\n        value: null\n      - component_type: text\n        id: webinar_topics_title\n        label: 'Topics and Speakers Include:'\n        bounds: {x: 310, y: 365, width: 180, height: 20}\n        state: null\n        value: null\n      - component_type: list\n        id: webinar_speakers_list\n        bounds: {x: 325, y: 390, width: 650, height: 100}\n        subcomponents:\n        - component_type: list_item\n          id: speaker_1\n          label: National Weather Impacts - Bob Ritchie, CEO\n        - component_type: list_item\n          id: speaker_2\n          label: National Legislative Landscape - Mark Friedlander, Triple-I Guest Speaker\n        - component_type: list_item\n          id: speaker_3\n          label: American Integrity Market Response - Dick Dowd, EVP of Sales & Marketing\n        - component_type: list_item\n          id: speaker_4\n          label: Florida Property Insurance Market Results - Brent Radeloff, EVP of Product,\n            Pricing & Underwriting\n        - component_type: list_item\n          id: speaker_5\n          label: Storm Trends - Natalie Ferrari, Emmy-winning meteorologist & Catastrophic\n            Risk Analyst\n      - component_type: link\n        id: link_register_webinar\n        label: Click Here to Register for Our Webinar\n        bounds: {x: 820, y: 510, width: 280, height: 20}\n        state: null\n        value: null\n      - component_type: text\n        id: webinar_note_1\n        label: 'Please note: if you previously registered, you will need to re-register.'\n        bounds: {x: 310, y: 550, width: 420, height: 15}\n        state: null\n        value: null\n      - component_type: text\n        id: webinar_note_2\n        label: If you can't join, register anyway and we'll send you the slides following\n          the webinar!\n        bounds: {x: 310, y: 570, width: 500, height: 15}\n        state: null\n        value: null\n    - component_type: container\n      id: flood_capacity_section\n      bounds: {x: 270, y: 600, width: 1580, height: 180}\n      subcomponents:\n      - component_type: text\n        id: flood_title\n        label: Flood Capacity Update\n        bounds: {x: 860, y: 615, width: 200, height: 25}\n        state: null\n        value: null\n      - component_type: text\n        id: flood_text_1\n        label: Our flood endorsement is currently available in all counties except\n          Collier and Lee.\n        bounds: {x: 310, y: 660, width: 550, height: 15}\n        state: null\n        value: null\n      - component_type: text\n        id: flood_text_2\n        label: Please note that risks located in previously closed counties will require\n          confirmation of no flood related losses prior to the submission of any quote\n          or bind requests.\n        bounds: {x: 310, y: 700, width: 800, height: 15}\n        state: null\n        value: null\n      - component_type: text\n        id: flood_text_3\n        label: As Hurricanes Helene and Milton proved, EVERY home is in a flood zone\n          - so this is the perfect time to talk to your customers about this important\n          coverage.\n        bounds: {x: 310, y: 740, width: 850, height: 15}\n        state: null\n        value: null\n    - component_type: container\n      id: workflow_tips_section\n      bounds: {x: 270, y: 800, width: 1580, height: 100}\n      subcomponents:\n      - component_type: text\n        id: workflow_title\n        label: Streamline Your Workflow With These Tips!\n        bounds: {x: 800, y: 820, width: 320, height: 25}\n        state: null\n        value: null\n      - component_type: text\n        id: workflow_text\n        label: We understand that our quoting and binding online platform, SPIN, can\n          sometimes be a little tricky to navigate. Check out the below job aids to\n          overcome common roadblocks.\n        bounds: {x: 310, y: 860, width: 950, height: 15}\n        state: null\n        value: null\n    - component_type: container\n      id: email_notifications_section\n      bounds: {x: 270, y: 900, width: 1580, height: 100}\n      subcomponents:\n      - component_type: text\n        id: email_title\n        label: Not Receiving Email Notifications?\n        bounds: {x: 310, y: 905, width: 200, height: 15}\n        state: null\n        value: null\n      - component_type: text\n        id: email_text\n        label: The My Subscriptions feature in SPIN ensures agents receive email notifications\n          when a task that requires review is assigned by Underwriting. If your subscriptions\n          aren't set up properly, or if the email notifications are going to the wrong\n          email address, you may miss critical updates.\n        bounds: {x: 310, y: 925, width: 1100, height: 15}\n        state: null\n        value: null\n```"]}, {"role": "model", "parts": ["```yaml\nbrowser_component:\n  tab_title: HH Insurance Propos...\n  url: drive.google.com/drive/folders/1v773cxo-vfGpoJOYt9CYY5YGcpiLe5TL\n  address_bar_focused: false\n  tab_state: idle\nwebpage:\n  header:\n  - component_type: header\n    id: google_drive_header\n    bounds: {x: 0, y: 64, width: 1920, height: 56}\n    subcomponents:\n    - component_type: text\n      id: file_name_header\n      label: Cassidy HO3 AI.pdf\n      bounds: {x: 48, y: 88, width: 150, height: 24}\n      state: null\n      value: null\n    - component_type: input\n      id: search_bar\n      label: Search in Drive\n      bounds: {x: 130, y: 80, width: 700, height: 40}\n      state: null\n      value: null\n    - component_type: button\n      id: btn_open_with_google_docs\n      label: Open with Google Docs\n      bounds: {x: 870, y: 80, width: 180, height: 40}\n      state: null\n      value: null\n    - component_type: button\n      id: btn_share\n      label: Share\n      bounds: {x: 1820, y: 80, width: 80, height: 40}\n      state: null\n      value: null\n  sidebar:\n  - component_type: sidebar\n    id: google_drive_sidebar\n    bounds: {x: 0, y: 120, width: 256, height: 860}\n    subcomponents:\n    - component_type: button\n      id: btn_new\n      label: + New\n      bounds: {x: 16, y: 136, width: 100, height: 48}\n      state: null\n      value: null\n    - component_type: navigation\n      id: sidebar_nav\n      bounds: {x: 0, y: 200, width: 256, height: 400}\n      subcomponents:\n      - component_type: link\n        id: nav_home\n        label: Home\n        bounds: {x: 0, y: 210, width: 256, height: 32}\n        state: null\n        value: null\n      - component_type: link\n        id: nav_my_drive\n        label: My Drive\n        bounds: {x: 0, y: 250, width: 256, height: 32}\n        state: null\n        value: null\n      - component_type: link\n        id: nav_computers\n        label: Computers\n        bounds: {x: 0, y: 290, width: 256, height: 32}\n        state: null\n        value: null\n      - component_type: link\n        id: nav_shared_with_me\n        label: Shared with me\n        bounds: {x: 0, y: 330, width: 256, height: 32}\n        state: selected\n        value: null\n      - component_type: link\n        id: nav_recent\n        label: Recent\n        bounds: {x: 0, y: 370, width: 256, height: 32}\n        state: null\n        value: null\n      - component_type: link\n        id: nav_starred\n        label: Starred\n        bounds: {x: 0, y: 410, width: 256, height: 32}\n        state: null\n        value: null\n      - component_type: link\n        id: nav_spam\n        label: Spam\n        bounds: {x: 0, y: 450, width: 256, height: 32}\n        state: null\n        value: null\n      - component_type: link\n        id: nav_trash\n        label: Trash\n        bounds: {x: 0, y: 490, width: 256, height: 32}\n        state: null\n        value: null\n      - component_type: link\n        id: nav_storage\n        label: Storage\n        bounds: {x: 0, y: 530, width: 256, height: 32}\n        state: null\n        value: null\n    - component_type: text\n      id: storage_usage\n      label: 310.4 MB of 15 GB used\n      bounds: {x: 24, y: 570, width: 150, height: 16}\n      state: null\n      value: null\n    - component_type: button\n      id: btn_get_more_storage\n      label: Get more storage\n      bounds: {x: 24, y: 610, width: 120, height: 36}\n      state: null\n      value: null\n  main_content:\n  - component_type: container\n    id: file_list_container\n    bounds: {x: 256, y: 120, width: 1664, height: 860}\n    subcomponents:\n    - component_type: text\n      id: breadcrumb_shared_with_me\n      label: Shared with me\n      bounds: {x: 280, y: 140, width: 150, height: 24}\n      state: null\n      value: null\n    - component_type: text\n      id: breadcrumb_processing\n      label: Processing\n      bounds: {x: 440, y: 140, width: 100, height: 24}\n      state: null\n      value: null\n    - component_type: table\n      id: file_list_table\n      bounds: {x: 280, y: 220, width: 1620, height: 600}\n      headers:\n      - Name\n      - Last modified\n      - File size\n      rows:\n      - id: row_1\n        cells:\n        - component_type: text\n          label: Troyer HO3 AI.pdf\n        - component_type: text\n          label: May 23, 2025 me\n        - component_type: text\n          label: 140 KB\n      - id: row_2\n        cells:\n        - component_type: text\n          label: Towns HO3 AI.pdf\n        - component_type: text\n          label: May 23, 2025 me\n        - component_type: text\n          label: 139 KB\n      - id: row_3\n        cells:\n        - component_type: text\n          label: Rowen HO3 AI.pdf\n        - component_type: text\n          label: May 23, 2025 me\n        - component_type: text\n          label: 139 KB\n      - id: row_4\n        cells:\n        - component_type: text\n          label: Guevara HO3 AI.pdf\n        - component_type: text\n          label: May 23, 2025 me\n        - component_type: text\n          label: 139 KB\n      - id: row_5\n        cells:\n        - component_type: text\n          label: Grady HO3 AI.pdf\n        - component_type: text\n          label: May 23, 2025 me\n        - component_type: text\n          label: 139 KB\n      - id: row_6\n        cells:\n        - component_type: text\n          label: Cassidy HO3 AI.pdf\n        - component_type: text\n          label: May 23, 2025 me\n        - component_type: text\n          label: 277 KB\n  overlay:\n  - component_type: overlay\n    id: pdf_viewer\n    bounds: {x: 256, y: 120, width: 1664, height: 860}\n    subcomponents:\n    - component_type: container\n      id: pdf_document\n      bounds: {x: 300, y: 130, width: 1320, height: 800}\n      subcomponents:\n      - component_type: image\n        id: logo_american_integrity\n        label: AMERICAN INTEGRITY logo\n        bounds: {x: 320, y: 220, width: 200, height: 50}\n        state: null\n        value: null\n      - component_type: text\n        id: insured_address\n        label: |-\n          Landon Cassidy\n          4227 5th AVE S\n          St Petersburg, FL 33711-1522\n        bounds: {x: 320, y: 280, width: 200, height: 50}\n        state: null\n        value: null\n      - component_type: text\n        id: insurer_address\n        label: |-\n          HH Insurance Group, LLC\n          9887 4th St N Ste 200\n          St Petersburg, FL 33702-2451\n          (727) 498-5551\n        bounds: {x: 550, y: 280, width: 200, height: 60}\n        state: null\n        value: null\n      - component_type: text\n        id: quote_number\n        label: 'QUOTE NUMBER: QT-15441432'\n        bounds: {x: 320, y: 360, width: 200, height: 20}\n        state: null\n        value: null\n      - component_type: text\n        id: effective_date\n        label: 'Effective Date: 06/20/2025 12:01am'\n        bounds: {x: 320, y: 380, width: 250, height: 15}\n        state: null\n        value: null\n      - component_type: text\n        id: effective_date_note\n        label: STANDARD TIME at the residence premises\n        bounds: {x: 320, y: 395, width: 250, height: 15}\n        state: null\n        value: null\n      - component_type: text\n        id: expiration_date\n        label: 'Expiration Date: 06/20/2026 12:01am'\n        bounds: {x: 550, y: 380, width: 250, height: 15}\n        state: null\n        value: null\n      - component_type: text\n        id: expiration_date_note\n        label: STANDARD TIME at the residence premises\n        bounds: {x: 550, y: 395, width: 250, height: 15}\n        state: null\n        value: null\n      - component_type: text\n        id: document_title\n        label: HOMEOWNERS - HO3 INSURANCE QUOTE\n        bounds: {x: 600, y: 430, width: 400, height: 25}\n        state: null\n        value: null\n      - component_type: table\n        id: table_protect_your_home\n        bounds: {x: 320, y: 460, width: 1280, height: 250}\n        headers:\n        - PROTECT YOUR HOME\n        - '% OF COVERAGE A'\n        - LIMIT\n        - DEDUCTIBLE\n        - PREMIUM\n        rows:\n        - id: row_coverage_a\n          cells:\n          - component_type: text\n            label: Coverage A - Dwelling\n          - component_type: text\n            label: null\n          - component_type: text\n            label: $261,000\n          - component_type: text\n            label: null\n          - component_type: text\n            label: $17,929.45\n        - id: row_coverage_b\n          cells:\n          - component_type: text\n            label: Coverage B - Other Structures\n          - component_type: text\n            label: '20'\n          - component_type: text\n            label: $52,200\n          - component_type: text\n            label: null\n          - component_type: text\n            label: Included\n        - id: row_coverage_c\n          cells:\n          - component_type: text\n            label: Coverage C - Personal Property\n          - component_type: text\n            label: '70'\n          - component_type: text\n            label: $182,700\n          - component_type: text\n            label: null\n          - component_type: text\n            label: Included\n        - id: row_coverage_d\n          cells:\n          - component_type: text\n            label: Coverage D - Loss of Use\n          - component_type: text\n            label: '20'\n          - component_type: text\n            label: $52,200\n          - component_type: text\n            label: null\n          - component_type: text\n            label: Included\n        - id: row_ordinance\n          cells:\n          - component_type: text\n            label: Ordinance or Law\n          - component_type: text\n            label: '50'\n          - component_type: text\n            label: $130,500\n          - component_type: text\n            label: null\n          - component_type: text\n            label: Included\n        - id: row_fungi\n          cells:\n          - component_type: text\n            label: Limited Fungi, Mold, Wet or Dry Rot, or Bacteria\n          - component_type: text\n            label: null\n          - component_type: text\n            label: $10,000\n          - component_type: text\n            label: null\n          - component_type: text\n            label: Included\n        - id: row_loss_assessment\n          cells:\n          - component_type: text\n            label: Loss Assessment\n          - component_type: text\n            label: null\n          - component_type: text\n            label: $1,000\n          - component_type: text\n            label: null\n          - component_type: text\n            label: Included\n        - id: row_roof_settlement\n          cells:\n          - component_type: text\n            label: Roof Settlement\n          - component_type: text\n            label: null\n          - component_type: text\n            label: Actual Cash Value\n          - component_type: text\n            label: null\n          - component_type: text\n            label: Included\n        - id: row_other_perils\n          cells:\n          - component_type: text\n            label: All Other Perils Deductible\n          - component_type: text\n            label: null\n          - component_type: text\n            label: null\n          - component_type: text\n            label: $2,500\n          - component_type: text\n            label: null\n        - id: row_windstorm\n          cells:\n          - component_type: text\n            label: Windstorm or Hail (Other Than Hurricane) Deductible\n          - component_type: text\n            label: null\n          - component_type: text\n            label: null\n          - component_type: text\n            label: $2,500\n          - component_type: text\n            label: null\n        - id: row_hurricane\n          cells:\n          - component_type: text\n            label: Hurricane Deductible\n          - component_type: text\n            label: '2'\n          - component_type: text\n            label: null\n          - component_type: text\n            label: $5,220\n          - component_type: text\n            label: null\n      - component_type: table\n        id: table_protect_you\n        bounds: {x: 320, y: 720, width: 1280, height: 80}\n        headers:\n        - PROTECT YOU\n        - LIMIT\n        - PREMIUM\n        rows:\n        - id: row_coverage_e\n          cells:\n          - component_type: text\n            label: Coverage E - Personal Liability\n          - component_type: text\n            label: $500,000\n          - component_type: text\n            label: Included\n        - id: row_coverage_f\n          cells:\n          - component_type: text\n            label: Coverage F - Medical Payments to Others\n          - component_type: text\n            label: $5,000\n          - component_type: text\n            label: Included\n      - component_type: table\n        id: table_extra_protection\n        bounds: {x: 320, y: 810, width: 1280, height: 200}\n        headers:\n        - EXTRA PROTECTION\n        - LIMIT\n        - PREMIUM\n        rows:\n        - id: row_diamond_reserve\n          cells:\n          - component_type: text\n            label: Diamond Reserve\n          - component_type: text\n            label: $500,000\n          - component_type: text\n            label: Included\n        - id: row_animal_liability\n          cells:\n          - component_type: text\n            label: Animal Liability\n          - component_type: text\n            label: $10,000\n          - component_type: text\n            label: Included\n        - id: row_home_computer\n          cells:\n          - component_type: text\n            label: Home Computer\n          - component_type: text\n            label: $25,000\n          - component_type: text\n            label: Included\n        - id: row_home_systems\n          cells:\n          - component_type: text\n            label: Home Systems Protection\n          - component_type: text\n            label: $50,000\n          - component_type: text\n            label: Included\n        - id: row_identity_recovery\n          cells:\n          - component_type: text\n            label: Identity Recovery\n          - component_type: text\n            label: $15,000\n          - component_type: text\n            label: Included\n        - id: row_limited_carports\n          cells:\n          - component_type: text\n            label: Limited Carport(s), Pool Cage(s), and Screen Enclosure(s)\n          - component_type: text\n            label: $20,000\n          - component_type: text\n            label: Included\n        - id: row_personal_injury\n          cells:\n          - component_type: text\n            label: Personal Injury\n          - component_type: text\n            label: $500,000\n          - component_type: text\n            label: Included\n        - id: row_personal_property_replacement\n          cells:\n          - component_type: text\n            label: Personal Property Replacement Cost\n          - component_type: text\n            label: Included\n          - component_type: text\n            label: Included\n        - id: row_service_line\n          cells:\n          - component_type: text\n            label: Service Line\n          - component_type: text\n            label: $10,000\n          - component_type: text\n            label: Included\n        - id: row_special_personal_property\n          cells:\n          - component_type: text\n            label: Special Personal Property\n          - component_type: text\n            label: Included\n          - component_type: text\n            label: Included\n        - id: row_water_damage\n          cells:\n          - component_type: text\n            label: Water Damage\n          - component_type: text\n            label: Excluded\n          - component_type: text\n            label: -$459.44\n      - component_type: table\n        id: table_discounts\n        bounds: {x: 320, y: 1020, width: 1280, height: 100}\n        headers:\n        - DISCOUNTS AND SURCHARGES\n        - PREMIUM\n        rows:\n        - id: row_burglar_alarm\n          cells:\n          - component_type: text\n            label: Burglar Alarm\n          - component_type: text\n            label: null\n        - id: row_proof_of_updates\n          cells:\n          - component_type: text\n            label: Proof of Updates - Roof Only\n          - component_type: text\n            label: null\n        - id: row_secured_community\n          cells:\n          - component_type: text\n            label: Secured Community/Building\n          - component_type: text\n            label: null\n        - id: row_windstorm_mitigation\n          cells:\n          - component_type: text\n            label: Windstorm Loss Mitigation\n          - component_type: text\n            label: null\n    - component_type: footer\n      id: pdf_viewer_footer\n      bounds: {x: 800, y: 940, width: 320, height: 40}\n      subcomponents:\n      - component_type: text\n        id: text_page\n        label: Page\n        bounds: {x: 810, y: 950, width: 30, height: 20}\n        state: null\n        value: null\n      - component_type: input\n        id: input_page_number\n        label: null\n        bounds: {x: 850, y: 945, width: 30, height: 30}\n        state: null\n        value: '1'\n      - component_type: text\n        id: text_page_total\n        label: / 3\n        bounds: {x: 885, y: 950, width: 20, height: 20}\n        state: null\n        value: null\n      - component_type: button\n        id: btn_zoom_out\n        label: '-'\n        bounds: {x: 950, y: 945, width: 30, height: 30}\n        state: null\n        value: null\n      - component_type: button\n        id: btn_zoom_in\n        label: +\n        bounds: {x: 1030, y: 945, width: 30, height: 30}\n        state: null\n        value: null\n```"]}, {"role": "model", "parts": ["```yaml\nbrowser_component:\n  tab_title: HH Insurance Propos...\n  url: ai.iscs.com/innovation\n  address_bar_focused: false\n  tab_state: idle\nwebpage:\n  header:\n  - component_type: header\n    id: main_header\n    bounds: {x: 0, y: 64, width: 1920, height: 64}\n    subcomponents:\n    - component_type: image\n      id: logo_american_integrity\n      label: AMERICAN INTEGRITY\n      bounds: {x: 24, y: 103, width: 135, height: 21}\n      state: null\n      value: null\n    - component_type: navigation\n      id: main_navigation\n      bounds: {x: 1290, y: 88, width: 580, height: 45}\n      subcomponents:\n      - component_type: link\n        id: nav_home\n        label: Home\n        bounds: {x: 1295, y: 108, width: 40, height: 20}\n        state: null\n        value: null\n      - component_type: link\n        id: nav_quote_policy\n        label: Quote/Policy\n        bounds: {x: 1365, y: 108, width: 85, height: 20}\n        state: active\n        value: null\n      - component_type: link\n        id: nav_claims\n        label: Claims\n        bounds: {x: 1480, y: 108, width: 50, height: 20}\n        state: null\n        value: null\n      - component_type: link\n        id: nav_cabinets\n        label: Cabinets\n        bounds: {x: 1560, y: 108, width: 65, height: 20}\n        state: null\n        value: null\n      - component_type: link\n        id: nav_support\n        label: Support\n        bounds: {x: 1655, y: 108, width: 55, height: 20}\n        state: null\n        value: null\n  sidebar:\n  - component_type: sidebar\n    id: left_sidebar\n    bounds: {x: 0, y: 128, width: 250, height: 850}\n    subcomponents:\n    - component_type: input\n      id: input_search\n      label: Search\n      bounds: {x: 15, y: 155, width: 185, height: 30}\n      state: null\n      value: ''\n    - component_type: button\n      id: btn_search\n      label: null\n      bounds: {x: 200, y: 155, width: 30, height: 30}\n      state: null\n      value: null\n    - component_type: text\n      id: text_advanced_search\n      label: 'ADVANCED SEARCH:'\n      bounds: {x: 15, y: 195, width: 110, height: 15}\n      state: null\n      value: null\n    - component_type: link\n      id: link_policy\n      label: POLICY\n      bounds: {x: 130, y: 195, width: 45, height: 15}\n      state: null\n      value: null\n    - component_type: link\n      id: link_claims\n      label: CLAIMS\n      bounds: {x: 180, y: 195, width: 45, height: 15}\n      state: null\n      value: null\n    - component_type: navigation\n      id: sidebar_nav\n      bounds: {x: 0, y: 220, width: 250, height: 400}\n      subcomponents:\n      - component_type: link\n        id: nav_quote\n        label: Quote\n        bounds: {x: 0, y: 225, width: 250, height: 40}\n        state: null\n        value: null\n      - component_type: link\n        id: nav_policy\n        label: Policy\n        bounds: {x: 15, y: 265, width: 235, height: 40}\n        state: selected\n        value: null\n      - component_type: link\n        id: nav_dwelling\n        label: Dwelling\n        bounds: {x: 15, y: 305, width: 235, height: 40}\n        state: null\n        value: null\n        subcomponents:\n        - component_type: badge\n          id: badge_dwelling_count\n          label: '2'\n          bounds: {x: 210, y: 315, width: 20, height: 20}\n          state: null\n          value: null\n      - component_type: link\n        id: nav_review\n        label: Review\n        bounds: {x: 15, y: 345, width: 235, height: 40}\n        state: null\n        value: null\n      - component_type: link\n        id: nav_attachments\n        label: Attachments\n        bounds: {x: 0, y: 385, width: 250, height: 40}\n        state: null\n        value: null\n      - component_type: link\n        id: nav_correspondence\n        label: Correspondence\n        bounds: {x: 0, y: 425, width: 250, height: 40}\n        state: null\n        value: null\n      - component_type: link\n        id: nav_tasks\n        label: Tasks\n        bounds: {x: 0, y: 465, width: 250, height: 40}\n        state: null\n        value: null\n      - component_type: link\n        id: nav_notes\n        label: Notes\n        bounds: {x: 0, y: 505, width: 250, height: 40}\n        state: null\n        value: null\n      - component_type: link\n        id: nav_policy_file\n        label: Policy File\n        bounds: {x: 0, y: 545, width: 250, height: 40}\n        state: null\n        value: null\n  - component_type: sidebar\n    id: right_sidebar\n    bounds: {x: 1875, y: 128, width: 45, height: 850}\n    subcomponents:\n    - component_type: button\n      id: btn_summary\n      label: SUMMARY\n      bounds: {x: 1880, y: 145, width: 35, height: 55}\n      state: null\n      value: null\n    - component_type: button\n      id: btn_quick_quote\n      label: WTRCRAFT QUICK QT\n      bounds: {x: 1880, y: 210, width: 35, height: 55}\n      state: null\n      value: null\n    - component_type: button\n      id: btn_new_quote\n      label: NEW QUOTE\n      bounds: {x: 1880, y: 275, width: 35, height: 55}\n      state: null\n      value: null\n    - component_type: button\n      id: btn_new_note\n      label: NEW NOTE\n      bounds: {x: 1880, y: 340, width: 35, height: 55}\n      state: null\n      value: null\n    - component_type: button\n      id: btn_new_attach\n      label: NEW ATTACH...\n      bounds: {x: 1880, y: 405, width: 35, height: 55}\n      state: null\n      value: null\n    - component_type: button\n      id: btn_new_task\n      label: NEW TASK\n      bounds: {x: 1880, y: 470, width: 35, height: 55}\n      state: null\n      value: null\n  main_content:\n  - component_type: container\n    id: main_content_area\n    bounds: {x: 250, y: 128, width: 1625, height: 850}\n    subcomponents:\n    - component_type: container\n      id: quote_summary_bar\n      bounds: {x: 250, y: 128, width: 1625, height: 50}\n      subcomponents:\n      - component_type: button\n        id: btn_quote\n        label: QUOTE\n        bounds: {x: 270, y: 140, width: 70, height: 30}\n        state: active\n        value: null\n      - component_type: text\n        id: text_quote_number\n        label: 'Quote Number: QT-15441432'\n        bounds: {x: 350, y: 145, width: 180, height: 20}\n        state: null\n        value: null\n      - component_type: text\n        id: text_insured_label\n        label: Insured\n        bounds: {x: 540, y: 135, width: 50, height: 15}\n        state: null\n        value: null\n      - component_type: link\n        id: link_insured_name\n        label: Landon Cassidy\n        bounds: {x: 540, y: 150, width: 100, height: 20}\n        state: null\n        value: null\n      - component_type: text\n        id: text_product_label\n        label: Product\n        bounds: {x: 650, y: 135, width: 50, height: 15}\n        state: null\n        value: null\n      - component_type: text\n        id: text_product_value\n        label: Voluntary Homeowners (HO3)\n        bounds: {x: 650, y: 150, width: 180, height: 20}\n        state: null\n        value: null\n      - component_type: text\n        id: text_subtype_label\n        label: Sub Type\n        bounds: {x: 840, y: 135, width: 60, height: 15}\n        state: null\n        value: null\n      - component_type: text\n        id: text_subtype_value\n        label: HO3\n        bounds: {x: 840, y: 150, width: 30, height: 20}\n        state: null\n        value: null\n      - component_type: text\n        id: text_policyterm_label\n        label: Policy Term\n        bounds: {x: 910, y: 135, width: 70, height: 15}\n        state: null\n        value: null\n      - component_type: text\n        id: text_policyterm_value\n        label: 06/20/2025 - 06/20/2026\n        bounds: {x: 910, y: 150, width: 160, height: 20}\n        state: null\n        value: null\n      - component_type: text\n        id: text_producer_label\n        label: Producer\n        bounds: {x: 1080, y: 135, width: 60, height: 15}\n        state: null\n        value: null\n      - component_type: link\n        id: link_producer_name\n        label: HH Insurance Group, LLC\n        bounds: {x: 1080, y: 150, width: 150, height: 20}\n        state: null\n        value: null\n      - component_type: text\n        id: text_status_label\n        label: Status\n        bounds: {x: 1240, y: 135, width: 40, height: 15}\n        state: null\n        value: null\n      - component_type: text\n        id: text_status_value\n        label: In Process\n        bounds: {x: 1240, y: 150, width: 70, height: 20}\n        state: null\n        value: null\n      - component_type: text\n        id: text_premium_label\n        label: Premium + Fees\n        bounds: {x: 1320, y: 135, width: 100, height: 15}\n        state: null\n        value: null\n      - component_type: text\n        id: text_premium_value\n        label: $17,776.90\n        bounds: {x: 1320, y: 150, width: 80, height: 20}\n        state: null\n        value: null\n    - component_type: container\n      id: action_bar\n      bounds: {x: 250, y: 178, width: 1625, height: 40}\n      subcomponents:\n      - component_type: button\n        id: btn_next_page\n        label: NEXT PAGE\n        bounds: {x: 1180, y: 185, width: 90, height: 30}\n        state: null\n        value: null\n      - component_type: button\n        id: btn_save\n        label: SAVE\n        bounds: {x: 1280, y: 185, width: 60, height: 30}\n        state: null\n        value: null\n      - component_type: button\n        id: btn_copy\n        label: COPY\n        bounds: {x: 1350, y: 185, width: 60, height: 30}\n        state: null\n        value: null\n      - component_type: button\n        id: btn_print\n        label: PRINT\n        bounds: {x: 1420, y: 185, width: 60, height: 30}\n        state: null\n        value: null\n      - component_type: button\n        id: btn_create_application\n        label: CREATE APPLICATION\n        bounds: {x: 1490, y: 185, width: 150, height: 30}\n        state: hovered\n        value: null\n      - component_type: button\n        id: btn_discard_changes\n        label: DISCARD CHANGES\n        bounds: {x: 1650, y: 185, width: 130, height: 30}\n        state: null\n        value: null\n      - component_type: button\n        id: btn_view_notes\n        label: VIEW NOTES\n        bounds: {x: 1790, y: 185, width: 100, height: 30}\n        state: null\n        value: null\n      - component_type: button\n        id: btn_delete\n        label: DELETE\n        bounds: {x: 1900, y: 185, width: 70, height: 30}\n        state: null\n        value: null\n      - component_type: button\n        id: btn_more\n        label: '... MORE'\n        bounds: {x: 1980, y: 185, width: 70, height: 30}\n        state: null\n        value: null\n    - component_type: link\n      id: link_return_home\n      label: < Return to Home\n      bounds: {x: 270, y: 225, width: 110, height: 20}\n      state: null\n      value: null\n    - component_type: form\n      id: policy_form\n      bounds: {x: 270, y: 250, width: 1580, height: 700}\n      subcomponents:\n      - component_type: text\n        id: title_policy_general\n        label: Policy General\n        bounds: {x: 270, y: 250, width: 100, height: 20}\n        state: null\n        value: null\n      - component_type: dropdown\n        id: dropdown_product\n        label: Product*\n        bounds: {x: 270, y: 280, width: 400, height: 30}\n        state: null\n        value: Florida - Voluntary Homeowners (HO3) - American Integrity Insurance Group\n      - component_type: input\n        id: input_effective_date\n        label: Effective Date*\n        bounds: {x: 270, y: 320, width: 150, height: 30}\n        state: null\n        value: 06/20/2025\n      - component_type: input\n        id: input_producer_code\n        label: 'Producer: Code*'\n        bounds: {x: 270, y: 360, width: 150, height: 30}\n        state: null\n        value: AG8529A1\n      - component_type: text\n        id: text_producer_name\n        label: HH Insurance Group, LLC\n        bounds: {x: 450, y: 365, width: 150, height: 20}\n        state: null\n        value: null\n      - component_type: text\n        id: title_prior_carrier\n        label: Prior Carrier Details\n        bounds: {x: 270, y: 410, width: 150, height: 20}\n        state: null\n        value: null\n      - component_type: dropdown\n        id: dropdown_prior_carrier\n        label: Prior Carrier*\n        bounds: {x: 270, y: 440, width: 200, height: 30}\n        state: null\n        value: New Purchase\n      - component_type: input\n        id: input_prior_policy_exp\n        label: Prior Policy Expiration Date\n        bounds: {x: 650, y: 440, width: 200, height: 30}\n        state: null\n        value: ''\n      - component_type: text\n        id: title_insured_info\n        label: Insured Information\n        bounds: {x: 270, y: 490, width: 150, height: 20}\n        state: null\n        value: null\n      - component_type: dropdown\n        id: dropdown_entity_type\n        label: Entity Type*\n        bounds: {x: 270, y: 520, width: 200, height: 30}\n        state: null\n        value: Individual\n      - component_type: text\n        id: text_entity_type_value\n        label: Individual\n        bounds: {x: 270, y: 555, width: 60, height: 20}\n        state: null\n        value: null\n      - component_type: input\n        id: input_first_name\n        label: First*\n        bounds: {x: 500, y: 520, width: 150, height: 30}\n        state: null\n        value: Landon\n      - component_type: input\n        id: input_middle_name\n        label: Middle\n        bounds: {x: 660, y: 520, width: 150, height: 30}\n        state: null\n        value: ''\n      - component_type: input\n        id: input_last_name\n        label: Last*\n        bounds: {x: 820, y: 520, width: 150, height: 30}\n        state: null\n        value: Cassidy\n      - component_type: input\n        id: input_suffix\n        label: Suffix\n        bounds: {x: 980, y: 520, width: 100, height: 30}\n        state: null\n        value: ''\n      - component_type: input\n        id: input_dob\n        label: DOB*\n        bounds: {x: 270, y: 590, width: 150, height: 30}\n        state: null\n        value: 05/20/1998\n      - component_type: dropdown\n        id: dropdown_insurance_score\n        label: Insurance Score*\n        bounds: {x: 500, y: 590, width: 200, height: 30}\n        state: null\n        value: Excellent (850-999)\n      - component_type: input\n        id: input_search_name\n        label: Search Name*\n        bounds: {x: 270, y: 630, width: 200, height: 30}\n        state: null\n        value: Landon Cassidy\n      - component_type: link\n        id: link_reset\n        label: Reset\n        bounds: {x: 480, y: 635, width: 40, height: 20}\n        state: null\n        value: null\n      - component_type: dropdown\n        id: dropdown_primary_phone\n        label: Primary Phone\n        bounds: {x: 270, y: 670, width: 200, height: 30}\n        state: null\n        value: Select...\n      - component_type: input\n        id: input_email\n        label: Email\n        bounds: {x: 500, y: 670, width: 200, height: 30}\n        state: null\n        value: ''\n      - component_type: checkbox\n        id: checkbox_no_email\n        label: No Email\n        bounds: {x: 710, y: 675, width: 80, height: 20}\n        state: null\n        value: null\n      - component_type: text\n        id: title_dwelling_info\n        label: Dwelling Information\n        bounds: {x: 270, y: 720, width: 150, height: 20}\n        state: null\n        value: null\n      - component_type: text\n        id: text_lookup_address\n        label: Lookup Address\n        bounds: {x: 270, y: 750, width: 100, height: 20}\n        state: null\n        value: null\n      - component_type: input\n        id: input_number\n        label: Number*\n        bounds: {x: 270, y: 780, width: 80, height: 30}\n        state: null\n        value: '4227'\n      - component_type: dropdown\n        id: dropdown_direction\n        label: Direction\n        bounds: {x: 360, y: 780, width: 80, height: 30}\n        state: null\n        value: S\n      - component_type: input\n        id: input_street\n        label: Street*\n        bounds: {x: 450, y: 780, width: 100, height: 30}\n        state: null\n        value: 5th\n      - component_type: dropdown\n        id: dropdown_suffix\n        label: Suffix\n        bounds: {x: 560, y: 780, width: 80, height: 30}\n        state: null\n        value: Ave\n      - component_type: input\n        id: input_post_dir\n        label: Post Dir\n        bounds: {x: 650, y: 780, width: 80, height: 30}\n        state: null\n        value: ''\n      - component_type: dropdown\n        id: dropdown_type\n        label: Type\n        bounds: {x: 740, y: 780, width: 80, height: 30}\n        state: null\n        value: ''\n      - component_type: input\n        id: input_number_2\n        label: Number\n        bounds: {x: 830, y: 780, width: 80, height: 30}\n        state: null\n        value: ''\n      - component_type: text\n        id: text_address_verified\n        label: Address Verified\n        bounds: {x: 920, y: 785, width: 100, height: 20}\n        state: null\n        value: null\n      - component_type: link\n        id: link_view_map\n        label: View Map\n        bounds: {x: 1030, y: 785, width: 60, height: 20}\n        state: null\n        value: null\n      - component_type: input\n        id: input_city\n        label: City*\n        bounds: {x: 270, y: 820, width: 150, height: 30}\n        state: null\n        value: St Petersburg\n      - component_type: dropdown\n        id: dropdown_county\n        label: County*\n        bounds: {x: 430, y: 820, width: 150, height: 30}\n        state: null\n        value: Pinellas\n      - component_type: dropdown\n        id: dropdown_state\n        label: State*\n        bounds: {x: 590, y: 820, width: 150, height: 30}\n        state: null\n        value: Florida\n      - component_type: input\n        id: input_zip\n        label: Zip*\n        bounds: {x: 750, y: 820, width: 100, height: 30}\n        state: null\n        value: 33711-1522\n      - component_type: input\n        id: input_latitude\n        label: Latitude*\n        bounds: {x: 270, y: 860, width: 150, height: 30}\n        state: null\n        value: '27.766685'\n      - component_type: input\n        id: input_longitude\n        label: Longitude*\n        bounds: {x: 430, y: 860, width: 150, height: 30}\n        state: null\n        value: \"-82.690887\"\n      - component_type: dropdown\n        id: dropdown_construction_type\n        label: Construction Type*\n        bounds: {x: 270, y: 900, width: 150, height: 30}\n        state: null\n        value: Masonry\n      - component_type: dropdown\n        id: dropdown_occupancy\n        label: Occupancy*\n        bounds: {x: 430, y: 900, width: 150, height: 30}\n        state: null\n        value: Owner Occupied\n      - component_type: dropdown\n        id: dropdown_months_occupied\n        label: Months Occupied*\n        bounds: {x: 590, y: 900, width: 150, height: 30}\n        state: null\n        value: 0 to 12 Months\n      - component_type: checkbox\n        id: checkbox_ignore_address_validation\n        label: Ignore Address Validation\n        bounds: {x: 920, y: 750, width: 180, height: 20}\n        state: null\n        value: null\n      - component_type: text\n        id: text_resided_question\n        label: Has the Insured resided at the risk address for less than 2 years?*\n        bounds: {x: 270, y: 940, width: 400, height: 20}\n        state: null\n        value: null\n      - component_type: dropdown\n        id: dropdown_resided_answer\n        label: null\n        bounds: {x: 680, y: 935, width: 80, height: 30}\n        state: null\n        value: 'Yes'\n      - component_type: text\n        id: text_prior_address\n        label: Prior Address\n        bounds: {x: 270, y: 970, width: 100, height: 20}\n        state: null\n        value: null\n      - component_type: input\n        id: input_prior_address_number\n        label: Number*\n        bounds: {x: 270, y: 1000, width: 80, height: 30}\n        state: null\n        value: '18001'\n      - component_type: input\n        id: input_prior_address_direction\n        label: Direction\n        bounds: {x: 360, y: 1000, width: 80, height: 30}\n        state: null\n        value: ''\n      - component_type: input\n        id: input_prior_address_street\n        label: Street*\n        bounds: {x: 450, y: 1000, width: 100, height: 30}\n        state: null\n        value: Avalon\n      - component_type: dropdown\n        id: dropdown_prior_address_suffix\n        label: Suffix\n        bounds: {x: 560, y: 1000, width: 80, height: 30}\n        state: null\n        value: Ln\n      - component_type: input\n        id: input_prior_address_post_dir\n        label: Post Dir\n        bounds: {x: 650, y: 1000, width: 80, height: 30}\n        state: null\n        value: ''\n      - component_type: dropdown\n        id: dropdown_prior_address_type\n        label: Type\n        bounds: {x: 740, y: 1000, width: 80, height: 30}\n        state: null\n        value: ''\n      - component_type: input\n        id: input_prior_address_number_2\n        label: Number\n        bounds: {x: 830, y: 1000, width: 80, height: 30}\n        state: null\n        value: ''\n      - component_type: link\n        id: link_verify_address\n        label: Verify\n        bounds: {x: 920, y: 1005, width: 40, height: 20}\n        state: null\n        value: null\n      - component_type: text\n        id: text_address_verified_2\n        label: Address\n        bounds: {x: 970, y: 1005, width: 60, height: 20}\n        state: null\n        value: null\n```"]}]