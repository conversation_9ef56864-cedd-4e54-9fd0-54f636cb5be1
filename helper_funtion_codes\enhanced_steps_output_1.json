{"enhanced_steps": [{"step_number": 1, "timestamp": "000", "instruction": "Quick video here for how to locate an American Integrity quote.", "source": "Both", "url": "https://ai.iscs.com/innovation", "action_details": {"target_element": "webpage", "input_value": null}}, {"step_number": 2, "timestamp": "008", "instruction": "So I'm at the American Integrity homepage.", "source": "Both", "url": "https://drive.google.com/drive/folders/1v773cXo-vfGpoJOYt9CYY5YGcpilE5TL", "action_details": {"target_element": "browser_tab", "input_value": null}}, {"step_number": 3, "timestamp": "010", "instruction": "And let's say I wanted to find this quote here.", "source": "Both", "url": "https://drive.google.com/drive/folders/1v773cXo-vfGpoJOYt9CYY5YGcpilE5TL", "action_details": {"target_element": "browser_tab", "input_value": null}}, {"step_number": 4, "timestamp": "010", "instruction": "Switched to tab: Cassidy HO3 AI.pdf", "source": "UI Action", "url": "https://drive.google.com/drive/folders/1v773cXo-vfGpoJOYt9CYY5YGcpilE5TL", "action_details": {"target_element": "browser_tab", "input_value": null}}, {"step_number": 5, "timestamp": "010", "instruction": "Switched to tab: Guidewire InsuranceNow™", "source": "UI Action", "url": "https://ai.iscs.com/innovation", "action_details": {"target_element": "browser_tab", "input_value": null}}, {"step_number": 6, "timestamp": "014", "instruction": "I would highlight this quote number and then I'm going to copy that.", "source": "Audio Narration", "url": "https://ai.iscs.com/innovation", "action_details": null}, {"step_number": 7, "timestamp": "019", "instruction": "I'm going to paste it in this search bar up here.", "source": "Audio Narration", "url": "https://ai.iscs.com/innovation", "action_details": null}, {"step_number": 8, "timestamp": "025", "instruction": "And then when I hit search it will take me, if I'm using that quote number, it will take me right into the quote.", "source": "Both", "url": "https://ai.iscs.com/innovation", "action_details": {"target_element": "browser_tab", "input_value": null}}, {"step_number": 9, "timestamp": "029", "instruction": "So this is where I was editing and making changes.", "source": "Both", "url": "https://ai.iscs.com/innovation", "action_details": {"target_element": "input_dob", "input_value": "05/20/1988"}}, {"step_number": 10, "timestamp": "036", "instruction": "This is the first page of a few. We've got policy, dwelling, and then review.", "source": "Both", "url": "https://ai.iscs.com/innovation", "action_details": {"target_element": "nav_dwelling", "input_value": null}}, {"step_number": 11, "timestamp": "040", "instruction": "And so we can move through our quote here.", "source": "Both", "url": "https://ai.iscs.com/innovation", "action_details": {"target_element": "radio_button", "input_value": "LANDON CASSIDY"}}, {"step_number": 12, "timestamp": "043", "instruction": "User entered address information: City: St Petersburg, County: Pinellas, State: Florida, Zip: 33711-1522", "source": "UI Action", "url": "https://ai.iscs.com/innovation", "action_details": {"target_element": "input_city, dropdown_county, dropdown_state, input_zip", "input_value": "St Petersburg, Pinellas, Florida, 33711-1522"}}, {"step_number": 13, "timestamp": "047", "instruction": "And then when the quote is filled out, you don't even have to be on the last page. You can just hit the create application button up here to turn it into an application.", "source": "Audio Narration", "url": "https://ai.iscs.com/innovation", "action_details": null}]}