import os
import yaml
import json
import google.generativeai as genai
from pathlib import Path
from datetime import datetime
import time

RESULTS_FILE = "yaml_analysis_results.json"
HISTORY_FILE = "chat_history_context.json"
METADATA_FILE = "metadata_context.json"

class ContextGenerator:
    def __init__(self, api_key="AIzaSyDhnI4SD3TdF-rUCq8UxtbNDOiDA-Np7Pw"):
        """Initialize the Context Generator with API key"""
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel('gemini-2.5-flash')  # Adjusted to valid model name; original '2.5' may be a typo
        
        # Enhanced prompt template to incorporate previous context, handle no changes explicitly, and emphasize history tracking
        self.PROMPT_TEMPLATE = """
You are an expert UI analyst.

Your job is to read YAML structures describing either:
1. Changes in a user interface (UI) or browser state (with `old_value` and `new_value`), or
2. A static snapshot of the current UI and user input (without change tracking).

CRITICAL PRIORITY RULES - CHECK THESE FIRST:

### Browser Navigation Detection (HIGHEST PRIORITY):
1. **Tab Switching**: If `tab_title` changes between old_value and new_value  
   - Output: "User switched from tab '[old_title]' to tab '[new_title]'"  
   - Include both tab names and URLs if available  

2. **New Tab Creation**: If `total_tabs` increases  
   - Output: "User opened a new tab"`+ destination if URL changes  

3. **Tab Closure**: If `total_tabs` decreases  
   - Output: "User closed a tab"  

4. **URL Navigation**: If `url` changes while `tab_title` stays same  
   - Output: "User navigated from [old_url] to [new_url]"  

5. **Address Bar Interaction**: If `address_bar_focused` changes or `address_bar_content` changes  
   - Output: "User clicked address bar" or "User typed '[content]' in address bar"  

---

### Standard UI Change Detection (SECOND PRIORITY):  
When YAML contains changes (`old_value` / `new_value`):  
1. Interpret and describe each change in clear, human-readable sentences  
2. Group related changes together (e.g., changes to the same UI element)  
3. Use descriptive phrases like:  
   - "The label was changed from X to Y"  
   - "The size of the element was adjusted"  
   - "The mouse cursor moved from position (x, y) to (x, y)"  
4. Mention specific UI elements by type and label if available  
5. Be concise but clear  

---

### Static Snapshot Detection (THIRD PRIORITY):  
When YAML contains no changes (single snapshot):  
1. Interpret what the user is doing or seeing at that point  
2. Summarize the page or interface shown  
3. Include:  
   - The page title and URL (if available)  
   - The main UI elements (e.g., forms, buttons, checkboxes)  
   - Any values entered into fields  
   - Visible labels, headings, or text  

---

**ALWAYS check for browser navigation changes FIRST before analyzing webpage content changes.**

Now analyze the following YAML and produce the summary:

Now analyze the following YAML and produce the summary, using all available context from previous messages in the chat history:

```yaml
{yaml_content}
```
"""


    def is_yaml_empty_or_insignificant(self, yaml_content):
        """
        Check if YAML content is empty, contains only empty objects, or has no meaningful data
        Returns True if the YAML should be skipped
        """
        try:
            # Check if content is empty or just whitespace
            if not yaml_content or not yaml_content.strip():
                return True
            
            # Parse YAML content
            parsed_yaml = yaml.safe_load(yaml_content)
            
            # Check if parsing resulted in None or empty
            if parsed_yaml is None:
                return True
            
            # Check if it's an empty dict, list, or string
            if parsed_yaml == {} or parsed_yaml == [] or parsed_yaml == "":
                return True
            
            # Check if it's a dict with only empty values
            if isinstance(parsed_yaml, dict):
                # Remove None, empty strings, empty dicts, empty lists
                non_empty_values = []
                for value in parsed_yaml.values():
                    if value is not None and value != "" and value != {} and value != []:
                        # For nested dicts, check if they have meaningful content
                        if isinstance(value, dict):
                            if any(v is not None and v != "" and v != {} and v != [] for v in value.values()):
                                non_empty_values.append(value)
                        else:
                            non_empty_values.append(value)
                
                # If no non-empty values found, consider it empty
                if not non_empty_values:
                    return True
            
            # Check if it's a list with only empty elements
            if isinstance(parsed_yaml, list):
                non_empty_items = [item for item in parsed_yaml if item is not None and item != "" and item != {} and item != []]
                if not non_empty_items:
                    return True
            
            return False
            
        except yaml.YAMLError as e:
            # If YAML parsing fails, log the error but don't skip (let LLM handle it)
            print(f"Warning: YAML parsing error (will still process): {str(e)}")
            return False
        except Exception as e:
            print(f"Warning: Error checking YAML content (will still process): {str(e)}")
            return False

    def save_results_to_json(self, results_array, output_file=RESULTS_FILE):
        """Save results array to JSON file"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results_array, f, indent=2, ensure_ascii=False)
            print(f"\nResults saved to: {output_file}")
            return output_file
        except Exception as e:
            print(f"Error saving to JSON: {str(e)}")
            return None

    def load_results(self):
        """Load previous results from JSON file"""
        if os.path.exists(RESULTS_FILE):
            with open(RESULTS_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        return []

    def save_history(self, history):
        """Saves the chat history to a JSON file."""
        serializable_history = [
            {"role": message["role"], "parts": [part["text"] for part in message["parts"]]}
            for message in history
        ]
        with open(HISTORY_FILE, 'w', encoding='utf-8') as f:
            json.dump(serializable_history, f, indent=2)
        print("Chat history saved.")

    def load_history(self):
        """Loads the last 9 chat history objects from a JSON file."""
        if os.path.exists(HISTORY_FILE):
            with open(HISTORY_FILE, 'r', encoding='utf-8') as f:
                all_history = json.load(f)
            # Get only the last 9 records
            recent_history = all_history[-9:] if len(all_history) > 9 else all_history
            # Reconstruct the parts as list of dicts
            history = [
                {"role": item["role"], "parts": [{"text": part} for part in item["parts"]]}
                for item in recent_history
            ]
            print(f"Chat history loaded: {len(history)} objects.")
            return history
        return []

    def save_metadata(self, metadata):
        """Saves the metadata to a JSON file."""
        with open(METADATA_FILE, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2)

    def load_metadata(self):
        """Loads the metadata from a JSON file."""
        if os.path.exists(METADATA_FILE):
            with open(METADATA_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}

    def get_analysis_strings(self, results_array):
        """Extract just the AI analysis strings from results"""
        return [result['ai_analysis'] for result in results_array]

    def get_file_names(self, results_array):
        """Extract just the file names from results"""
        return [result['file_details']['file_name'] for result in results_array]

    def get_analysis_by_filename(self, results_array):
        """Get dictionary mapping filenames to their analysis"""
        return {result['file_details']['file_name']: result['ai_analysis'] for result in results_array}

    def process_yaml_files(self, folder_path):
        """Process all YAML files in the given folder using a chat session for context"""
        results_array = self.load_results()
        skipped_files = []

        # Get all YAML files from the folder, sorted by name for sequential processing
        yaml_files = sorted(list(Path(folder_path).glob("*.yaml")) + list(Path(folder_path).glob("*.yml")))

        # Load metadata
        metadata = self.load_metadata()

        # Initialize chat history
        history = self.load_history()

        # Start chat session with existing history
        chat = self.model.start_chat(history=history)

        for yaml_file in yaml_files:
            if yaml_file.name in metadata:
                print(f"Skipped (already processed): {yaml_file.name}")
                continue

            try:
                # Read YAML file with fallback encoding
                try:
                    with open(yaml_file, 'r', encoding='utf-8') as file:
                        yaml_content = file.read()
                except UnicodeDecodeError:
                    print(f"UTF-8 decoding failed for {yaml_file.name}, trying cp1252...")
                    with open(yaml_file, 'r', encoding='cp1252') as file:
                        yaml_content = file.read()

                # Check if YAML content is empty or insignificant
                if self.is_yaml_empty_or_insignificant(yaml_content):
                    print(f"Skipped (empty/insignificant): {yaml_file.name}")
                    skipped_files.append(yaml_file.name)
                    continue

                # Create prompt with YAML content
                prompt = self.PROMPT_TEMPLATE.format(yaml_content=yaml_content)

                # Retry logic for sending message
                retries = 0
                max_retries = 4
                while retries < max_retries:
                    try:
                        response = chat.send_message(prompt)
                        break
                    except Exception as e:
                        retries += 1
                        print(f"Attempt {retries}/{max_retries} failed for {yaml_file.name}. Error: {str(e)}")
                        if retries < max_retries:
                            wait_time = 15 * retries
                            print(f"Retrying in {wait_time} seconds...")
                            time.sleep(wait_time)
                        else:
                            raise Exception(f"Failed to process {yaml_file.name} after {max_retries} attempts.")

                # Append user input and model output to history
                history.append({"role": "user", "parts": [{"text": prompt}]})

                # raw_text = response.text.strip()
                # print("raw_text",raw_text)
                # # Remove Markdown fences if present
                # if raw_text.startswith("```"):
                #     raw_text = raw_text.split("```")[1]  # get the middle part
                #     if raw_text.strip().startswith("json"):
                #         raw_text = raw_text.strip()[4:]  # remove 'json' after ```
                #     raw_text = raw_text.strip()
                # # Validate that the response is valid JSON before writing
                # json_data = json.loads(raw_text)
        
                history.append({"role": "model", "parts": [{"text": response.text.strip()}]})


                # Extract time_in_seconds from filename (characters 14-16)
                filename = yaml_file.name
                time_in_seconds = filename[13:16] if len(filename) > 16 else "000"

                # Create result object
                result_obj = {
                    "file_details": {
                        "file_name": filename,
                        "file_path": str(yaml_file),
                        "time_in_seconds": time_in_seconds
                        # "yaml_content": yaml_content
                    },
                    "ai_analysis": response.text.strip()
                }

                results_array.append(result_obj)
                metadata[yaml_file.name] = yaml_file.name
                self.save_results_to_json(results_array)
                self.save_history(history)
                self.save_metadata(metadata)
                print(f"Processed: {yaml_file.name}")

            except Exception as e:
                print(f"Error processing {yaml_file}: {str(e)}")
                raise  # Raise to stop processing on error

        # Print summary of processing
        if skipped_files:
            print(f"\nSummary: Processed {len(results_array)} files, skipped {len(skipped_files)} empty/insignificant files:")
            for skipped in skipped_files:
                print(f"  - {skipped}")

        return results_array

    async def main(self, folder_path, isIntent=None):
        """
        Main processing function that processes YAML files and triggers intent analysis
        Returns the final JSON result from intent generator
        """
        # print(f"Processing folder: {folder_path}")

        # Verify folder exists
        if not os.path.exists(folder_path):
            print(f"Error: Folder does not exist: {folder_path}")
            return None

        # Process all YAML files
        results = self.process_yaml_files(folder_path)

        # Save results to JSON file
        json_file = self.save_results_to_json(results)
        # json_file = r"E:\loveable_AI\bunch\yaml_analysis_results_20250810_224933.json"

        # Trigger intent generator and capture result
        if json_file:
            print(f"\n{'='*50}")
            print("AUTOMATICALLY TRIGGERING INTENT ANALYSIS...")
            print(f"{'='*50}")

            try:
                # Import and use intent generator as a class
                # from intent_generator import IntentGenerator

                # intent_gen = IntentGenerator()
                # intent_result = intent_gen.analyze_video_frames_sync(json_file , isIntent)

                from parameterizationIntent import main

                intent_result = await main(json_file , isIntent)
                print("intent_result", intent_result)
                if intent_result:
                    print("FINAL RESULT FROM INTENT GENERATOR:")
                    print(json.dumps(intent_result, indent=2))
                    return intent_result
                else:
                    print("Intent generator returned no result")
                    return {"status": False, "error_stage": "intent_generator", "error_message": "No result returned by intent generator"}

            except Exception as e:
                print(f"Error running intent_generator: {e}")
                import traceback
                traceback.print_exc()
                return {"status": False, "error_stage": "intent_generator", "error_message": str(e)}

        return {"status": False, "error_stage": "context_generator", "error_message": "No JSON file produced"}

# Backward compatibility for standalone usage

# Main execution
import asyncio

if __name__ == "__main__":
    async def run_main():
        context_gen = ContextGenerator()
        result = await context_gen.main(r"E:\loveable_AI\bunch\ui_element_extraction_20250829_134324\diff_folder", "active")
        if result:
            print("\nProcess completed successfully!")
        else:
            print("\nProcess failed!")

    asyncio.run(run_main())