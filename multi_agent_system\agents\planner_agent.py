"""Planner Agent - Orchestrates the workflow between other agents."""

import asyncio
import logging
from typing import Dict, Any, Optional
from a2a.types import Agent<PERSON>kill

from .base_agent import BaseAgent, TaskRequest, TaskResponse
from ..config import AGENT_URLS


class PlannerAgent(BaseAgent):
    """
    Planner Agent coordinates the entire workflow:
    1. Receives task from user
    2. Orchestrates Salesforce Agent to get data
    3. Orchestrates Document Agent to get quote number
    4. Orchestrates American Integrity Agent to fill forms
    """
    
    def __init__(self):
        skills = [
            AgentSkill(
                id="workflow_orchestration",
                name="Workflow Orchestration",
                description="Orchestrate complex multi-step workflows across multiple agents",
                tags=["orchestration", "workflow", "coordination"]
            ),
            AgentSkill(
                id="task_planning",
                name="Task Planning",
                description="Plan and sequence tasks for optimal execution",
                tags=["planning", "sequencing", "optimization"]
            )
        ]
        
        super().__init__(
            name="PlannerAgent",
            description="Orchestrates workflow between Salesforce, Document, and American Integrity agents",
            port=8001,
            skills=skills,

        )
    
    async def process_task(self, request: TaskRequest) -> TaskResponse:
        """Process the main workflow orchestration task."""
        try:
            task_id = request.message.get("parts", [{}])[0].get("text", "")
            
            if not task_id:
                return TaskResponse(
                    id=request.id,
                    status="error",
                    error="No task ID provided"
                )
            
            self.logger.info(f"Starting workflow orchestration for task: {task_id}")
            
            # Step 1: Get Salesforce data
            self.logger.info("Step 1: Getting Salesforce data")
            salesforce_result = await self._get_salesforce_data(task_id)
            
            if salesforce_result.get("status") != "success":
                return TaskResponse(
                    id=request.id,
                    status="error",
                    error=f"Salesforce step failed: {salesforce_result.get('error')}"
                )
            
            # Step 2: Get quote number from document
            self.logger.info("Step 2: Getting quote number from document")
            document_result = await self._get_quote_number()
            
            if document_result.get("status") != "success":
                return TaskResponse(
                    id=request.id,
                    status="error",
                    error=f"Document step failed: {document_result.get('error')}"
                )
            
            quote_number = document_result.get("data", {}).get("quote_number")
            if not quote_number:
                return TaskResponse(
                    id=request.id,
                    status="error",
                    error="No quote number found in document"
                )
            
            # Step 3: Fill American Integrity form
            self.logger.info("Step 3: Filling American Integrity form")
            ai_result = await self._fill_american_integrity_form(
                quote_number, 
                salesforce_result.get("data", {})
            )
            
            if ai_result.get("status") != "success":
                return TaskResponse(
                    id=request.id,
                    status="error",
                    error=f"American Integrity step failed: {ai_result.get('error')}"
                )
            
            # Success - all steps completed
            return TaskResponse(
                id=request.id,
                status="success",
                data={
                    "task_id": task_id,
                    "quote_number": quote_number,
                    "salesforce_data": salesforce_result.get("data"),
                    "form_completion": ai_result.get("data")
                },
                message=f"Task {task_id} completed successfully"
            )
            
        except Exception as e:
            self.logger.error(f"Error in workflow orchestration: {str(e)}")
            return TaskResponse(
                id=request.id,
                status="error",
                error=str(e)
            )
    
    async def _get_salesforce_data(self, task_id: str) -> Dict[str, Any]:
        """Get data from Salesforce agent."""
        try:
            task_data = {
                "action": "get_household_data",
                "task_id": task_id
            }
            
            result = await self.communicate_with_agent(
                AGENT_URLS["salesforce"],
                task_data
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error getting Salesforce data: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    async def _get_quote_number(self) -> Dict[str, Any]:
        """Get quote number from document agent."""
        try:
            task_data = {
                "action": "extract_quote_number"
            }
            
            result = await self.communicate_with_agent(
                AGENT_URLS["document"],
                task_data
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error getting quote number: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    async def _fill_american_integrity_form(self, quote_number: str, salesforce_data: Dict[str, Any]) -> Dict[str, Any]:
        """Fill American Integrity form with data."""
        try:
            task_data = {
                "action": "fill_underwriting_form",
                "quote_number": quote_number,
                "salesforce_data": salesforce_data
            }
            
            result = await self.communicate_with_agent(
                AGENT_URLS["american_integrity"],
                task_data
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error filling American Integrity form: {str(e)}")
            return {"status": "error", "error": str(e)}


if __name__ == "__main__":
    agent = PlannerAgent()
    asyncio.run(agent.initialize())
    agent.run()
