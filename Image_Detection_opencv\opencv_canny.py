import cv2
import numpy as np
import glob
import os

# ==== Hardcoded values ====
TEMPLATE_PATH = r"E:\loveable_AI\bunch\Image_Detection_opencv\cross-x-mark-greyed.jpg"   # path to template image
IMAGES_PATH = r"E:\loveable_AI\bunch\Image_Detection_opencv\input_images_web\*.png"    # path to folder containing sample images
THRESHOLD = 0.4      # confidence threshold
USE_EDGES = True                 # whether to apply Canny edge detection
OUTPUT_DIR = r"E:\loveable_AI\bunch\Image_Detection_opencv\cropped_images"
CROP_START = 50          # Start cropping after this many pixels from the top (increased from 10)
CROP_END = 970
CROP_LEFT = 1780
CROP_RIGHT = 100
# Create output directory if it doesn't exist

# Create output directory if it doesn't exist
if not os.path.exists(OUTPUT_DIR):
    os.makedirs(OUTPUT_DIR)

# Load template
template = cv2.imread(TEMPLATE_PATH)
if template is None:
    raise FileNotFoundError(f"Template not found at {TEMPLATE_PATH}")

template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)

# Optional: Apply edges
if USE_EDGES:
    template_gray = cv2.Canny(template_gray, 50, 200)

(tH, tW) = template_gray.shape[:2]

# Loop over sample images
for image_path in glob.glob(IMAGES_PATH):
    image = cv2.imread(image_path)
    if image is None:
        print(f"{image_path} -> False (image not readable)")
        continue
    
    # Debug: Print original image shape
    print(f"Original image shape: {image.shape}")
    
    # Crop the image, removing the top pixels
    if image.shape[0] > CROP_START:  # Ensure image height is sufficient
        cropped_image = image[CROP_START:-CROP_END, CROP_RIGHT:-CROP_LEFT]  # Simplified slicing - crop from CROP_START to end
        print(f"Cropped image shape: {cropped_image.shape}")
    else:
        print(f"Image height {image.shape[0]} is less than CROP_START {CROP_START}, using full image")
        cropped_image = image.copy()  # Use copy to avoid reference issues
    
    # Save the cropped image
    filename = os.path.basename(image_path)
    output_path = os.path.join(OUTPUT_DIR, f"cropped_{filename}")
    success = cv2.imwrite(output_path, cropped_image)
    print(f"Saved cropped image to: {output_path}")
    
    # Proceed with template matching on cropped image
    gray = cv2.cvtColor(cropped_image, cv2.COLOR_BGR2GRAY)
    
    if USE_EDGES:
        gray = cv2.Canny(gray, 50, 200)

    found = None
    for scale in np.linspace(0.1, 1.0, 20):
        resized_template = cv2.resize(template_gray, (int(tW * scale), int(tH * scale)))
        rH, rW = resized_template.shape[::-1]  # Fixed: was using [::-1] which reversed the order
        # rH, rW = resized_template.shape  # Fixed: was using [::-1] which reversed the order

        if rH > gray.shape[0] or rW > gray.shape[1]:
            continue

        result = cv2.matchTemplate(gray, resized_template, cv2.TM_CCOEFF_NORMED)
        (_, maxVal, _, maxLoc) = cv2.minMaxLoc(result)

        if found is None or maxVal > found[0]:
            found = (maxVal, maxLoc, scale)

    if found and found[0] >= THRESHOLD:
        print(f"{image_path} -> True (confidence: {found[0]:.3f})")
    else:
        confidence = found[0] if found else 0
        print(f"{image_path} -> False (confidence: {confidence:.3f})")