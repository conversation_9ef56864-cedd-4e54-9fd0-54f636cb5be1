import cv2
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle

def template_matching_demo():
    """
    Comprehensive template matching demonstration using OpenCV and Matplotlib
    """
    
    # Create a sample main image (you can replace this with cv2.imread('your_image.jpg'))
    main_image = create_sample_image()
    
    # Create a template from part of the main image (you can replace with cv2.imread('template.jpg'))
    template = create_sample_template()
    
    # Convert images to grayscale for template matching
    main_gray = cv2.cvtColor(main_image, cv2.COLOR_BGR2GRAY) if len(main_image.shape) == 3 else main_image
    template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY) if len(template.shape) == 3 else template
    
    # Perform template matching using different methods
    methods = [
        ('TM_CCOEFF', cv2.TM_CCOEFF),
        ('TM_CCOEFF_NORMED', cv2.TM_CCOEFF_NORMED),
        ('TM_CCORR', cv2.TM_CCORR),
        ('TM_CCORR_NORMED', cv2.TM_CCORR_NORMED),
        ('TM_SQDIFF', cv2.TM_SQDIFF),
        ('TM_SQDIFF_NORMED', cv2.TM_SQDIFF_NORMED)
    ]
    
    # Create subplot for visualization
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    fig.suptitle('Template Matching Results - Different Methods', fontsize=16)
    
    template_h, template_w = template_gray.shape
    
    for i, (method_name, method) in enumerate(methods):
        row = i // 3
        col = i % 3
        ax = axes[row, col]
        
        # Apply template matching
        result = cv2.matchTemplate(main_gray, template_gray, method)
        
        # Find the best match location
        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
        
        # For SQDIFF and SQDIFF_NORMED, best match is minimum value
        if method in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
            top_left = min_loc
            match_value = min_val
        else:
            top_left = max_loc
            match_value = max_val
        
        bottom_right = (top_left[0] + template_w, top_left[1] + template_h)
        
        # Create a copy of the main image to draw on
        result_image = main_image.copy()
        cv2.rectangle(result_image, top_left, bottom_right, (0, 255, 0), 2)
        
        # Display the result
        ax.imshow(cv2.cvtColor(result_image, cv2.COLOR_BGR2RGB))
        ax.set_title(f'{method_name}\nMatch Value: {match_value:.3f}')
        ax.axis('off')
    
    plt.tight_layout()
    plt.show()
    
    # Detailed analysis with the best method (TM_CCOEFF_NORMED)
    detailed_analysis(main_image, template, main_gray, template_gray)

def detailed_analysis(main_image, template, main_gray, template_gray):
    """
    Detailed analysis with multiple match detection
    """
    # Use TM_CCOEFF_NORMED as it's generally most reliable
    result = cv2.matchTemplate(main_gray, template_gray, cv2.TM_CCOEFF_NORMED)
    
    # Set threshold for multiple detections
    threshold = 0.8
    locations = np.where(result >= threshold)
    
    template_h, template_w = template_gray.shape
    
    # Create visualization
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    fig.suptitle('Detailed Template Matching Analysis', fontsize=14)
    
    # Original image
    axes[0, 0].imshow(cv2.cvtColor(main_image, cv2.COLOR_BGR2RGB))
    axes[0, 0].set_title('Original Image')
    axes[0, 0].axis('off')
    
    # Template
    axes[0, 1].imshow(cv2.cvtColor(template, cv2.COLOR_BGR2RGB))
    axes[0, 1].set_title('Template')
    axes[0, 1].axis('off')
    
    # Matching result heatmap
    im = axes[1, 0].imshow(result, cmap='hot', interpolation='nearest')
    axes[1, 0].set_title('Matching Confidence Heatmap')
    plt.colorbar(im, ax=axes[1, 0])
    
    # Results with all detections
    result_image = main_image.copy()
    for pt in zip(*locations[::-1]):  # Switch columns and rows
        cv2.rectangle(result_image, pt, (pt[0] + template_w, pt[1] + template_h), (0, 255, 0), 2)
    
    axes[1, 1].imshow(cv2.cvtColor(result_image, cv2.COLOR_BGR2RGB))
    axes[1, 1].set_title(f'All Matches (Threshold: {threshold})')
    axes[1, 1].axis('off')
    
    plt.tight_layout()
    plt.show()
    
    print(f"Found {len(locations[0])} matches above threshold {threshold}")

def multi_scale_template_matching(main_image, template):
    """
    Template matching with multiple scales to handle size variations
    """
    main_gray = cv2.cvtColor(main_image, cv2.COLOR_BGR2GRAY)
    template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
    
    # Define scale range
    scales = np.linspace(0.5, 2.0, 20)
    best_match = None
    best_val = -1
    best_scale = 1
    
    template_h, template_w = template_gray.shape
    
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    for scale in scales:
        # Resize template
        resized_template = cv2.resize(template_gray, 
                                    (int(template_w * scale), int(template_h * scale)))
        
        # Skip if template is larger than main image
        if resized_template.shape[0] > main_gray.shape[0] or resized_template.shape[1] > main_gray.shape[1]:
            continue
        
        # Perform template matching
        result = cv2.matchTemplate(main_gray, resized_template, cv2.TM_CCOEFF_NORMED)
        _, max_val, _, max_loc = cv2.minMaxLoc(result)
        
        # Keep track of best match
        if max_val > best_val:
            best_val = max_val
            best_match = max_loc
            best_scale = scale
            best_template_size = resized_template.shape
    
    # Visualize results
    axes[0].imshow(cv2.cvtColor(main_image, cv2.COLOR_BGR2RGB))
    axes[0].set_title('Original Image')
    axes[0].axis('off')
    
    axes[1].imshow(cv2.cvtColor(template, cv2.COLOR_BGR2RGB))
    axes[1].set_title('Template')
    axes[1].axis('off')
    
    # Draw best match
    result_image = main_image.copy()
    if best_match:
        top_left = best_match
        bottom_right = (top_left[0] + best_template_size[1], 
                       top_left[1] + best_template_size[0])
        cv2.rectangle(result_image, top_left, bottom_right, (0, 255, 0), 2)
    
    axes[2].imshow(cv2.cvtColor(result_image, cv2.COLOR_BGR2RGB))
    axes[2].set_title(f'Best Match (Scale: {best_scale:.2f}, Confidence: {best_val:.3f})')
    axes[2].axis('off')
    
    plt.tight_layout()
    plt.show()
    
    return best_match, best_val, best_scale

def create_sample_image():
    """
    Create a sample image for demonstration
    """
    # Create a 400x300 image with some patterns
    img = np.ones((300, 400, 3), dtype=np.uint8) * 200
    
    # Add some geometric shapes
    cv2.rectangle(img, (50, 50), (150, 100), (255, 0, 0), -1)
    cv2.circle(img, (300, 150), 40, (0, 255, 0), -1)
    cv2.rectangle(img, (200, 200), (350, 250), (0, 0, 255), -1)
    
    # Add some noise
    noise = np.random.randint(0, 50, (300, 400, 3))
    img = cv2.add(img, noise.astype(np.uint8))
    
    return img

def create_sample_template():
    """
    Create a sample template (part of the main image)
    """
    # Create a small template that matches part of the main image
    template = np.ones((50, 100, 3), dtype=np.uint8) * 200
    cv2.rectangle(template, (0, 0), (100, 50), (255, 0, 0), -1)
    
    # Add some noise
    noise = np.random.randint(0, 20, (50, 100, 3))
    template = cv2.add(template, noise.astype(np.uint8))
    
    return template

def load_and_match_custom_images(main_image_path, template_path):
    """
    Function to use with your own images
    Usage: load_and_match_custom_images('path/to/main/image.jpg', 'path/to/template.jpg')
    """
    # Load images
    main_image = cv2.imread(main_image_path)
    template = cv2.imread(template_path)
    
    if main_image is None or template is None:
        print("Error: Could not load images. Check file paths.")
        return
    
    # Convert to grayscale
    main_gray = cv2.cvtColor(main_image, cv2.COLOR_BGR2GRAY)
    template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
    
    # Perform template matching
    result = cv2.matchTemplate(main_gray, template_gray, cv2.TM_CCOEFF_NORMED)
    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
    
    # Get template dimensions
    h, w = template_gray.shape
    
    # Draw rectangle around matched area
    top_left = max_loc
    bottom_right = (top_left[0] + w, top_left[1] + h)
    result_image = main_image.copy()
    cv2.rectangle(result_image, top_left, bottom_right, (0, 255, 0), 2)
    
    # Display results
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    axes[0].imshow(cv2.cvtColor(main_image, cv2.COLOR_BGR2RGB))
    axes[0].set_title('Main Image')
    axes[0].axis('off')
    
    axes[1].imshow(cv2.cvtColor(template, cv2.COLOR_BGR2RGB))
    axes[1].set_title('Template')
    axes[1].axis('off')
    
    axes[2].imshow(cv2.cvtColor(result_image, cv2.COLOR_BGR2RGB))
    axes[2].set_title(f'Match Result (Confidence: {max_val:.3f})')
    axes[2].axis('off')
    
    plt.tight_layout()
    plt.show()
    
    return max_loc, max_val

# Main execution
if __name__ == "__main__":
    print("Template Pattern Detection Demo")
    print("=" * 40)
    
    # Run the comprehensive demo
    template_matching_demo()
    
    # Example of multi-scale matching
    print("\nRunning multi-scale template matching...")
    # main_img = create_sample_image()
    # template_img = create_sample_template()
    multi_scale_template_matching(r"E:\loveable_AI\bunch\Image_Detection_opencv\input_images\Screenshot 2025-09-02 215516.png", r"C:\Users\<USER>\Downloads\images.png")
    
    print("\nDemo completed!")
    print("\nTo use with your own images:")
    print("load_and_match_custom_images('path/to/your/image.jpg', 'path/to/your/template.jpg')")