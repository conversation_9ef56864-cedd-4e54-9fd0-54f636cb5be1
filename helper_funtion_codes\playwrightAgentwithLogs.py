# playwright_runner.py

import asyncio
import sys
import os
import logging
import json  # Added for pretty-printing dict responses
from contextlib import asynccontextmanager
from google.adk.agents.llm_agent import Agent
from google.adk.tools.mcp_tool.mcp_toolset import (
    MCPToolset,
    StdioConnectionParams,
    StdioServerParameters,
)

import litellm
from litellm.integrations.custom_logger import CustomLogger
from google.adk.agents.invocation_context import InvocationContext
from google.adk.events import Event, EventActions
from google.adk.sessions import InMemorySessionService
from google.adk.runners import Runner
from google.genai import types
from pydantic import BaseModel, Field
from google.genai.types import Schema
from google.adk.models.lite_llm import LiteLlm

# Configure logging for MCP to capture internal activity
# Changed from ERROR to DEBUG to capture all MCP internal logs
logging.getLogger("google.adk.tools.mcp_tool").setLevel(logging.DEBUG)
logging.getLogger("google.adk.tools").setLevel(logging.DEBUG)  # Added for broader tool logging

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class PlaywrightAgent:
    def __init__(self, model="gemini-2.5-pro", log_file: str = "agent_logs.txt"):
        # self.model = model
        self.model = LiteLlm(model="openai/gpt-5-2025-08-07")
        self.agent = None
        self.session_service = InMemorySessionService()
        self.runner = None
        self.mcp_toolset = None
        self.log_file = log_file
        self.logger = self._setup_logger()
        self.mcp_logger = self._setup_mcp_logger()  # Added dedicated MCP logger

    def _setup_logger(self):
        logger = logging.getLogger("PlaywrightAgentLogger")
        logger.setLevel(logging.DEBUG)

        # Create handlers
        # File handler writes logs to a file
        fh = logging.FileHandler(self.log_file, mode='a', encoding='utf-8')
        fh.setLevel(logging.DEBUG)

        # Console/stream handler (optional)
        ch = logging.StreamHandler()
        ch.setLevel(logging.INFO)  # maybe less verbose on console

        # Create formatter and add to handlers
        formatter = logging.Formatter(
            '%(asctime)s | %(levelname)s | %(name)s | %(message)s'
        )
        fh.setFormatter(formatter)
        ch.setFormatter(formatter)

        # Add handlers to logger
        logger.addHandler(fh)
        logger.addHandler(ch)

        return logger
    
    def _setup_mcp_logger(self):
        """Setup dedicated MCP internal activity logger"""
        # Capture MCP-specific logs
        mcp_logger = logging.getLogger("google.adk.tools.mcp_tool")
        mcp_logger.setLevel(logging.DEBUG)
        
        # Also capture broader tool logs
        tools_logger = logging.getLogger("google.adk.tools")
        tools_logger.setLevel(logging.DEBUG)
        
        # Create a dedicated file handler for MCP logs
        mcp_fh = logging.FileHandler(f"mcp_{self.log_file}", mode='a', encoding='utf-8')
        mcp_fh.setLevel(logging.DEBUG)
        
        # Add formatter with more detail for MCP
        mcp_formatter = logging.Formatter(
            '%(asctime)s | MCP | %(levelname)s | %(name)s | %(funcName)s:%(lineno)d | %(message)s'
        )
        mcp_fh.setFormatter(mcp_formatter)
        
        # Add handler to both MCP loggers
        mcp_logger.addHandler(mcp_fh)
        tools_logger.addHandler(mcp_fh)
        
        # Also add to main log file for consolidated view
        main_fh = logging.FileHandler(self.log_file, mode='a', encoding='utf-8')
        main_fh.setLevel(logging.DEBUG)
        main_formatter = logging.Formatter(
            '%(asctime)s | MCP_INTERNAL | %(levelname)s | %(message)s'
        )
        main_fh.setFormatter(main_formatter)
        mcp_logger.addHandler(main_fh)
        
        return mcp_logger
        
    async def __aenter__(self):
        """Async context manager entry"""
        await self.initialize()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit with proper cleanup"""
        await self.cleanup()
        
    async def initialize(self):

        # Basic config for litellm internal debug if needed
        litellm._turn_on_debug()
        litellm.json_logs = True

        # Register custom callback logger for success & failure
        class MyCustomLogger(CustomLogger):
            def log_pre_api_call(self, model, messages, kwargs):
                # Optional: log before making API call
                self_outer = self_outer_logger  # closure capture
                self_outer.logger.debug(f"LiteLLM Pre-API call: model={model}, messages={messages}")

            def log_post_api_call(self, kwargs, response_obj, start_time, end_time,self_outer):
                self_outer.logger.debug(f"LiteLLM Post-API: duration={(end_time - start_time).total_seconds():.3f}s")

            def log_success_event(self, kwargs, response_obj, start_time, end_time,self_outer):
                # Log the successful response
                try:
                    model_name = kwargs.get("model", "")
                    # Extract input / messages
                    messages = kwargs.get("messages", [])
                    last_user_msg = None
                    for m in reversed(messages):
                        if m.get("role") == "user":
                            last_user_msg = m.get("content")
                            break
                    duration = (end_time - start_time).total_seconds()

                    # Extract output text (adapt depending on structure)
                    output_text = None
                    if hasattr(response_obj, "choices"):
                        # e.g. OpenAI-style
                        output_text = response_obj.choices[0].message.content
                    else:
                        output_text = str(response_obj)

                    # Log
                    self_outer.logger.info(
                        f"LiteLLM Success | model={model_name} | duration={duration:.3f}s | prompt='{last_user_msg}' | response='{output_text}'"
                    )
                except Exception as e:
                    self_outer.logger.warning(f"Error in success callback logging: {e}")

            def log_failure_event(self, kwargs, response_obj, start_time, end_time,self_outer):
                # Log failures
                try:
                    model_name = kwargs.get("model", "")
                    duration = (end_time - start_time).total_seconds()
                    self_outer.logger.error(
                        f"LiteLLM Failure | model={model_name} | duration={duration:.3f}s | error={response_obj}"
                    )
                except Exception as e:
                    self_outer.logger.warning(f"Error in failure callback logging: {e}")

        # Create closure to access outer `self.logger`
        self_outer_logger = self  # hack to get access inside inner class

        custom_logger = MyCustomLogger()
        litellm.callbacks = [custom_logger]

        """Initialize the agent and its components"""
        
        # Log MCP initialization start
        self.logger.info("Starting MCP Toolset initialization...")
        
        self.mcp_toolset = MCPToolset(
            connection_params=StdioConnectionParams(
                server_params=StdioServerParameters(
                    command="npx",
                    args=["@playwright/mcp@latest"]
                ),
                timeout=60000.0
            )
        )
        
        # Initialize the MCP toolset
        try:
            # Log MCP connection details
            self.logger.debug(f"MCP Connection params: command='npx', args={['@playwright/mcp@latest']}, timeout=60000.0")
            
            # If MCPToolset has an async initialization method, use it
            if hasattr(self.mcp_toolset, 'initialize'):
                self.logger.debug("Calling MCP toolset initialize method...")
                await self.mcp_toolset.initialize()
                self.logger.info("MCP toolset initialized successfully")
            else:
                self.logger.debug("MCP toolset has no explicit initialize method")
                
            # Log available MCP tools/methods if accessible
            if hasattr(self.mcp_toolset, 'get_tools'):
                tools = self.mcp_toolset.get_tools()
                self.logger.debug(f"Available MCP tools: {[tool.name if hasattr(tool, 'name') else str(tool) for tool in tools]}")
                
        except Exception as e:
            self.logger.warning(f"MCPToolset initialization warning: {e}", exc_info=True)
        
        self.agent = Agent(
            name="playwright_agent",
            model=self.model,
            description="Browser automation agent with Playwright",
            instruction="""
            You are a browser automation agent powered by Playwright. Your job is to interact with web pages just like a human would, using Playwright commands. You can open URLs, click buttons, fill input fields, take screenshots, wait for elements, and extract content.          
            Always ensure proper error handling and wait for elements to be ready before interacting with them.
            """,
            tools=[self.mcp_toolset],
        )
        
        self.runner = Runner(
            agent=self.agent, 
            app_name="playwright_app", 
            session_service=self.session_service
        )
        
        self.logger.info("PlaywrightAgent fully initialized")
        
    async def cleanup(self):
        """Clean up resources properly"""
        try:
            self.logger.info("Starting cleanup process...")
            
            # Close MCP toolset if it has a close method
            if self.mcp_toolset:
                self.logger.debug("Closing MCP toolset...")
                if hasattr(self.mcp_toolset, 'close'):
                    await self.mcp_toolset.close()
                    self.logger.info("MCP toolset closed successfully")
                else:
                    self.logger.debug("MCP toolset has no close method")
                    
            # Add small delay to ensure proper cleanup
            await asyncio.sleep(0.1)
            self.logger.info("Cleanup completed")
            
        except Exception as e:
            logging.error(f"Error during cleanup: {e}", exc_info=True)

    async def invoke_user_input(self, user_input: str):
        """Process user input through the agent"""
        try:
            self.logger.info(f"Processing user input: {user_input[:100]}...")  # Log first 100 chars
            
            # Create session
            await self.session_service.create_session(
                app_name="playwright_app", 
                user_id="user1", 
                session_id="sess1"
            )
            
            content = types.Content(
                parts=[types.Part(text=user_input)], 
                role="user"
            )
            
            # Create async generator for events
            events_async = self.runner.run_async(
                user_id="user1", 
                session_id="sess1", 
                new_message=content
            )
            
            result = None
            event_count = 0
            
            try:
                async for event in events_async:
                    event_count += 1
                    
                    # Log all event types for MCP tracking
                    self.logger.debug(f"Event #{event_count} - Role: {event.content.role}, Parts: {len(event.content.parts) if event.content.parts else 0}")
                    
                    # Enhanced logging for MCP (tool) responses
                    if event.content.role == "function":
                        try:
                            func_resp = event.content.parts[0].function_response
                            
                            # Log tool invocation details
                            self.logger.info(f"=== MCP Tool Invocation ===")
                            self.logger.info(f"Tool Name: {func_resp.name}")
                            
                            # Log the response with better formatting
                            if isinstance(func_resp.response, dict):
                                response_str = json.dumps(func_resp.response, indent=2)
                                self.logger.info(f"MCP Response (dict):\n{response_str}")
                                
                                # Log specific fields if they exist (common MCP response patterns)
                                if 'status' in func_resp.response:
                                    self.logger.debug(f"Tool Status: {func_resp.response['status']}")
                                if 'error' in func_resp.response:
                                    self.logger.error(f"Tool Error: {func_resp.response['error']}")
                                if 'data' in func_resp.response:
                                    self.logger.debug(f"Tool Data Type: {type(func_resp.response['data'])}")
                            else:
                                response_str = str(func_resp.response)
                                self.logger.info(f"MCP Response (raw): {response_str}")
                            
                            self.logger.info(f"=== End MCP Tool Response ===")
                            
                        except Exception as log_err:
                            self.logger.warning(f"Error logging MCP response: {log_err}", exc_info=True)
                    
                    # Log model thinking/processing events
                    if event.content.role == "model":
                        self.logger.debug(f"Model processing event with {len(event.content.parts) if event.content.parts else 0} parts")
                    
                    if event.is_final_response() and event.content.parts:
                        result = {
                            "status": True,
                            "message": "Data retrieved successfully",
                            "data": event.content.parts[0].text
                        }
                        self.logger.info(f"Final response received after {event_count} events")
                        break
                        
            except Exception as e:
                self.logger.error(f"Error processing events: {e}", exc_info=True)
                result = {
                    "status": False,
                    "message": "Failed when using MCP",
                    "data": str(e)
                }
                
            finally:
                # Properly close the async generator
                try:
                    await events_async.aclose()
                    self.logger.debug("Events generator closed")
                except Exception as e:
                    logging.warning(f"Warning closing events generator: {e}")
                    
            return result if result else {
                "status": False,
                "message": "No response received",
                "data": None
            }
            
        except Exception as e:
            self.logger.error(f"Error in invoke_user_input: {e}", exc_info=True)
            return {
                "status": False,
                "message": f"Failed to process request: {str(e)}",
                "data": None
            }

async def main():
    """Main function with proper async context management"""
    user_instruction = """
    You are a Playwright automation agent integrated with MCP (Model Context Protocol).
 Objective: Your task is to find specific information in Salesforce and a PDF, and then use it to fill out a form in another system.

Primary Rule: You must open each new website link in a new browser tab. Keep all tabs open until the process is complete.

Open a chrome browser and follow the below actions

----------------------------------------------------
Step 1: Get the Source Information from Salesforce
----------------------------------------------------

1.  Open a new tab and paste the link :  "https://hhinsgroup--qburstqa.sandbox.lightning.force.com"  
2. Log in with the following credentials:  
   • Username: <EMAIL>  
   • Password: 3Z8~u\[42  
3. Navigate to the "Tasks" section.  
4. Use the GLOBAL SEARCH BAR (top search input only — do not use any other search fields) to search for:  
   "Elizabeth & William Diaz household"  
   Then click on the matching result.  
5. On the Task page, click the **Account** data link, then click the **Account** name hyperlink.  
   → This page contains the source information. Keep this tab open.  



----------------------------------------------------
Step 2: Find the Quote Number in Google Drive
----------------------------------------------------

1. Open a new tab and paste the link : "https://drive.google.com/drive/folders/1QGfOmC1gjf72vuXQo2Zhng4o6YbxrMQJ?usp=sharing"
2. Open the PDF file named "Guevara HO3 AI.pdf".
3. Locate the "quote number" inside this document and copy it.

----------------------------------------------------
Step 3: Enter the Information into the American Integrity System
----------------------------------------------------

1. Open a new tab and paste the link : "https://ai.iscs.com/innovation"
2. If you are asked to log in, use these credentials:
   - Username: AG8529
   - Password: Potatoes2025!
3. Search the quote number fetched from the drive (step2)  in American Integrity (Tab title noted as Guidewire InsuranceNow) website
4. Open the application and find the section named "Underwriting Questions." This is the form you need to fill out.
5. Match and transfer information:  
    

    For each question in the Underwriting Questions form, switch to the Salesforce tab opened in Step 1.
    Navigate to the specific Salesforce object and section as outlined in the question instructions above.
    Follow the detailed mapping instructions provided for each question to locate the exact field and read its value.
    Copy the exact answer from the specified Salesforce field and paste it into the corresponding field in the Underwriting Questions form.
    If the required subheading is not immediately visible, scroll down on the Salesforce page to locate it.
    If the question field is not immediately visible in the Underwriting Questions form, scroll down to locate it.

    1. During the last 5 years, has any applicant been convicted of any degree of the crime of insurance related fraud, bribery, arson or any arson related crime in connection with this or any other property?*
    - Open the target user's **Account** record in Salesforce in step 1.
    - Inside the Applicant Information subheading, locate the field "Felony, insurance fraud, arson, etc..?".
    - If value indicates conviction (true/yes), select Yes.
    - If value indicates no conviction (false/no/empty), select No.

    2. Has the applicant(s) had a personal or business foreclosure, repossession or bankruptcy in the past 5 years?*
    - Open **Account** record in Salesforce in step 1.
    - Inside Applicant Information, locate "Repossession, foreclosure or bankruptcy?".
    - If repossession/foreclosure/bankruptcy = Yes, select Yes.
    - If none/false/no/empty, select No.

    3. Has the applicant(s) had any fire or liability losses within the past 5 years?*
    - Open **Claim** record in Salesforce in step 1.
    - Inside Loss Details, check "Loss Type" and "Loss Date".
    - If Loss Type = Fire AND Loss Date within last 5 years, select Yes.
    - Otherwise, select No.

    4. Has the applicant(s) ever had a flood loss at the location stated in this application?*
    - Open **Property** record in Salesforce in step 1.
    - Inside Underwriting Information, locate "Any flood losses at this property?".
    - If Yes, select Yes.
    - If No/empty, select No.

    5. Has the applicant(s) been cancelled, declined or non-renewed by any property insurance carrier in the past 3 years?*
    - Open **Account** record in Salesforce in step 1.
    - Inside Applicant Information, locate "Cxled, NonRenewed or Declined Coverage?".
    - If cancellation/decline/non-renewal = Yes, select Yes.
    - If none/false/no/empty, select No.

    6. Has the applicant(s) had more than 1 non-weather related losses within the past 3 years?*
    - Open **Claim** record in Salesforce in step 1.
    - Inside Loss Details, check "Loss Type" and "Loss Date".
    - Count all losses NOT Flood, Lightning, Wind within last 3 years.
    - If >1 non-weather loss = Yes.
    - If ≤1 non-weather loss = No.

    7. Has the applicant(s), or any person who will be an insured under this policy ever requested a sinkhole investigation, ground study, and/or sinkhole inspection?*
    - Open **Property** record in Salesforce in step 1.
    - Inside Underwriting Information, locate "Has property ever had sinkhole activity?".
    - If Yes, select Yes.
    - If No/empty, select No.

    8. Has the applicant(s) and/or additional insureds ever submitted a claim for sinkhole damage/loss?*
    - Open **Property** record in Salesforce in step 1.
    - Inside Underwriting Information, locate "Any sinkhole claims?".
    - If Yes, select Yes.
    - If No/empty, select No.

    9. Does the applicant(s) have prior insurance? (If property is a new purchase or new construction, answer "Yes").*
    - Open **Opportunity** record in Salesforce in step 1.
    - Inside Currently Owned, locate "Currently Insured".
    - Apply rules:
    - New Purchase = Yes
    - Currently Insured = Yes
    - Never Insured = No
    - Lapsed = No
    - Cancelled = No

    10. Has there been a lapse in continuous homeowners coverage during the past year?*
    - Open **Opportunity** record in Salesforce in step 1.
    - Inside Currently Owned, locate "Any lapse in coverage >30 days?".
    - If Yes, select Yes.
    - If No/empty, select No.

    11. Does the applicant(s)/occupant(s) own or care for any animals?*
    - Open **Account** record in Salesforcein step 1.
    - Inside **Account** Information, locate "Count of Animals".
    - If 0 = No.
    - If >0 = Yes.

    12. Does the applicant(s)/occupant(s) have any non-domesticated, exotic animals?*
    - Open **Property** record in Salesforce in step 1.
    - Inside Animals, locate "Species".
    - If Other = Yes.
    - Else = No.

    13. Does the applicant(s)/occupant(s) own recreational vehicles (ATV’s, etc.)?*
    - Open **Property** record in Salesforce in step 1.
    - Inside Fun Stuff, locate "ATV?".
    - If Yes, select Yes.
    - If No/empty, select No.

    14. Does the insured location have excessive/unusual liability exposures?*
    - Open **Property** record in Salesforce in step 1.
    - Check Fun Stuff and Animals:
    - Diving Board or slide?
    - Pool fully fenced/walled/screened?
    - Vicious or Biting History?
    - Dog Breed?
    - Skateboard/bicycle ramps?
    - If Diving Board=No, Pool Fence=Yes, Vicious=Biting=No, Dog Breed ≠ Pit Bull/Staffordshire/Wolf/Wolf hybrid, Ramps=No → Select No.
    - Else → Select Yes.

    15. Will the applicant(s) occupy the property within 30 days of policy effective date?*
    - Open **Opportunity** record in Salesforce in step 1.
    - Inside Occupancy, locate "Home vacant for >30 days after closing?".
    - If vacancy >30 days = No.
    - If occupancy within 30 days = Yes.

    16. Has the applicant(s) had 1+ non-weather water losses in past 3 years?*
    - give as No.

    17. Was the property a short-sale or foreclosure prior to purchase?*
    - Open **Account** record in Salesforce in step 1.
    - Inside Applicant Information, locate "Repossession, foreclosure or bankruptcy?".
    - If foreclosure = Yes.
    - Else = No.

    18. Does the insured location have existing/unrepaired damage?*
    - Open **Property** record in Salesforce in step 1.
    - Inside Underwriting Information, locate "Existing Damage at Residence?".
    - If Yes, select Yes.
    - If No/empty, select No.

    19. At time of purchase/building, were there disclosures concerning sinkhole activity?*
    - Open **Property** record in Salesforce in step 1.
    - Inside Underwriting Information, locate "Has property ever had sinkhole activity?".
    - If Yes, select Yes.
    - If No/empty, select No.

    20. Does the insured location have a pool, hot tub, or spa?*
    - Open **Property** record in Salesforce in step 1.
    - Inside Fun Stuff, locate "Pool?".
    - If Yes, select Yes.
    - If No/empty, select No.


    21. Is the insured location occupied by 3+ unrelated individuals?*
    - Default to No (no Salesforce mapping).

    22. Is there any business activity on the premises?*
    - Open **Property** record in Salesforce in step 1.
    - Inside Underwriting Information, locate "Any business conducted at the property?".
    - If Yes, select Yes.
    - If No/empty, select No.

    23. Is there child/adult day care on premises?*
    - Open **Property** record in Salesforce in step 1.
    - Inside Underwriting Information, locate "Daycare or rehab services conducted?".
    - If Yes, select Yes.
    - If No/empty, select No.

    24. Does the property have any known sinkhole activity?*
    - Open **Property** record in Salesforce in step 1.
    - Inside Underwriting Information, locate "Has property ever had sinkhole activity?".
    - If Yes, select Yes.
    - If No/empty, select No.

    25. Has the insured location been vacant/unoccupied 30+ days prior to purchase?*
    - Open **Opportunity** record in Salesforce in step 1.
    - Inside Occupancy, locate "Home vacant for >30 days after closing?".
    - If Yes, select Yes.
    - If No/Unknown, select No.

    26. Is the insured location in a Special Flood Hazard Area?*
    - Open **Property** record in Salesforce in step 1.
    - Inside EC & Flood Information, locate "Hazardous Flood Zone?".
    - If No, select No.
    - If Zone A or Zone V indicated, follow picklist options.

    27. Has the applicant ever been insured with American Integrity?*
    - Open **Opportunity** record in Salesforce in step 1.
    - Inside Quote(s) to Bind, locate "Have you ever been insured with this carrier before?".
    - If Yes, select Yes.
    - If No/empty, select No.

    28. Has the prospective insured ever been party to a lawsuit against an insurance company?*
    - Open **Account** record in Salesforce in step 1.
    - Inside Applicant Information, locate "Ever sued your insurance carrier?".
    - If Yes, select Yes.
    - If No/empty, select No.

    29. Has the prospective insured had an assignment of benefits claim that resulted in a lawsuit?*
    - Open **Account** record in Salesforce in step 1.
    - Inside Applicant Information, locate "Ever signed an assignment of benefits?".
    - If Yes, select Yes.
    - If No/empty, select No.



6. After transferring all available information from Salesforce into the Underwriting Questions form, click **"SAVE"** to complete the process.  

    """
    


    try:
        # Use async context manager for proper resource management
        async with PlaywrightAgent() as agent:
            result = await agent.invoke_user_input(user_instruction)
            print("\n✅ Result:", result)
            
    except Exception as e:
        print(f"\n❌ Main error: {e}")
        import traceback
        traceback.print_exc()

# Alternative approach without context manager
async def main_simple():
    """Alternative approach with manual cleanup"""
    agent = None
    try:
        agent = PlaywrightAgent()
        await agent.initialize()
        
        user_instruction = "Navigate to https://example.com and take a screenshot"
        
        result = await agent.invoke_user_input(user_instruction)
        print("\n✅ Result:", result)
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        if agent:
            await agent.cleanup()
            # Add extra delay to ensure all resources are freed
            await asyncio.sleep(0.5)

if __name__ == "__main__":
    # Choose which approach to use
    # asyncio.run(main())  # With context manager
    asyncio.run(main())  # Without context manager




# """
#     You are a Playwright automation agent integrated with MCP (Model Context Protocol). Your job: parse the incoming user request, extract values for the following keys (case-insensitive): application_url, brand_name, brand_name_typo, file_name, google_drive_folder_url, next_page_button_label, platform_name, quote_number. 
    
#     Support patterns key:value, key = value, free-text mentions, and quoted values. User-supplied values override Parameters. Merge values into the parameterized JSON placeholders. If a placeholder remains unset after extraction and parameters, set it to an empty string. 
    
#     Then send the fully-merged JSON to the Playwright MCP tool for execution. 
    
#     If some action says "navigated to" then open an new tab and If the instruction doesn’t reference a valid URL, combine the instruction with the URL to perform the action
    
  
#     You are a Playwright automation agent integrated with MCP (Model Context Protocol). Your job: parse the incoming user request, extract values for the following keys (case-insensitive): application_url, brand_name, brand_name_typo, file_name, google_drive_folder_url, next_page_button_label, platform_name, quote_number. 
    
#     Support patterns key:value, key = value, free-text mentions, and quoted values. User-supplied values override Parameters. Merge values into the parameterized JSON placeholders. If a placeholder remains unset after extraction and parameters, set it to an empty string. 
    
#     Then send the fully-merged JSON to the Playwright MCP tool for execution. 
    
#     If some action says "navigated to" then open an new tab 
    
#     Parameterized JSON (compact, escaped): {"intent":"To search for an existing insurance quote in the <<brand_name>> application using a quote number found in a PDF document, and navigate through the dwelling detail pages.","action_summary":"The user navigated to Google Drive to open a quote PDF, copied the quote number, returned to the <<brand_name>> application which has Guidewire tab to search for the quote, updated the insured's date of birth, and then clicked through the subsequent policy detail pages.","steps":[{"step_number":1,"action":"Started on the <<brand_name>> website with \"<<platform_name>>\" page at ai.iscs.com/innovation.","details":{"target_element":"webpage","page_url":"<<application_url>>","timestamp":"0.0"}},{"step_number":2,"action":"Navigated to <<google_drive_folder_url>>","details":{"target_element":"address_bar","input_value":"<<google_drive_folder_url>>","page_url":"<<google_drive_folder_url>>","timestamp":"8.0"}},{"step_number":3,"action":"Clicked on the file row for \"<<file_name>>\".","details":{"target_element":"file_list_table row for \"<<file_name>>\"","page_url":"<<google_drive_folder_url>>","timestamp":"9.0"}},{"step_number":4,"action":"Switched to tab: <<file_name>>","details":{"target_element":"browser_tab","page_url":"<<google_drive_folder_url>>","timestamp":"10.0"}},{"step_number":5,"action":"Switched to <<brand_name_typo>> website with the tab: <<platform_name>>","details":{"target_element":"browser_tab","page_url":"<<application_url>>","timestamp":"10.0"}},{"step_number":6,"action":"Switch back to <<application_url>>","details":{"target_element":"address_bar","input_value":"<<application_url>>","page_url":"<<application_url>>","timestamp":"17.0"}},{"step_number":7,"action":"Typed '<<quote_number>>' into the Search field.","details":{"target_element":"Search input field","input_value":"<<quote_number>>","page_url":"<<application_url>>","timestamp":"21.0"}},{"step_number":8,"action":"Clicked the search button.","details":{"target_element":"btn_search","page_url":"<<application_url>>","timestamp":"22.0"}},{"step_number":10,"action":"Clicked the \"<<next_page_button_label>>\" button.","details":{"target_element":"btn_next_page_footer","page_url":"<<application_url>>","timestamp":"44.0"}}]}
    
#     Parameters (compact, escaped): {"brand_name":"American Integrity","platform_name":"Guidewire InsuranceNow","application_url":"https://ai.iscs.com/innovation","google_drive_folder_url":"https://drive.google.com/drive/folders/1xCFdciryxHabdhw9ea62YMlCY1TegSbk?usp=drive_link","file_name":"Copy of Troyer HO3 AI.pdf","brand_name_typo":"American intergrity","quote_number":"QT-15441432","next_page_button_label":"Next Page"}
    

#     Notes: If it needs login use these credentials: username as "AG8529" and password as "Potatoes2025!"
#     """
    