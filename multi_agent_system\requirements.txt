# Multi-Agent System Requirements

# A2A Python SDK
-e ../a2a-python

# Web framework
fastapi>=0.104.0
uvicorn[standard]>=0.24.0

# HTTP client
httpx>=0.25.0

# Data validation
pydantic>=2.4.0

# Async support
asyncio-mqtt>=0.13.0

# Logging and utilities
python-multipart>=0.0.6

# Google ADK (if available)
# google-adk

# MCP Tools (if available)
# google-adk-tools-mcp

# Development dependencies
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
isort>=5.12.0
