"""Message types for inter-agent communication."""

from typing import Dict, Any, Optional, List
from pydantic import BaseModel
from enum import Enum


class MessageType(str, Enum):
    """Types of messages that can be sent between agents."""
    TASK_REQUEST = "task_request"
    TASK_RESPONSE = "task_response"
    STATUS_UPDATE = "status_update"
    ERROR = "error"
    HEARTBEAT = "heartbeat"


class TaskStatus(str, Enum):
    """Status of a task."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class AgentMessage(BaseModel):
    """Base message structure for agent communication."""
    id: str
    type: MessageType
    sender: str
    recipient: str
    timestamp: str
    data: Dict[str, Any]
    correlation_id: Optional[str] = None


class TaskRequestMessage(BaseModel):
    """Message for requesting a task from another agent."""
    task_id: str
    action: str
    parameters: Dict[str, Any]
    priority: int = 1
    timeout: Optional[int] = None


class TaskResponseMessage(BaseModel):
    """Message for responding to a task request."""
    task_id: str
    status: TaskStatus
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    progress: Optional[float] = None  # 0.0 to 1.0


class StatusUpdateMessage(BaseModel):
    """Message for status updates."""
    task_id: str
    status: TaskStatus
    progress: Optional[float] = None
    message: Optional[str] = None


class ErrorMessage(BaseModel):
    """Message for error reporting."""
    error_code: str
    error_message: str
    context: Optional[Dict[str, Any]] = None


class HeartbeatMessage(BaseModel):
    """Message for agent health checks."""
    agent_name: str
    status: str
    load: Optional[float] = None
    capabilities: Optional[List[str]] = None


# Workflow-specific message types

class SalesforceDataRequest(BaseModel):
    """Request for Salesforce data extraction."""
    task_id: str
    search_term: str = "08/19"


class SalesforceDataResponse(BaseModel):
    """Response with extracted Salesforce data."""
    task_id: str
    account_data: Dict[str, Any]
    property_data: Dict[str, Any]
    opportunity_data: Dict[str, Any]
    claims_data: Dict[str, Any]


class DocumentExtractionRequest(BaseModel):
    """Request for document processing and quote extraction."""
    document_url: Optional[str] = None
    document_name: Optional[str] = None


class DocumentExtractionResponse(BaseModel):
    """Response with extracted document data."""
    quote_number: str
    document_content: Optional[str] = None


class FormFillingRequest(BaseModel):
    """Request for form filling in American Integrity system."""
    quote_number: str
    salesforce_data: Dict[str, Any]
    form_sections: List[str] = ["underwriting", "homeowners_info"]


class FormFillingResponse(BaseModel):
    """Response after form filling completion."""
    sections_completed: List[str]
    success: bool
    errors: Optional[List[str]] = None


class WorkflowOrchestrationRequest(BaseModel):
    """Request for complete workflow orchestration."""
    workflow_id: str
    task_identifier: str
    steps: List[str] = [
        "extract_salesforce_data",
        "extract_quote_number", 
        "fill_american_integrity_form"
    ]


class WorkflowOrchestrationResponse(BaseModel):
    """Response after workflow completion."""
    workflow_id: str
    completed_steps: List[str]
    failed_steps: Optional[List[str]] = None
    final_result: Dict[str, Any]
