browser_component:
  tab_title: *********** - Guidewire
  url: ai.iscs.com/innovation
  address_bar_focused: false
  tab_state: idle
webpage:
  header:
    - type: container
      id: header_container
      bounds: {x: 0, y: 35, width: 1920, height: 40}
      children:
        - type: image
          id: logo_american_integrity
          label: AMERICAN INTEGRITY
          bounds: {x: 15, y: 45, width: 150, height: 20}
        - type: navigation
          id: main_nav
          bounds: {x: 790, y: 40, width: 400, height: 30}
          children:
            - type: link
              id: nav_home
              label: Home
              bounds: {x: 795, y: 48, width: 40, height: 20}
            - type: link
              id: nav_quote_policy
              label: Quote/Policy
              bounds: {x: 850, y: 48, width: 80, height: 20}
              state: active
            - type: link
              id: nav_claims
              label: Claims
              bounds: {x: 945, y: 48, width: 50, height: 20}
            - type: link
              id: nav_cabinets
              label: Cabinets
              bounds: {x: 1010, y: 48, width: 60, height: 20}
            - type: link
              id: nav_support
              label: Support
              bounds: {x: 1085, y: 48, width: 55, height: 20}
  sidebar:
    - type: container
      id: left_sidebar
      bounds: {x: 0, y: 80, width: 230, height: 850}
      children:
        - type: input
          id: input_search
          label: null
          bounds: {x: 10, y: 90, width: 180, height: 30}
          value: Search
        - type: button
          id: btn_search
          label: Q
          bounds: {x: 190, y: 90, width: 30, height: 30}
        - type: link
          id: link_advanced_search
          label: 'ADVANCED SEARCH: POLICY CLAIMS'
          bounds: {x: 10, y: 125, width: 210, height: 20}
        - type: text
          id: text_quote_header
          label: Quote
          bounds: {x: 10, y: 150, width: 210, height: 30}
        - type: navigation
          id: quote_nav
          bounds: {x: 10, y: 180, width: 210, height: 300}
          children:
            - type: link
              id: nav_policy
              label: Policy
              bounds: {x: 10, y: 185, width: 210, height: 30}
            - type: link
              id: nav_dwelling
              label: Dwelling
              bounds: {x: 10, y: 215, width: 210, height: 30}
              state: active
            - type: badge
              id: badge_dwelling
              label: '2'
              bounds: {x: 190, y: 220, width: 15, height: 15}
            - type: link
              id: nav_review
              label: Review
              bounds: {x: 10, y: 245, width: 210, height: 30}
            - type: link
              id: nav_attachments
              label: Attachments
              bounds: {x: 10, y: 275, width: 210, height: 30}
            - type: link
              id: nav_correspondence
              label: Correspondence
              bounds: {x: 10, y: 305, width: 210, height: 30}
            - type: link
              id: nav_tasks
              label: Tasks
              bounds: {x: 10, y: 335, width: 210, height: 30}
            - type: link
              id: nav_notes
              label: Notes
              bounds: {x: 10, y: 365, width: 210, height: 30}
            - type: link
              id: nav_policy_file
              label: Policy File
              bounds: {x: 10, y: 395, width: 210, height: 30}
    - type: container
      id: right_sidebar
      bounds: {x: 1880, y: 80, width: 40, height: 850}
      children:
        - type: button
          id: btn_summary
          label: SUMMARY
          bounds: {x: 1885, y: 120, width: 30, height: 40}
        - type: button
          id: btn_quick_qt
          label: WTBCBEP QUICK QT
          bounds: {x: 1885, y: 170, width: 30, height: 40}
        - type: button
          id: btn_new_quote
          label: NEW QUOTE
          bounds: {x: 1885, y: 220, width: 30, height: 40}
        - type: button
          id: btn_new_note
          label: NEW NOTE
          bounds: {x: 1885, y: 270, width: 30, height: 40}
        - type: button
          id: btn_new_attach
          label: NEW ATTACH...
          bounds: {x: 1885, y: 320, width: 30, height: 40}
        - type: button
          id: btn_new_task
          label: NEW TASK
          bounds: {x: 1885, y: 370, width: 30, height: 40}
  main_content:
    - type: container
      id: quote_summary
      bounds: {x: 240, y: 80, width: 1630, height: 120}
      children:
        - type: text
          id: text_quote
          label: QUOTE
          bounds: {x: 250, y: 90, width: 60, height: 30}
        - type: table
          id: table_quote_details
          bounds: {x: 320, y: 85, width: 1400, height: 40}
          headers: [Quote Number, Insured, Product, Sub Type, Policy Term, Producer,
            Status, Premium + Fees]
          rows:
            - id: row_1
              cells:
                - type: text
                  id: cell_quote_number
                  label: ***********
                - type: text
                  id: cell_insured
                  label: Landon Cassidy
                - type: text
                  id: cell_product
                  label: Voluntary Homeowners (HO3)
                - type: text
                  id: cell_sub_type
                  label: HO3
                - type: text
                  id: cell_policy_term
                  label: 06/20/2025 - 06/20/2026
                - type: link
                  id: cell_producer
                  label: HH Insurance Group, LLC
                - type: text
                  id: cell_status
                  label: In Process
                - type: text
                  id: cell_premium
                  label: $17,776.90
        - type: link
          id: link_return_to_home
          label: < Return to Home
          bounds: {x: 250, y: 130, width: 100, height: 20}
        - type: toolbar
          id: quote_actions
          bounds: {x: 1250, y: 130, width: 620, height: 30}
          children:
            - type: button
              id: btn_next_page
              label: NEXT PAGE
              bounds: {x: 1255, y: 132, width: 90, height: 25}
            - type: button
              id: btn_save
              label: SAVE
              bounds: {x: 1350, y: 132, width: 60, height: 25}
            - type: button
              id: btn_print
              label: PRINT
              bounds: {x: 1415, y: 132, width: 60, height: 25}
            - type: button
              id: btn_create_application
              label: CREATE APPLICATION
              bounds: {x: 1480, y: 132, width: 150, height: 25}
            - type: button
              id: btn_discard_changes
              label: DISCARD CHANGES
              bounds: {x: 1635, y: 132, width: 130, height: 25}
              state: disabled
            - type: button
              id: btn_view_notes
              label: VIEW NOTES
              bounds: {x: 1770, y: 132, width: 100, height: 25}
            - type: button
              id: btn_delete
              label: DELETE
              bounds: {x: 1875, y: 132, width: 70, height: 25}
            - type: button
              id: btn_more
              label: ... MORE
              bounds: {x: 1950, y: 132, width: 60, height: 25}
    - type: form
      id: form_dwelling_details
      bounds: {x: 240, y: 200, width: 1630, height: 700}
      children:
        - type: section
          id: section_prior_carrier
          label: Prior Carrier Details
          bounds: {x: 250, y: 210, width: 1610, height: 80}
          children:
            - type: dropdown
              id: dropdown_prior_carrier
              label: Prior Carrier*
              bounds: {x: 260, y: 280, width: 200, height: 30}
              value: New Purchase
            - type: input
              id: input_prior_policy_expiration
              label: Prior Policy Expiration Date
              bounds: {x: 600, y: 280, width: 200, height: 30}
              value: ''
              state: hovered
        - type: section
          id: section_insured_info
          label: Insured Information
          bounds: {x: 250, y: 320, width: 1610, height: 250}
          children:
            - type: dropdown
              id: dropdown_entity_type
              label: Entity Type*
              bounds: {x: 380, y: 350, width: 150, height: 30}
              value: Individual
            - type: text
              id: text_individual_label
              label: Individual
              bounds: {x: 380, y: 385, width: 150, height: 20}
            - type: input
              id: input_first_name
              label: First*
              bounds: {x: 550, y: 400, width: 150, height: 30}
              value: Landon
            - type: input
              id: input_middle_name
              label: Middle
              bounds: {x: 710, y: 400, width: 150, height: 30}
              value: ''
            - type: input
              id: input_last_name
              label: Last*
              bounds: {x: 870, y: 400, width: 150, height: 30}
              value: Cassidy
            - type: input
              id: input_suffix
              label: Suffix
              bounds: {x: 1030, y: 400, width: 100, height: 30}
              value: ''
            - type: input
              id: input_dob
              label: DOB*
              bounds: {x: 260, y: 450, width: 150, height: 30}
              value: 05/20/1988
            - type: dropdown
              id: dropdown_insurance_score
              label: Insurance Score*
              bounds: {x: 420, y: 450, width: 180, height: 30}
              value: Excellent (850-899)
            - type: input
              id: input_search_name
              label: Search Name*
              bounds: {x: 260, y: 490, width: 200, height: 30}
              value: Landon Cassidy
            - type: link
              id: link_reset
              label: Reset
              bounds: {x: 470, y: 495, width: 40, height: 20}
            - type: dropdown
              id: dropdown_primary_phone
              label: Primary Phone
              bounds: {x: 260, y: 530, width: 150, height: 30}
              value: Select...
            - type: input
              id: input_email
              label: Email
              bounds: {x: 260, y: 570, width: 200, height: 30}
              value: ''
            - type: checkbox
              id: checkbox_no_email
              label: No Email
              bounds: {x: 470, y: 570, width: 100, height: 30}
        - type: section
          id: section_dwelling_info
          label: Dwelling Information
          bounds: {x: 250, y: 600, width: 1610, height: 200}
          children:
            - type: input
              id: input_lookup_address
              label: Lookup Address
              bounds: {x: 260, y: 630, width: 200, height: 30}
              value: ''
            - type: checkbox
              id: checkbox_ignore_address_validation
              label: Ignore Address Validation
              bounds: {x: 880, y: 630, width: 200, height: 30}
            - type: input
              id: input_dwelling_number
              label: Number*
              bounds: {x: 330, y: 670, width: 80, height: 30}
              value: '4227'
            - type: input
              id: input_dwelling_direction
              label: Direction
              bounds: {x: 420, y: 670, width: 80, height: 30}
              value: ''
            - type: input
              id: input_dwelling_street
              label: Street*
              bounds: {x: 510, y: 670, width: 150, height: 30}
              value: 5th
            - type: input
              id: input_dwelling_suffix
              label: Suffix
              bounds: {x: 670, y: 670, width: 80, height: 30}
              value: Ave
            - type: dropdown
              id: dropdown_dwelling_post_dir
              label: Post Dir
              bounds: {x: 760, y: 670, width: 80, height: 30}
              value: ''
            - type: dropdown
              id: dropdown_dwelling_type
              label: Type
              bounds: {x: 850, y: 670, width: 80, height: 30}
              value: ''
            - type: input
              id: input_dwelling_number_2
              label: Number
              bounds: {x: 940, y: 670, width: 80, height: 30}
              value: ''
            - type: input
              id: input_dwelling_city
              label: City*
              bounds: {x: 260, y: 710, width: 150, height: 30}
              value: St Petersburg
            - type: dropdown
              id: dropdown_dwelling_county
              label: County*
              bounds: {x: 420, y: 710, width: 150, height: 30}
              value: Pinellas
            - type: dropdown
              id: dropdown_dwelling_state
              label: State*
              bounds: {x: 580, y: 710, width: 150, height: 30}
              value: Florida
            - type: input
              id: input_dwelling_zip
              label: Zip*
              bounds: {x: 740, y: 710, width: 150, height: 30}
              value: 33711-1522
            - type: text
              id: text_address_verified
              label: Address Verified
              bounds: {x: 910, y: 715, width: 100, height: 20}
            - type: link
              id: link_view_map
              label: View Map
              bounds: {x: 1015, y: 715, width: 60, height: 20}
            - type: input
              id: input_latitude
              label: Latitude*
              bounds: {x: 260, y: 750, width: 150, height: 30}
              value: '27.766685'
            - type: input
              id: input_longitude
              label: Longitude*
              bounds: {x: 420, y: 750, width: 150, height: 30}
              value: "-82.690887"
            - type: dropdown
              id: dropdown_construction_type
              label: Construction Type*
              bounds: {x: 260, y: 790, width: 150, height: 30}
              value: Masonry
            - type: dropdown
              id: dropdown_occupancy
              label: Occupancy*
              bounds: {x: 420, y: 790, width: 150, height: 30}
              value: Owner Occupied
            - type: dropdown
              id: dropdown_months_occupied
              label: Months Occupied*
              bounds: {x: 750, y: 790, width: 150, height: 30}
              value: 0 to 12 Months
        - type: section
          id: section_prior_address
          label: Prior Address
          bounds: {x: 250, y: 820, width: 1610, height: 150}
          children:
            - type: text
              id: text_resided_less_than_2_years
              label: Has the insured resided at the risk address for less than 2 years?*
              bounds: {x: 260, y: 830, width: 400, height: 20}
            - type: dropdown
              id: dropdown_resided_less_than_2_years
              label: null
              bounds: {x: 670, y: 830, width: 80, height: 30}
              value: 'Yes'
            - type: text
              id: text_prior_address_label
              label: Prior Address
              bounds: {x: 260, y: 860, width: 100, height: 20}
            - type: input
              id: input_prior_address_number
              label: Number*
              bounds: {x: 330, y: 890, width: 80, height: 30}
              value: '18001'
            - type: input
              id: input_prior_address_direction
              label: Direction
              bounds: {x: 420, y: 890, width: 80, height: 30}
              value: ''
            - type: input
              id: input_prior_address_street
              label: Street*
              bounds: {x: 510, y: 890, width: 150, height: 30}
              value: Avalon
            - type: input
              id: input_prior_address_suffix
              label: Suffix
              bounds: {x: 670, y: 890, width: 80, height: 30}
              value: Ln
            - type: dropdown
              id: dropdown_prior_address_post_dir
              label: Post Dir
              bounds: {x: 760, y: 890, width: 80, height: 30}
              value: ''
            - type: dropdown
              id: dropdown_prior_address_type
              label: Type
              bounds: {x: 850, y: 890, width: 80, height: 30}
              value: ''
            - type: input
              id: input_prior_address_number_2
              label: Number
              bounds: {x: 940, y: 890, width: 80, height: 30}
              value: ''
            - type: input
              id: input_prior_address_city
              label: City*
              bounds: {x: 260, y: 930, width: 150, height: 30}
              value: Tampa
            - type: dropdown
              id: dropdown_prior_address_county
              label: County*
              bounds: {x: 420, y: 930, width: 150, height: 30}
              value: Hillsborough
            - type: dropdown
              id: dropdown_prior_address_state
              label: State*
              bounds: {x: 580, y: 930, width: 150, height: 30}
              value: Florida
            - type: input
              id: input_prior_address_zip
              label: Zip*
              bounds: {x: 740, y: 930, width: 150, height: 30}
              value: 33647-3102
            - type: text
              id: text_prior_address_verified
              label: Address Verified
              bounds: {x: 910, y: 935, width: 100, height: 20}
            - type: link
              id: link_prior_address_view_map
              label: View Map
              bounds: {x: 1015, y: 935, width: 60, height: 20}
        - type: button
          id: btn_form_next_page
          label: Next Page
          bounds: {x: 260, y: 980, width: 100, height: 30}
  footer:
    - type: container
      id: footer_container
      bounds: {x: 0, y: 920, width: 1920, height: 30}
      children:
        - type: text
          id: text_powered_by
          label: Powered by
          bounds: {x: 900, y: 930, width: 60, height: 20}
        - type: image
          id: logo_guidewire
          label: GUIDEWIRE
          bounds: {x: 970, y: 930, width: 80, height: 20}
        - type: text
          id: text_environment
          label: 'Environment : PROD AIIG'
          bounds: {x: 1100, y: 930, width: 150, height: 20}
        - type: text
          id: text_current_logon
          label: 'Current Logon : AG8529f'
          bounds: {x: 1260, y: 930, width: 150, height: 20}
        - type: link
          id: link_sign_out
          label: (Sign Out)
          bounds: {x: 1410, y: 930, width: 60, height: 20}
        - type: text
          id: text_posting_date
          label: 'Posting Date : 05/23/2025'
          bounds: {x: 1480, y: 930, width: 150, height: 20}