import cv2
import numpy as np

def compare_detection_methods(main_image_path, template_image_path):
    """Compare different preprocessing methods for template matching"""
    
    main_image = cv2.imread(main_image_path, cv2.IMREAD_GRAYSCALE)
    template = cv2.imread(template_image_path, cv2.IMREAD_GRAYSCALE)
    
    if main_image is None or template is None:
        print("Error loading images")
        return
    
    print("Comparing different detection methods:\n")
    
    # Method 1: Direct grayscale matching
    result1 = cv2.matchTemplate(main_image, template, cv2.TM_CCOEFF_NORMED)
    _, max_val1, _, _ = cv2.minMaxLoc(result1)
    print(f"1. Direct Grayscale Matching: {max_val1:.3f}")
    print("   - Matches exact pixel intensities")
    print("   - Sensitive to brightness/contrast differences")
    
    # Method 2: Edge detection matching
    main_edges = cv2.Canny(main_image, 50, 150)
    template_edges = cv2.Canny(template, 50, 150)
    result2 = cv2.matchTemplate(main_edges, template_edges, cv2.TM_CCOEFF_NORMED)
    _, max_val2, _, _ = cv2.minMaxLoc(result2)
    print(f"\n2. Edge Detection Matching: {max_val2:.3f}")
    print("   - Matches structural outlines")
    print("   - Ignores color/brightness differences")
    
    # Method 3: Gaussian blur + matching (reduces noise)
    main_blur = cv2.GaussianBlur(main_image, (5, 5), 0)
    template_blur = cv2.GaussianBlur(template, (5, 5), 0)
    result3 = cv2.matchTemplate(main_blur, template_blur, cv2.TM_CCOEFF_NORMED)
    _, max_val3, _, _ = cv2.minMaxLoc(result3)
    print(f"\n3. Gaussian Blur Matching: {max_val3:.3f}")
    print("   - Reduces noise and small variations")
    print("   - Smooths out minor differences")
    
    # Method 4: Binary threshold matching
    _, main_binary = cv2.threshold(main_image, 127, 255, cv2.THRESH_BINARY)
    _, template_binary = cv2.threshold(template, 127, 255, cv2.THRESH_BINARY)
    result4 = cv2.matchTemplate(main_binary, template_binary, cv2.TM_CCOEFF_NORMED)
    _, max_val4, _, _ = cv2.minMaxLoc(result4)
    print(f"\n4. Binary Threshold Matching: {max_val4:.3f}")
    print("   - Converts to pure black/white")
    print("   - Very strict shape matching")
    
    # Method 5: Adaptive threshold matching
    main_adaptive = cv2.adaptiveThreshold(main_image, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
    template_adaptive = cv2.adaptiveThreshold(template, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
    result5 = cv2.matchTemplate(main_adaptive, template_adaptive, cv2.TM_CCOEFF_NORMED)
    _, max_val5, _, _ = cv2.minMaxLoc(result5)
    print(f"\n5. Adaptive Threshold Matching: {max_val5:.3f}")
    print("   - Handles varying lighting conditions")
    print("   - Better for non-uniform backgrounds")
    
    # Save all preprocessed images for visual comparison
    cv2.imwrite('debug_original_main.jpg', main_image)
    cv2.imwrite('debug_original_template.jpg', template)
    cv2.imwrite('debug_edges_main.jpg', main_edges)
    cv2.imwrite('debug_edges_template.jpg', template_edges)
    cv2.imwrite('debug_blur_main.jpg', main_blur)
    cv2.imwrite('debug_blur_template.jpg', template_blur)
    cv2.imwrite('debug_binary_main.jpg', main_binary)
    cv2.imwrite('debug_binary_template.jpg', template_binary)
    cv2.imwrite('debug_adaptive_main.jpg', main_adaptive)
    cv2.imwrite('debug_adaptive_template.jpg', template_adaptive)
    
    # Find the best method
    methods = [
        ("Direct Grayscale", max_val1),
        ("Edge Detection", max_val2),
        ("Gaussian Blur", max_val3),
        ("Binary Threshold", max_val4),
        ("Adaptive Threshold", max_val5)
    ]
    
    best_method = max(methods, key=lambda x: x[1])
    print(f"\n🏆 Best performing method: {best_method[0]} with score {best_method[1]:.3f}")
    
    # Explain why edge detection is commonly used
    print(f"\n📚 Why Edge Detection is Popular:")
    print("- Works well across different lighting conditions")
    print("- Focuses on shape rather than appearance")
    print("- Reduces false positives from texture/color variations")
    print("- Handles anti-aliasing and rendering differences")
    print("- Good balance between robustness and specificity")
    
    return methods

def why_not_direct_matching():
    """Explain problems with direct pixel matching"""
    print("❌ Problems with Direct Grayscale Matching:")
    print("1. Template: Black X (pixel value ~0) on white bg (pixel value ~255)")
    print("2. Screenshot: Gray X (pixel value ~100) on light gray bg (pixel value ~200)")
    print("3. Direct matching fails because 0 ≠ 100 and 255 ≠ 200")
    print("\n✅ Edge Detection Solution:")
    print("1. Both images: Edges detected where intensity changes rapidly")
    print("2. Both become: White edges (255) on black background (0)")
    print("3. Now they match because both have same edge structure")


def detect_pattern_binary(main_image_path, template_image_path, threshold=0.5):
    # Load images in grayscale
    main_image = cv2.imread(main_image_path, cv2.IMREAD_GRAYSCALE)
    template = cv2.imread(template_image_path, cv2.IMREAD_GRAYSCALE)
    
    if main_image is None or template is None:
        return False
    
    # Convert to binary (black and white only)
    _, main_binary = cv2.threshold(main_image, 127, 255, cv2.THRESH_BINARY)
    _, template_binary = cv2.threshold(template, 127, 255, cv2.THRESH_BINARY)
    
    # Multi-scale template matching
    scales = np.linspace(0.5, 2.0, 15)
    tH, tW = template_binary.shape
    
    for scale in scales:
        newW = int(tW * scale)
        newH = int(tH * scale)
        
        if newW < 10 or newH < 10 or newW > main_binary.shape[1] or newH > main_binary.shape[0]:
            continue
        
        resized_template = cv2.resize(template_binary, (newW, newH))
        result = cv2.matchTemplate(main_binary, resized_template, cv2.TM_CCOEFF_NORMED)
        _, max_val, _, _ = cv2.minMaxLoc(result)
        
        if max_val >= threshold:
            return True
    
    return False


# Example usage
if __name__ == "__main__":
    print("=== Understanding Why Edge Detection ===\n")
    # why_not_direct_matching()
    
    print("\n" + "="*50 + "\n")
    
    # Run comparison (uncomment to test with your images)
    compare_detection_methods(
        r'E:\loveable_AI\bunch\Image_Detection_opencv\input_images\Screenshot 2025-09-02 215516.png',
        r'E:\loveable_AI\bunch\Image_Detection_opencv\cross-x-mark-.jpg'
    )
#     result = detect_pattern_binary( r'E:\loveable_AI\bunch\Image_Detection_opencv\input_images\Screenshot 2025-09-02 215516.png',
#   r'E:\loveable_AI\bunch\Image_Detection_opencv\cross-x-mark-.jpg')
#     print("result > ",result)