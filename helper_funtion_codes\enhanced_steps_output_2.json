{"enhanced_steps": [{"step_number": 1, "timestamp": "1.0", "instruction": "Quick video here for how to locate an American Integrity quote. So I'm at the American Integrity homepage.", "source": "Both", "url": "https://ai.iscs.com/innovation", "action_details": {"target_element": "webpage", "input_value": null}}, {"step_number": 2, "timestamp": "8.0", "instruction": "Switched to tab: Test Quotes - Google Drive", "source": "UI Action", "url": "https://drive.google.com/drive/folders/1v773cXo-vfGpoJOYt9CYY5YGcpilE5TL", "action_details": {"target_element": "browser_tab", "input_value": null}}, {"step_number": 3, "timestamp": "10.0", "instruction": "User opened Cassidy HO3 AI.pdf from Google Drive.", "source": "UI Action", "url": "https://drive.google.com/drive/folders/1v773cXo-vfGpoJOYt9CYY5YGcpilE5TL", "action_details": {"target_element": "browser_tab", "input_value": null}}, {"step_number": 4, "timestamp": "10.0", "instruction": "Switched to tab: Cassidy HO3 AI.pdf", "source": "UI Action", "url": "https://drive.google.com/drive/folders/1v773cXo-vfGpoJOYt9CYY5YGcpilE5TL", "action_details": {"target_element": "browser_tab", "input_value": null}}, {"step_number": 5, "timestamp": "10.0", "instruction": "Switched to tab: Guidewire InsuranceNow™", "source": "UI Action", "url": "https://ai.iscs.com/innovation", "action_details": {"target_element": "browser_tab", "input_value": null}}, {"step_number": 6, "timestamp": "10.0", "instruction": "And let's say I wanted to find this quote here. I would highlight this quote number and then I'm going to copy that.", "source": "Audio Narration", "url": "https://ai.iscs.com/innovation", "action_details": null}, {"step_number": 7, "timestamp": "19.0", "instruction": "I'm going to paste it in this search bar up here.", "source": "Audio Narration", "url": "https://ai.iscs.com/innovation", "action_details": null}, {"step_number": 8, "timestamp": "25.0", "instruction": "When I hit search it will take me right into the quote.", "source": "Both", "url": "https://ai.iscs.com/innovation", "action_details": {"target_element": "browser_tab", "input_value": null}}, {"step_number": 9, "timestamp": "29.0", "instruction": "So this is where I was editing and making changes, such as updating <PERSON>'s Date of Birth to 05/20/1988.", "source": "Both", "url": "https://ai.iscs.com/innovation", "action_details": {"target_element": "input_dob", "input_value": "05/20/1988"}}, {"step_number": 10, "timestamp": "36.0", "instruction": "Navigate to the dwelling information section. The quote has several pages: policy, dwelling, and then review.", "source": "Both", "url": "https://ai.iscs.com/innovation", "action_details": {"target_element": "nav_dwelling", "input_value": null}}, {"step_number": 11, "timestamp": "40.0", "instruction": "User started a new quote entry, selecting <PERSON> as the customer.", "source": "UI Action", "url": "https://ai.iscs.com/innovation", "action_details": {"target_element": "radio_button", "input_value": "LANDON CASSIDY"}}, {"step_number": 12, "timestamp": "43.0", "instruction": "Continue to move through the quote by entering the address information: City: St Petersburg, County: Pinellas, State: Florida, Zip: 33711-1522", "source": "Both", "url": "https://ai.iscs.com/innovation", "action_details": {"target_element": "input_city, dropdown_county, dropdown_state, input_zip", "input_value": "St Petersburg, Pinellas, Florida, 33711-1522"}}, {"step_number": 13, "timestamp": "47.0", "instruction": "And then when the quote is filled out, you don't even have to be on the last page. You can just hit the create application button up here to turn it into an application.", "source": "Audio Narration", "url": "https://ai.iscs.com/innovation", "action_details": null}]}