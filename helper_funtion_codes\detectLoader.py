import cv2
import numpy as np
import os
import glob
from pathlib import Path

class ImagePatternDetector:
    def __init__(self, sample_image_path, threshold=0.8, method=cv2.TM_CCOEFF_NORMED):
        """
        Initialize the pattern detector
        
        Args:
            sample_image_path: Path to the sample image (e.g., image with number 3)
            threshold: Matching threshold (0-1, higher = more strict)
            method: Template matching method
        """
        self.sample_image = cv2.imread(sample_image_path)
        if self.sample_image is None:
            raise ValueError(f"Could not load sample image: {sample_image_path}")
        
        self.sample_gray = cv2.cvtColor(self.sample_image, cv2.COLOR_BGR2GRAY)
        self.threshold = threshold
        self.method = method
        
        # Store multiple scales of the template for better detection
        self.templates = self._create_template_scales()
    
    def _create_template_scales(self):
        """Create multiple scales of the template for robust detection"""
        templates = []
        scales = [0.5, 0.75, 1.0, 1.25, 1.5]
        
        for scale in scales:
            width = int(self.sample_gray.shape[1] * scale)
            height = int(self.sample_gray.shape[0] * scale)
            
            if width > 10 and height > 10:  # Minimum size check
                resized = cv2.resize(self.sample_gray, (width, height))
                templates.append((resized, scale))
        
        return templates
    
    def detect_pattern_in_image(self, image_path):
        """
        Detect if the sample pattern exists in the given image
        
        Args:
            image_path: Path to the image to analyze
            
        Returns:
            tuple: (bool: pattern_found, list: detection_info)
        """
        # Load and preprocess the target image
        target_image = cv2.imread(image_path)
        if target_image is None:
            print(f"Warning: Could not load image {image_path}")
            return False, []
        
        target_gray = cv2.cvtColor(target_image, cv2.COLOR_BGR2GRAY)
        
        detections = []
        
        # Try template matching at different scales
        for template, scale in self.templates:
            if template.shape[0] <= target_gray.shape[0] and template.shape[1] <= target_gray.shape[1]:
                result = cv2.matchTemplate(target_gray, template, self.method)
                min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
                
                # Use max_val for correlation-based methods
                confidence = max_val if self.method in [cv2.TM_CCOEFF_NORMED, cv2.TM_CCORR_NORMED] else (1 - min_val)
                
                if confidence >= self.threshold:
                    detections.append({
                        'confidence': confidence,
                        'location': max_loc if self.method in [cv2.TM_CCOEFF_NORMED, cv2.TM_CCORR_NORMED] else min_loc,
                        'scale': scale,
                        'template_size': template.shape
                    })
        
        # Alternative method: Feature matching (more robust for rotated/scaled patterns)
        pattern_found_features = self._detect_using_features(target_gray)
        
        # Return True if either method found a match
        pattern_found = len(detections) > 0 or pattern_found_features
        
        return pattern_found, detections
    
    def _detect_using_features(self, target_gray):
        """
        Alternative detection using feature matching (SIFT/ORB)
        More robust for rotated, scaled, or partially occluded patterns
        """
        try:
            # Use ORB detector (patent-free alternative to SIFT)
            orb = cv2.ORB_create()
            
            # Find keypoints and descriptors
            kp1, des1 = orb.detectAndCompute(self.sample_gray, None)
            kp2, des2 = orb.detectAndCompute(target_gray, None)
            
            if des1 is not None and des2 is not None:
                # Match features
                bf = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=True)
                matches = bf.match(des1, des2)
                
                # Sort matches by distance
                matches = sorted(matches, key=lambda x: x.distance)
                
                # Consider it a match if we have enough good matches
                good_matches = [m for m in matches if m.distance < 50]
                
                # Threshold: at least 10 good matches (adjust based on your needs)
                return len(good_matches) >= 10
            
        except Exception as e:
            print(f"Feature matching failed: {e}")
        
        return False
    
    def process_image_series(self, images_directory, output_directory=None, move_detected=True):
        """
        Process a series of images and remove/move those containing the pattern
        
        Args:
            images_directory: Directory containing images to analyze
            output_directory: Directory to move detected images (if move_detected=True)
            move_detected: If True, move detected images; if False, delete them
            
        Returns:
            dict: Summary of processing results
        """
        # Get list of image files
        image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.tif']
        image_files = []
        
        for ext in image_extensions:
            image_files.extend(glob.glob(os.path.join(images_directory, ext)))
            image_files.extend(glob.glob(os.path.join(images_directory, ext.upper())))
        
        if not image_files:
            return {"error": "No image files found in the directory"}
        
        # Create output directory if needed
        if move_detected and output_directory:
            Path(output_directory).mkdir(parents=True, exist_ok=True)
        
        results = {
            "total_images": len(image_files),
            "detected_images": [],
            "clean_images": [],
            "errors": []
        }
        
        print(f"Processing {len(image_files)} images...")
        
        for i, image_path in enumerate(image_files):
            print(f"Processing ({i+1}/{len(image_files)}): {os.path.basename(image_path)}")
            
            try:
                pattern_found, detections = self.detect_pattern_in_image(image_path)
                
                if pattern_found:
                    print(f"  ✓ Pattern detected! Confidence: {max([d['confidence'] for d in detections]) if detections else 'N/A'}")
                    results["detected_images"].append({
                        "path": image_path,
                        "detections": detections
                    })
                    
                    # Handle the detected image
                    if move_detected and output_directory:
                        # Move to output directory
                        filename = os.path.basename(image_path)
                        new_path = os.path.join(output_directory, filename)
                        os.rename(image_path, new_path)
                        print(f"  → Moved to: {new_path}")
                    elif not move_detected:
                        # Delete the image
                        os.remove(image_path)
                        print(f"  → Deleted: {image_path}")
                else:
                    print(f"  ○ No pattern detected")
                    results["clean_images"].append(image_path)
                    
            except Exception as e:
                error_msg = f"Error processing {image_path}: {str(e)}"
                print(f"  ✗ {error_msg}")
                results["errors"].append(error_msg)
        
        return results
    
    def visualize_detections(self, image_path, save_result=False, output_path=None):
        """
        Visualize detections on an image for debugging
        
        Args:
            image_path: Path to the image to analyze
            save_result: Whether to save the visualization
            output_path: Path to save the visualization
        """
        target_image = cv2.imread(image_path)
        if target_image is None:
            print(f"Could not load image: {image_path}")
            return
        
        target_gray = cv2.cvtColor(target_image, cv2.COLOR_BGR2GRAY)
        result_image = target_image.copy()
        
        # Detect patterns
        pattern_found, detections = self.detect_pattern_in_image(image_path)
        
        # Draw bounding boxes around detections
        for detection in detections:
            x, y = detection['location']
            h, w = detection['template_size']
            confidence = detection['confidence']
            
            # Draw rectangle
            cv2.rectangle(result_image, (x, y), (x + w, y + h), (0, 255, 0), 2)
            
            # Add confidence text
            cv2.putText(result_image, f"{confidence:.2f}", (x, y-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
        
        # Display result
        cv2.imshow('Pattern Detection Result', result_image)
        cv2.waitKey(0)
        cv2.destroyAllWindows()
        
        # Save if requested
        if save_result and output_path:
            cv2.imwrite(output_path, result_image)
            print(f"Visualization saved to: {output_path}")


# Example usage
def main():
    # Initialize the detector with your sample image
    sample_image_path = r"E:\loveable_AI\bunch\helper_funtion_codes\cross-x-mark-.jpg"  # Your sample image with number 3
    
    try:
        detector = ImagePatternDetector(
            sample_image_path=sample_image_path,
            threshold=0.8,  # Adjust based on your needs (0.6-0.9 typically)
            method=cv2.TM_CCOEFF_NORMED
        )
        
        # Process a directory of images
        images_directory = "input_images/"
        detected_images_dir = "detected_images/"
        
        results = detector.process_image_series(
            images_directory=images_directory,
            output_directory=detected_images_dir,
            move_detected=True  # Set to False to delete instead of move
        )
        
        # Print summary
        print("\n" + "="*50)
        print("PROCESSING SUMMARY")
        print("="*50)
        print(f"Total images processed: {results['total_images']}")
        print(f"Images with pattern detected: {len(results['detected_images'])}")
        print(f"Clean images remaining: {len(results['clean_images'])}")
        
        if results['errors']:
            print(f"Errors encountered: {len(results['errors'])}")
            for error in results['errors']:
                print(f"  - {error}")
        
        # Show detected images details
        if results['detected_images']:
            print("\nDetected images:")
            for img_info in results['detected_images']:
                print(f"  - {os.path.basename(img_info['path'])}")
                for detection in img_info['detections']:
                    print(f"    Confidence: {detection['confidence']:.3f}, Scale: {detection['scale']}")
        
    except Exception as e:
        print(f"Error initializing detector: {e}")


# Additional utility functions
def batch_visualize_detections(detector, images_directory, output_viz_dir):
    """Visualize detections for all images in a directory"""
    Path(output_viz_dir).mkdir(parents=True, exist_ok=True)
    
    image_files = []
    for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:
        image_files.extend(glob.glob(os.path.join(images_directory, ext)))
    
    for image_path in image_files:
        filename = os.path.splitext(os.path.basename(image_path))[0]
        output_path = os.path.join(output_viz_dir, f"{filename}_detection.jpg")
        detector.visualize_detections(image_path, save_result=True, output_path=output_path)


# def adjust_detector_sensitivity(sample_image_path, test_images_dir):
#     """Helper function to test different threshold values"""
#     thresholds = [0.9, 0.92, 0.95]
    
#     for threshold in thresholds:
#         print(f"\nTesting threshold: {threshold}")
#         detector = ImagePatternDetector(sample_image_path, threshold=threshold)
        
#         # Test on a few images
#         test_files = glob.glob(os.path.join(test_images_dir, "*.jpg"))[:5]
#         for test_file in test_files:
#             pattern_found, detections = detector.detect_pattern_in_image(test_file)
#             print(f"  {os.path.basename(test_file)}: {'DETECTED' if pattern_found else 'NOT DETECTED'}")


if __name__ == "__main__":
    main()