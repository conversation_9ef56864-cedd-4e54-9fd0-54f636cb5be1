import google.generativeai as genai
import json
import os
import re
from google.generativeai.types import GenerationConfig


# --- Configuration ---
# Set your Google API Key. It's recommended to use an environment variable.
# If you don't set the environment variable, you can uncomment the next line 
# and paste your key directly, but this is less secure.
# genai.configure(api_key="YOUR_API_KEY") 
try:
    genai.configure(api_key=os.environ["GOOGLE_API_KEY"])
except KeyError:
    print("Error: GOOGLE_API_KEY environment variable not set.")
    print("Please set the GOOGLE_API_KEY environment variable with your API key.")
    exit()

# --- The Main Prompt for the Generative AI Model ---
# This is the detailed set of instructions you provided.
SYSTEM_PROMPT = """
Of course. Based on your request to use the `user_intent.json` as the primary source and validate it with the `audio.json`, here is the revised prompt that reflects this logic, followed by the generated JSON output for the provided data.

### Revised and Finalized Prompt

You are an AI assistant that specializes in validating user interface actions with corresponding audio narration to create a comprehensive JSON output of step-by-step instructions.

You will be given two JSON objects as input:

1.  `user_intent_.json`: This file contains a structured list of user actions. It is the **primary source** for the output, and the final JSON will contain exactly one step for each action listed in this file.
2.  `audio_json`: This file contains the corresponding audio narration, used for **validation and enhancement** of the instructions.


Follow these rules precisely:

1.  **Iterate Through User Actions:** Process the `steps` array in `user_intent_.json` one by one. The final output will have the same number of steps in the same order.

2.  **Validation and Instruction Generation:** For each `step` from `user_intent_.json`:

      * Take the action's `timestamp`.
      * Search the `audio_json.word_timeline` for any audio segments whose timestamps are **within the nearest second** of the action's timestamp.
      * **If a match is found:** The user action is "validated" by the audio.  
            * Merge the words from the matching audio segments with the corresponding timestamps in `user_intent_.json`.  
            * Use this combined information to enrich each action from `user_intent_.json` with audio details, producing a more descriptive instruction.  
            * Do not rely solely on the audio to create the instruction; always use `user_intent_.json` as the primary source and enhance it with audio.  
            * Ensure that important details such as numbers or credentials in `user_intent_.json` are preserved and not omitted.
            * If the audio contains extra actions (e.g., copy, drag, etc.) that are not present in `user_intent_.json`, integrate them to enhance the intent while still keeping `user_intent_.json` as the base.  
   
      * **If no match is found:** The user action is a silent action.
          * Use the `action` string from the `user_intent_.json` step as the `instruction`.
          * Set the `source` to `"UI Action"`.

3.  **Final Output Format:** The final output **MUST** be a single JSON object with a top-level key: `"enhanced_steps"`. This key will hold an array of step objects, each conforming to the following schema:

      * `step_number`: (Integer) A sequential number for the step, starting from 1.
      * `timestamp`: (String) The `timestamp` from the original `user_intent_.json` step.
      * `instruction`: (String) The final instructional text, determined by Rule 2.
      * `source`: (String) Must be either `"Both"` or `"UI Action"`.
      * `url`: (String) The `page_url` from the original `user_intent_.json` step.
      * `action_details`: (Object) An object containing `target_element` and `input_value` from the original `user_intent_.json` step.

-----

```json
{{
  "enhanced_steps": [
    {{
      "step_number": 1,
      "timestamp": "010",
      "instruction": "Switched to tab: Guidewire InsuranceNow™",
      "source": "UI Action",
      "url": "https://ai.iscs.com/innovation",
      "action_details": {
        "target_element": "browser_tab",
        "input_value": null
      }}
    }},
    {{
      "step_number": 2,
      "timestamp": "025",
      "instruction": "To find the quote, I'm going to click the search bar up top.",
      "source": "Both",
      "url": "https://ai.iscs.com/innovation",
      "action_details": {
        "target_element": "search_bar",
        "input_value": null
      }}
    }}
  ]

  }}
```
"""

def clean_json_response(raw_text):
    """
    Cleans the raw text response from the AI to extract only the JSON object.
    It removes markdown code blocks and any leading/trailing text.
    """
    # Use regex to find the JSON block, even with potential newlines before/after
    match = re.search(r'```json\s*(\{.*?\})\s*```', raw_text, re.DOTALL)
    if match:
        return match.group(1)
    # Fallback for cases where markdown is not present but there's still a JSON object
    match = re.search(r'(\{.*?\})', raw_text, re.DOTALL)
    if match:
        return match.group(1)
    raise ValueError("Could not find a valid JSON object in the AI's response.")


def generate_enhanced_steps(user_intent_path, audio_path, output_path, model_name="gemini-2.5-pro"):
    """
    Generates enhanced step-by-step instructions by sending two JSON files
    and a prompt to a Generative AI model.

    Args:
        user_intent_path (str): Path to the user_intent JSON file.
        audio_path (str): Path to the audio narration JSON file.
        output_path (str): Path where the final JSON output will be saved.
        model_name (str): The name of the generative model to use.
    """
    print(f"Loading data from '{user_intent_path}' and '{audio_path}'...")
    try:
        with open(user_intent_path, 'r') as f:
            user_intent_data = json.load(f)
        with open(audio_path, 'r') as f:
            audio_data = json.load(f)
    except FileNotFoundError as e:
        print(f"Error: {e}. Please ensure the input files exist.")
        return
    except json.JSONDecodeError as e:
        print(f"Error decoding JSON from a file: {e}")
        return

    # Prepare the full prompt for the model
    full_prompt = (
        f"{SYSTEM_PROMPT}\n\n"
        f"--- Input user_intent.json ---\n"
        f"{json.dumps(user_intent_data, indent=2)}\n\n"
        f"--- Input audio.json ---\n"
        f"{json.dumps(audio_data, indent=2)}"
    )
    
    print("Initializing Generative AI model...")
    try:
        model = genai.GenerativeModel(model_name,generation_config=GenerationConfig(temperature=0.0))
        
        # Start a chat session
        chat = model.start_chat(history=[])
        
        print("Sending data to the AI for processing... This may take a moment.")
        # Send the message to the model
        response = chat.send_message(full_prompt)
        
        print("Received response from AI. Cleaning and parsing JSON...")
        # Clean the response to get pure JSON
        cleaned_json_str = clean_json_response(response.text)
        
        # Parse the cleaned string into a Python dictionary
        final_output = json.loads(cleaned_json_str)

        # Save the final JSON to the output file
        with open(output_path, 'w') as f:
            json.dump(final_output, f, indent=2)
            
        print(f"\n✅ Success! The enhanced steps have been saved to '{output_path}'")

    except Exception as e:
        print(f"\n❌ An error occurred during AI processing: {e}")
        # Optionally print the raw response for debugging
        if 'response' in locals():
            print("\n--- Raw AI Response for Debugging ---")
            print(response.text)
            print("------------------------------------")



if __name__ == "__main__":
    # Define file paths
    USER_INTENT_FILE = r"E:\loveable_AI\bunch\20250902_122847\user_intent_.json"
    AUDIO_FILE = "audio.json"
    OUTPUT_FILE = "enhanced_steps_output.json"
    
    # Create the sample files for the script to run
    
    # Run the main function
    generate_enhanced_steps(USER_INTENT_FILE, AUDIO_FILE, OUTPUT_FILE)