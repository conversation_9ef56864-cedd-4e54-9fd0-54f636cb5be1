"""Main orchestrator for the multi-agent system."""

import asyncio
import logging
import sys
import signal
from typing import Dict, Any
import uvicorn
from multiprocessing import Process
import time

from .config import AGENT_PORTS, AGENT_URLS
from .communication.a2a_client import agent_registry
from .agents.planner_agent import PlannerAgent
from .agents.salesforce_agent import SalesforceAgent
from .agents.document_agent import DocumentAgent
from .agents.american_integrity_agent import AmericanIntegrityAgent


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("main_orchestrator")


class MultiAgentOrchestrator:
    """Main orchestrator for the multi-agent system."""
    
    def __init__(self):
        self.agents = {}
        self.agent_processes = {}
        self.running = False
    
    def create_agents(self):
        """Create all agent instances."""
        self.agents = {
            "planner": PlannerAgent(),
            "salesforce": SalesforceAgent(),
            "document": DocumentAgent(),
            "american_integrity": AmericanIntegrityAgent()
        }
        
        logger.info("Created all agent instances")
    
    async def initialize_agents(self):
        """Initialize all agents."""
        for name, agent in self.agents.items():
            try:
                await agent.initialize()
                logger.info(f"Initialized {name} agent")
            except Exception as e:
                logger.error(f"Failed to initialize {name} agent: {str(e)}")
                raise
    
    def start_agent_servers(self):
        """Start all agent servers in separate processes."""
        for name, agent in self.agents.items():
            try:
                process = Process(target=self._run_agent_server, args=(name, agent))
                process.start()
                self.agent_processes[name] = process
                logger.info(f"Started {name} agent server on port {agent.port}")
                
                # Give the server time to start
                time.sleep(2)
                
            except Exception as e:
                logger.error(f"Failed to start {name} agent server: {str(e)}")
                raise
    
    def _run_agent_server(self, name: str, agent):
        """Run an agent server (called in separate process)."""
        try:
            # Set up logging for the subprocess
            logging.basicConfig(
                level=logging.INFO,
                format=f"%(asctime)s - {name}_agent - %(levelname)s - %(message)s"
            )
            
            # Initialize and run the agent
            asyncio.run(agent.initialize())
            agent.run()
            
        except Exception as e:
            logger.error(f"Error running {name} agent server: {str(e)}")
    
    async def setup_agent_registry(self):
        """Set up the agent registry with all agents."""
        for name, url in AGENT_URLS.items():
            agent_registry.register_agent(name, url)
        
        # Wait for all agents to be ready
        await asyncio.sleep(5)
        
        # Discover all agents
        await agent_registry.discover_all_agents("orchestrator")
        
        logger.info("Agent registry set up successfully")
    
    async def run_workflow(self, task_id: str = "08/19") -> Dict[str, Any]:
        """Run the complete workflow."""
        try:
            logger.info(f"Starting workflow for task: {task_id}")
            
            # Send task to planner agent
            task_data = {
                "id": f"workflow_{task_id}",
                "action": "orchestrate_workflow",
                "task_id": task_id
            }
            
            result = await agent_registry.send_task_to_agent(
                "orchestrator",
                "PlannerAgent", 
                task_data
            )
            
            logger.info(f"Workflow completed with result: {result}")
            return result
            
        except Exception as e:
            logger.error(f"Error running workflow: {str(e)}")
            raise
    
    def stop_all_agents(self):
        """Stop all agent servers."""
        logger.info("Stopping all agent servers...")
        
        for name, process in self.agent_processes.items():
            try:
                process.terminate()
                process.join(timeout=10)
                
                if process.is_alive():
                    process.kill()
                    process.join()
                
                logger.info(f"Stopped {name} agent server")
                
            except Exception as e:
                logger.error(f"Error stopping {name} agent: {str(e)}")
        
        self.agent_processes.clear()
    
    async def shutdown(self):
        """Shutdown the orchestrator."""
        logger.info("Shutting down orchestrator...")
        
        self.running = False
        
        # Close all A2A clients
        await agent_registry.close_all_clients()
        
        # Stop all agent servers
        self.stop_all_agents()
        
        logger.info("Orchestrator shutdown complete")


# Global orchestrator instance
orchestrator = MultiAgentOrchestrator()


def signal_handler(signum, frame):
    """Handle shutdown signals."""
    logger.info(f"Received signal {signum}, shutting down...")
    asyncio.create_task(orchestrator.shutdown())


async def main():
    """Main entry point."""
    try:
        # Set up signal handlers
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        logger.info("Starting Multi-Agent System Orchestrator")
        
        # Create and initialize agents
        orchestrator.create_agents()
        await orchestrator.initialize_agents()
        
        # Start agent servers
        orchestrator.start_agent_servers()
        
        # Set up agent registry
        await orchestrator.setup_agent_registry()
        
        orchestrator.running = True
        logger.info("Multi-Agent System is ready!")
        
        # Run the workflow
        result = await orchestrator.run_workflow("08/19")
        
        logger.info("Workflow execution completed")
        logger.info(f"Final result: {result}")
        
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Error in main: {str(e)}")
        raise
    finally:
        await orchestrator.shutdown()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
    except Exception as e:
        logger.error(f"Application error: {str(e)}")
        sys.exit(1)
