"""Base agent class with common functionality."""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from fastapi import FastAPI, Request, HTTPException
from pydantic import BaseModel
import uuid

from a2a.types import AgentCard, AgentCapabilities, AgentSkill
# from a2a.client import Client


class TaskRequest(BaseModel):
    """Standard task request format."""
    id: str
    message: dict  # {'role': 'user', 'parts': [{'text': str}]}
    context: Optional[Dict[str, Any]] = None


class TaskResponse(BaseModel):
    """Standard task response format."""
    id: str
    status: str  # "success", "error", "in_progress"
    data: Optional[Dict[str, Any]] = None
    message: Optional[str] = None
    error: Optional[str] = None


class BaseAgent(ABC):
    """Base class for all agents in the multi-agent system."""
    
    def __init__(self, name: str, description: str, port: int, skills: list[AgentSkill]):
        self.name = name
        self.description = description
        self.port = port
        self.skills = skills
        self.logger = logging.getLogger(f"agent.{name}")
        self.app = FastAPI(title=f"{name} Agent")
        self.agent_card = self._create_agent_card()
        self.a2a_client = None
        
        # Setup FastAPI routes
        self._setup_routes()
    
    def _create_agent_card(self) -> AgentCard:
        """Create the agent card for this agent."""
        return AgentCard(
            name=self.name,
            description=self.description,
            url=f"http://localhost:{self.port}",
            version="1.0",
            defaultInputModes=["text"],
            defaultOutputModes=["text"],
            capabilities=AgentCapabilities(
                streaming=False,
                push_notifications=False
            ),
            skills=self.skills
        )
    
    def _setup_routes(self):
        """Setup FastAPI routes."""
        
        @self.app.get("/")
        async def get_agent_card():
            return self.agent_card.model_dump()
        
        @self.app.post("/tasks")
        async def execute_task(request: TaskRequest):
            try:
                self.logger.info(f"Received task: {request.id}")
                result = await self.process_task(request)
                return result.model_dump()
            except Exception as e:
                self.logger.error(f"Error processing task {request.id}: {str(e)}")
                return TaskResponse(
                    id=request.id,
                    status="error",
                    error=str(e)
                ).model_dump()
    
    @abstractmethod
    async def process_task(self, request: TaskRequest) -> TaskResponse:
        """Process a task request. Must be implemented by subclasses."""
        pass
    
    async def initialize(self):
        """Initialize the agent. Override in subclasses if needed."""
        self.logger.info(f"Initializing {self.name} agent")
        # Initialize a2a client if needed
        # self.a2a_client = Client()
    
    async def communicate_with_agent(self, agent_url: str, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """Communicate with another agent via a2a protocol."""
        try:
            # This would use a2a client to communicate with other agents
            # For now, we'll use HTTP requests as a fallback
            import httpx
            
            task_request = TaskRequest(
                id=str(uuid.uuid4()),
                message={"role": "user", "parts": [{"text": str(task_data)}]},
                context=task_data
            )
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{agent_url}/tasks",
                    json=task_request.model_dump(),
                    timeout=300.0  # 5 minutes timeout
                )
                response.raise_for_status()
                return response.json()
                
        except Exception as e:
            self.logger.error(f"Error communicating with agent at {agent_url}: {str(e)}")
            raise
    
    def run(self):
        """Run the agent server."""
        import uvicorn
        self.logger.info(f"Starting {self.name} agent on port {self.port}")
        uvicorn.run(self.app, host="0.0.0.0", port=self.port)
