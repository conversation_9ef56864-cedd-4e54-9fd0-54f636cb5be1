browser_component:
  tab_title: *********** - Guidewire
  url: ai.iscs.com/innovation
  address_bar_focused: false
  tab_state: idle
webpage:
  header:
    - type: container
      id: header_container
      bounds: {x: 0, y: 0, width: 1920, height: 160}
      children:
        - type: image
          id: logo_american_integrity
          label: AMERICAN INTEGRITY
          bounds: {x: 15, y: 65, width: 120, height: 40}
        - type: navigation
          id: main_nav
          bounds: {x: 1250, y: 70, width: 400, height: 30}
          children:
            - type: link
              id: nav_home
              label: Home
              bounds: {x: 1250, y: 70, width: 40, height: 20}
            - type: link
              id: nav_quote_policy
              label: Quote/Policy
              bounds: {x: 1300, y: 70, width: 80, height: 20}
              state: active
            - type: link
              id: nav_claims
              label: Claims
              bounds: {x: 1390, y: 70, width: 50, height: 20}
            - type: link
              id: nav_cabinets
              label: Cabinets
              bounds: {x: 1450, y: 70, width: 60, height: 20}
            - type: link
              id: nav_support
              label: Support
              bounds: {x: 1520, y: 70, width: 60, height: 20}
        - type: input
          id: input_search
          label: null
          bounds: {x: 15, y: 110, width: 170, height: 30}
          value: Search
        - type: button
          id: btn_search
          label: null
          bounds: {x: 185, y: 110, width: 30, height: 30}
        - type: text
          id: text_advanced_search
          label: 'ADVANCED SEARCH:'
          bounds: {x: 15, y: 145, width: 100, height: 20}
        - type: link
          id: link_policy_search
          label: POLICY
          bounds: {x: 120, y: 145, width: 40, height: 20}
        - type: link
          id: link_claims_search
          label: CLAIMS
          bounds: {x: 170, y: 145, width: 45, height: 20}
        - type: container
          id: quote_info_bar
          bounds: {x: 230, y: 110, width: 1680, height: 50}
          children:
            - type: text
              id: text_quote_label
              label: QUOTE
              bounds: {x: 240, y: 115, width: 40, height: 20}
            - type: text
              id: text_quote_number
              label: 'Quote Number ***********'
              bounds: {x: 320, y: 115, width: 150, height: 20}
            - type: text
              id: text_insured
              label: Insured Landon Cassidy
              bounds: {x: 480, y: 115, width: 150, height: 20}
            - type: text
              id: text_product
              label: Product Voluntary Homeowners (HO3)
              bounds: {x: 640, y: 115, width: 200, height: 20}
            - type: text
              id: text_sub_type
              label: Sub Type HO3
              bounds: {x: 850, y: 115, width: 80, height: 20}
            - type: text
              id: text_policy_term
              label: Policy Term 06/20/2025 - 06/20/2026
              bounds: {x: 940, y: 115, width: 200, height: 20}
            - type: text
              id: text_producer
              label: Producer HH Insurance Group, LLC
              bounds: {x: 1150, y: 115, width: 200, height: 20}
            - type: text
              id: text_status
              label: Status In Process
              bounds: {x: 1360, y: 115, width: 100, height: 20}
            - type: text
              id: text_premium
              label: Premium + Fees $17,776.90
              bounds: {x: 1470, y: 115, width: 120, height: 20}
  sidebar:
    - type: container
      id: left_sidebar
      bounds: {x: 0, y: 160, width: 220, height: 750}
      children:
        - type: text
          id: text_quote_title
          label: Quote
          bounds: {x: 15, y: 170, width: 190, height: 20}
        - type: navigation
          id: quote_nav
          bounds: {x: 15, y: 200, width: 190, height: 300}
          children:
            - type: link
              id: nav_policy
              label: Policy
              bounds: {x: 15, y: 200, width: 190, height: 30}
              state: active
            - type: link
              id: nav_dwelling
              label: Dwelling
              bounds: {x: 15, y: 230, width: 190, height: 30}
            - type: badge
              id: badge_dwelling
              label: '2'
              bounds: {x: 190, y: 235, width: 15, height: 15}
            - type: link
              id: nav_review
              label: Review
              bounds: {x: 15, y: 260, width: 190, height: 30}
            - type: link
              id: nav_attachments
              label: Attachments
              bounds: {x: 15, y: 290, width: 190, height: 30}
            - type: link
              id: nav_correspondence
              label: Correspondence
              bounds: {x: 15, y: 320, width: 190, height: 30}
            - type: link
              id: nav_tasks
              label: Tasks
              bounds: {x: 15, y: 350, width: 190, height: 30}
            - type: link
              id: nav_notes
              label: Notes
              bounds: {x: 15, y: 380, width: 190, height: 30}
            - type: link
              id: nav_policy_file
              label: Policy File
              bounds: {x: 15, y: 410, width: 190, height: 30}
    - type: container
      id: right_sidebar
      bounds: {x: 1870, y: 160, width: 50, height: 750}
      children:
        - type: button
          id: btn_summary
          label: SUMMARY
          bounds: {x: 1875, y: 165, width: 40, height: 50}
        - type: button
          id: btn_quick_qt
          label: WTBCBEF QUICK QT
          bounds: {x: 1875, y: 220, width: 40, height: 50}
        - type: button
          id: btn_new_quote
          label: NEW QUOTE
          bounds: {x: 1875, y: 275, width: 40, height: 50}
        - type: button
          id: btn_new_note
          label: NEW NOTE
          bounds: {x: 1875, y: 330, width: 40, height: 50}
        - type: button
          id: btn_new_attach
          label: NEW ATTACH...
          bounds: {x: 1875, y: 385, width: 40, height: 50}
        - type: button
          id: btn_new_task
          label: NEW TASK
          bounds: {x: 1875, y: 440, width: 40, height: 50}
  main_content:
    - type: container
      id: main_content_container
      bounds: {x: 220, y: 160, width: 1650, height: 750}
      children:
        - type: button
          id: btn_return_to_home
          label: < Return to Home
          bounds: {x: 240, y: 170, width: 120, height: 30}
        - type: container
          id: action_buttons_container
          bounds: {x: 1100, y: 170, width: 750, height: 30}
          children:
            - type: button
              id: btn_next_page_header
              label: NEXT PAGE
              bounds: {x: 1100, y: 170, width: 100, height: 30}
            - type: button
              id: btn_save
              label: SAVE
              bounds: {x: 1210, y: 170, width: 70, height: 30}
            - type: button
              id: btn_print
              label: PRINT
              bounds: {x: 1290, y: 170, width: 70, height: 30}
            - type: button
              id: btn_create_application
              label: CREATE APPLICATION
              bounds: {x: 1370, y: 170, width: 150, height: 30}
            - type: button
              id: btn_discard_changes
              label: DISCARD CHANGES
              bounds: {x: 1530, y: 170, width: 140, height: 30}
            - type: button
              id: btn_view_notes
              label: VIEW NOTES
              bounds: {x: 1680, y: 170, width: 100, height: 30}
            - type: button
              id: btn_delete
              label: DELETE
              bounds: {x: 1790, y: 170, width: 70, height: 30}
            - type: button
              id: btn_more
              label: '... MORE'
              bounds: {x: 1870, y: 170, width: 70, height: 30}
        - type: form
          id: policy_form
          bounds: {x: 240, y: 210, width: 1610, height: 650}
          children:
            - type: section
              id: section_prior_carrier
              label: Prior Carrier Details
              bounds: {x: 240, y: 220, width: 1610, height: 80}
              children:
                - type: dropdown
                  id: dropdown_prior_carrier
                  label: Prior Carrier*
                  bounds: {x: 280, y: 280, width: 200, height: 30}
                  value: New Purchase
                - type: input
                  id: input_prior_policy_expiration
                  label: Prior Policy Expiration Date
                  bounds: {x: 600, y: 280, width: 200, height: 30}
                  value: ''
            - type: section
              id: section_insured_info
              label: Insured Information
              bounds: {x: 240, y: 310, width: 1610, height: 200}
              children:
                - type: dropdown
                  id: dropdown_entity_type
                  label: Entity Type*
                  bounds: {x: 380, y: 350, width: 200, height: 30}
                  value: Individual
                - type: input
                  id: input_first_name
                  label: First*
                  bounds: {x: 380, y: 410, width: 150, height: 30}
                  value: Landon
                - type: input
                  id: input_middle_name
                  label: Middle
                  bounds: {x: 540, y: 410, width: 150, height: 30}
                  value: ''
                - type: input
                  id: input_last_name
                  label: Last*
                  bounds: {x: 700, y: 410, width: 150, height: 30}
                  value: Cassidy
                - type: input
                  id: input_suffix
                  label: Suffix
                  bounds: {x: 860, y: 410, width: 100, height: 30}
                  value: ''
                - type: input
                  id: input_dob
                  label: DOB*
                  bounds: {x: 280, y: 460, width: 150, height: 30}
                  value: 05/20/1998
                - type: dropdown
                  id: dropdown_insurance_score
                  label: Insurance Score*
                  bounds: {x: 440, y: 460, width: 200, height: 30}
                  value: Excellent (850-899)
                - type: input
                  id: input_search_name
                  label: Search Name*
                  bounds: {x: 280, y: 490, width: 200, height: 30}
                  value: Landon Cassidy
                - type: link
                  id: link_reset
                  label: Reset
                  bounds: {x: 490, y: 490, width: 40, height: 20}
                - type: dropdown
                  id: dropdown_primary_phone
                  label: Primary Phone
                  bounds: {x: 280, y: 520, width: 200, height: 30}
                  value: Select...
                - type: input
                  id: input_email
                  label: Email
                  bounds: {x: 280, y: 550, width: 200, height: 30}
                  value: ''
                - type: checkbox
                  id: checkbox_no_email
                  label: No Email
                  bounds: {x: 490, y: 550, width: 100, height: 20}
            - type: section
              id: section_dwelling_info
              label: Dwelling Information
              bounds: {x: 240, y: 580, width: 1610, height: 200}
              children:
                - type: text
                  id: text_lookup_address
                  label: Lookup Address
                  bounds: {x: 280, y: 620, width: 100, height: 20}
                - type: checkbox
                  id: checkbox_ignore_address_validation
                  label: Ignore Address Validation
                  bounds: {x: 880, y: 600, width: 180, height: 20}
                - type: input
                  id: input_dwelling_number
                  label: Number*
                  bounds: {x: 340, y: 650, width: 100, height: 30}
                  value: '4227'
                - type: dropdown
                  id: dropdown_dwelling_direction
                  label: Direction
                  bounds: {x: 450, y: 650, width: 80, height: 30}
                  value: S
                - type: input
                  id: input_dwelling_street
                  label: Street*
                  bounds: {x: 540, y: 650, width: 150, height: 30}
                  value: 5th
                - type: dropdown
                  id: dropdown_dwelling_suffix
                  label: Suffix
                  bounds: {x: 700, y: 650, width: 80, height: 30}
                  value: Ave
                - type: input
                  id: input_dwelling_post_dir
                  label: Post Dir
                  bounds: {x: 790, y: 650, width: 80, height: 30}
                  value: ''
                - type: dropdown
                  id: dropdown_dwelling_type
                  label: Type
                  bounds: {x: 880, y: 650, width: 80, height: 30}
                  value: ''
                - type: input
                  id: input_dwelling_unit_number
                  label: Number
                  bounds: {x: 970, y: 650, width: 80, height: 30}
                  value: ''
                - type: input
                  id: input_dwelling_city
                  label: City*
                  bounds: {x: 280, y: 690, width: 120, height: 30}
                  value: St Petersburg
                - type: dropdown
                  id: dropdown_dwelling_county
                  label: County*
                  bounds: {x: 410, y: 690, width: 120, height: 30}
                  value: Pinellas
                - type: dropdown
                  id: dropdown_dwelling_state
                  label: State*
                  bounds: {x: 540, y: 690, width: 120, height: 30}
                  value: Florida
                - type: input
                  id: input_dwelling_zip
                  label: Zip*
                  bounds: {x: 670, y: 690, width: 120, height: 30}
                  value: 33711-1522
                - type: text
                  id: text_address_verified
                  label: Address Verified
                  bounds: {x: 700, y: 720, width: 100, height: 20}
                - type: link
                  id: link_view_map
                  label: View Map
                  bounds: {x: 800, y: 720, width: 60, height: 20}
                - type: input
                  id: input_latitude
                  label: Latitude*
                  bounds: {x: 280, y: 740, width: 120, height: 30}
                  value: '27.766685'
                - type: input
                  id: input_longitude
                  label: Longitude*
                  bounds: {x: 410, y: 740, width: 120, height: 30}
                  value: '-82.690887'
                - type: dropdown
                  id: dropdown_construction_type
                  label: Construction Type*
                  bounds: {x: 280, y: 780, width: 150, height: 30}
                  value: Masonry
                - type: dropdown
                  id: dropdown_occupancy
                  label: Occupancy*
                  bounds: {x: 440, y: 780, width: 150, height: 30}
                  value: Owner Occupied
                - type: dropdown
                  id: dropdown_months_occupied
                  label: Months Occupied*
                  bounds: {x: 600, y: 780, width: 150, height: 30}
                  value: 0 to 12 Months
            - type: section
              id: section_resided_less_than_2_years
              label: Has the insured resided at the risk address for less than 2 years?*
              bounds: {x: 240, y: 820, width: 1610, height: 40}
              children:
                - type: dropdown
                  id: dropdown_resided_less_than_2_years
                  label: null
                  bounds: {x: 650, y: 820, width: 80, height: 30}
                  value: 'Yes'
            - type: section
              id: section_prior_address
              label: Prior Address
              bounds: {x: 240, y: 860, width: 1610, height: 120}
              children:
                - type: input
                  id: input_prior_address_number
                  label: Number*
                  bounds: {x: 340, y: 890, width: 100, height: 30}
                  value: '18001'
                - type: dropdown
                  id: dropdown_prior_address_direction
                  label: Direction
                  bounds: {x: 450, y: 890, width: 80, height: 30}
                  value: ''
                - type: input
                  id: input_prior_address_street
                  label: Street*
                  bounds: {x: 540, y: 890, width: 150, height: 30}
                  value: Avalon
                - type: dropdown
                  id: dropdown_prior_address_suffix
                  label: Suffix
                  bounds: {x: 700, y: 890, width: 80, height: 30}
                  value: Ln
                - type: input
                  id: input_prior_address_post_dir
                  label: Post Dir
                  bounds: {x: 790, y: 890, width: 80, height: 30}
                  value: ''
                - type: dropdown
                  id: dropdown_prior_address_type
                  label: Type
                  bounds: {x: 880, y: 890, width: 80, height: 30}
                  value: ''
                - type: input
                  id: input_prior_address_unit_number
                  label: Number
                  bounds: {x: 970, y: 890, width: 80, height: 30}
                  value: ''
                - type: input
                  id: input_prior_address_city
                  label: City*
                  bounds: {x: 280, y: 930, width: 120, height: 30}
                  value: Tampa
                - type: dropdown
                  id: dropdown_prior_address_county
                  label: County*
                  bounds: {x: 410, y: 930, width: 120, height: 30}
                  value: Hillsborough
                - type: dropdown
                  id: dropdown_prior_address_state
                  label: State*
                  bounds: {x: 540, y: 930, width: 120, height: 30}
                  value: Florida
                - type: input
                  id: input_prior_address_zip
                  label: Zip*
                  bounds: {x: 670, y: 930, width: 120, height: 30}
                  value: 33647-3102
                - type: button
                  id: btn_verify_address
                  label: Verify Address
                  bounds: {x: 800, y: 930, width: 100, height: 30}
                - type: text
                  id: text_prior_address_verified
                  label: Address Verified
                  bounds: {x: 800, y: 960, width: 100, height: 20}
                - type: link
                  id: link_prior_address_view_map
                  label: View Map
                  bounds: {x: 900, y: 960, width: 60, height: 20}
        - type: button
          id: btn_next_page_footer
          label: Next Page
          bounds: {x: 240, y: 890, width: 90, height: 30}
  footer:
    - type: container
      id: footer_container
      bounds: {x: 0, y: 910, width: 1920, height: 40}
      children:
        - type: image
          id: logo_guidewire
          label: Powered by GUIDEWIRE
          bounds: {x: 400, y: 915, width: 150, height: 20}
        - type: text
          id: text_environment
          label: 'Environment : PROD AIIG'
          bounds: {x: 900, y: 915, width: 150, height: 20}
        - type: text
          id: text_current_logon
          label: 'Current Logon : AG8529('
          bounds: {x: 1060, y: 915, width: 150, height: 20}
        - type: link
          id: link_sign_out
          label: Sign Out
          bounds: {x: 1210, y: 915, width: 60, height: 20}
        - type: text
          id: text_closing_paren
          label: )
          bounds: {x: 1270, y: 915, width: 5, height: 20}
        - type: text
          id: text_posting_date
          label: 'Posting Date : 05/23/2025'
          bounds: {x: 1285, y: 915, width: 150, height: 20}