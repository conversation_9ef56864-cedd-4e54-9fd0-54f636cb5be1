browser_component:
  tab_title: *********** - Guidewire PolicyCenter
  url: ai.iscs.com/innovation
  address_bar_focused: false
  tab_state: idle
webpage:
  header:
  - component_type: container
    id: main_header
    bounds:
      x: 0
      y: 69
      width: 1920
      height: 61
    subcomponents:
    - component_type: image
      id: logo_american_integrity
      label: AMERICAN INTEGRITY
      bounds:
        x: 16
        y: 116
        width: 128
        height: 40
    - component_type: navigation_menu
      id: top_navigation
      bounds:
        x: 1290
        y: 116
        width: 600
        height: 36
      subcomponents:
      - component_type: link
        id: link_home
        label: Home
        bounds:
          x: 1298
          y: 126
          width: 40
          height: 20
      - component_type: link
        id: link_quote_policy
        label: Quote/Policy
        state: active
        bounds:
          x: 1362
          y: 126
          width: 80
          height: 20
      - component_type: link
        id: link_claims
        label: Claims
        bounds:
          x: 1466
          y: 126
          width: 48
          height: 20
      - component_type: link
        id: link_cabinets
        label: Cabinets
        bounds:
          x: 1538
          y: 126
          width: 58
          height: 20
      - component_type: link
        id: link_support
        label: Support
        bounds:
          x: 1620
          y: 126
          width: 54
          height: 20
  sidebar:
  - component_type: navigation_menu
    id: left_navigation
    bounds:
      x: 0
      y: 169
      width: 256
      height: 809
    subcomponents:
    - component_type: search_input
      id: search_sidebar
      label: Search
      bounds:
        x: 16
        y: 180
        width: 188
        height: 36
    - component_type: button_icon
      id: btn_search
      label: Search
      bounds:
        x: 204
        y: 180
        width: 36
        height: 36
    - component_type: text
      id: text_advanced_search
      label: 'ADVANCED SEARCH:'
      bounds:
        x: 16
        y: 224
        width: 100
        height: 16
    - component_type: link
      id: link_policy_search
      label: POLICY
      bounds:
        x: 120
        y: 224
        width: 40
        height: 16
    - component_type: link
      id: link_claims_search
      label: CLAIMS
      bounds:
        x: 170
        y: 224
        width: 45
        height: 16
    - component_type: dropdown
      id: dropdown_quote
      label: Quote
      state: expanded
      bounds:
        x: 16
        y: 252
        width: 224
        height: 32
    - component_type: link
      id: link_policy
      label: Policy
      state: active
      bounds:
        x: 32
        y: 284
        width: 208
        height: 32
    - component_type: link
      id: link_dwelling
      label: Dwelling
      bounds:
        x: 32
        y: 316
        width: 208
        height: 32
      subcomponents:
      - component_type: badge
        id: badge_dwelling_count
        label: '2'
        bounds:
          x: 210
          y: 322
          width: 16
          height: 20
    - component_type: link
      id: link_review
      label: Review
      bounds:
        x: 32
        y: 348
        width: 208
        height: 32
    - component_type: link
      id: link_attachments
      label: Attachments
      bounds:
        x: 16
        y: 380
        width: 224
        height: 32
    - component_type: link
      id: link_correspondence
      label: Correspondence
      bounds:
        x: 16
        y: 412
        width: 224
        height: 32
    - component_type: link
      id: link_tasks
      label: Tasks
      bounds:
        x: 16
        y: 444
        width: 224
        height: 32
    - component_type: link
      id: link_notes
      label: Notes
      bounds:
        x: 16
        y: 476
        width: 224
        height: 32
    - component_type: link
      id: link_policy_file
      label: Policy File
      bounds:
        x: 16
        y: 508
        width: 224
        height: 32
  - component_type: action_bar
    id: right_action_bar
    bounds:
      x: 1888
      y: 169
      width: 32
      height: 809
    subcomponents:
    - component_type: button_icon
      id: btn_summary
      label: SUMMARY
      bounds:
        x: 1888
        y: 180
        width: 32
        height: 50
    - component_type: button_icon
      id: btn_wtcroft_quick_qt
      label: WTCROFT QUICK QT
      bounds:
        x: 1888
        y: 230
        width: 32
        height: 50
    - component_type: button_icon
      id: btn_new_quote
      label: NEW QUOTE
      bounds:
        x: 1888
        y: 280
        width: 32
        height: 50
    - component_type: button_icon
      id: btn_new_note
      label: NEW NOTE
      bounds:
        x: 1888
        y: 330
        width: 32
        height: 50
    - component_type: button_icon
      id: btn_new_attach
      label: NEW ATTACH...
      bounds:
        x: 1888
        y: 380
        width: 32
        height: 50
    - component_type: button_icon
      id: btn_new_task
      label: NEW TASK
      bounds:
        x: 1888
        y: 430
        width: 32
        height: 50
  main_content:
  - component_type: container
    id: quote_summary_header
    bounds:
      x: 256
      y: 169
      width: 1632
      height: 80
    subcomponents:
    - component_type: button
      id: btn_quote
      label: QUOTE
      bounds:
        x: 280
        y: 180
        width: 80
        height: 36
    - component_type: text
      id: text_quote_number_label
      label: Quote Number
      bounds:
        x: 376
        y: 180
        width: 80
        height: 16
    - component_type: text
      id: text_quote_number_value
      label: ***********
      bounds:
        x: 376
        y: 198
        width: 80
        height: 16
    - component_type: text
      id: text_insured_label
      label: Insured
      bounds:
        x: 472
        y: 180
        width: 80
        height: 16
    - component_type: link
      id: link_insured_name
      label: Landon Cassidy
      bounds:
        x: 472
        y: 198
        width: 90
        height: 16
    - component_type: text
      id: text_product_label
      label: Product
      bounds:
        x: 578
        y: 180
        width: 150
        height: 16
    - component_type: text
      id: text_product_value
      label: Voluntary Homeowners (HO3)
      bounds:
        x: 578
        y: 198
        width: 150
        height: 16
    - component_type: text
      id: text_sub_type_label
      label: Sub Type
      bounds:
        x: 744
        y: 180
        width: 50
        height: 16
    - component_type: text
      id: text_sub_type_value
      label: HO3
      bounds:
        x: 744
        y: 198
        width: 30
        height: 16
    - component_type: text
      id: text_policy_term_label
      label: Policy Term
      bounds:
        x: 810
        y: 180
        width: 150
        height: 16
    - component_type: text
      id: text_policy_term_value
      label: 06/20/2025 - 06/20/2026
      bounds:
        x: 810
        y: 198
        width: 150
        height: 16
    - component_type: text
      id: text_producer_label
      label: Producer
      bounds:
        x: 976
        y: 180
        width: 120
        height: 16
    - component_type: link
      id: link_producer_name
      label: HH Insurance Group, LLC
      bounds:
        x: 976
        y: 198
        width: 120
        height: 16
    - component_type: text
      id: text_status_label
      label: Status
      bounds:
        x: 1112
        y: 180
        width: 60
        height: 16
    - component_type: text
      id: text_status_value
      label: In Process
      bounds:
        x: 1112
        y: 198
        width: 60
        height: 16
    - component_type: text
      id: text_premium_fees_label
      label: Premium + Fees
      bounds:
        x: 1188
        y: 180
        width: 90
        height: 16
    - component_type: text
      id: text_premium_fees_value
      label: $17,776.90
      bounds:
        x: 1188
        y: 198
        width: 70
        height: 16
  - component_type: container
    id: policy_general_form
    bounds:
      x: 256
      y: 249
      width: 1632
      height: 831
    subcomponents:
    - component_type: link
      id: link_return_to_home
      label: < Return to Home
      bounds:
        x: 280
        y: 260
        width: 100
        height: 20
    - component_type: button
      id: btn_next_page
      label: NEXT PAGE
      bounds:
        x: 1290
        y: 220
        width: 90
        height: 28
    - component_type: button
      id: btn_save
      label: SAVE
      bounds:
        x: 1390
        y: 220
        width: 60
        height: 28
    - component_type: button
      id: btn_copy
      label: COPY
      bounds:
        x: 1460
        y: 220
        width: 60
        height: 28
    - component_type: button
      id: btn_print
      label: PRINT
      bounds:
        x: 1530
        y: 220
        width: 60
        height: 28
    - component_type: button
      id: btn_create_application
      label: CREATE APPLICATION
      bounds:
        x: 1600
        y: 220
        width: 150
        height: 28
    - component_type: button
      id: btn_discard_changes
      label: DISCARD CHANGES
      bounds:
        x: 1760
        y: 220
        width: 130
        height: 28
    - component_type: button
      id: btn_view_notes
      label: VIEW NOTES
      bounds:
        x: 1290
        y: 250
        width: 100
        height: 28
    - component_type: button
      id: btn_delete
      label: DELETE
      bounds:
        x: 1400
        y: 250
        width: 70
        height: 28
    - component_type: button
      id: btn_more
      label: '... MORE'
      bounds:
        x: 1480
        y: 250
        width: 70
        height: 28
    - component_type: text
      id: title_policy_general
      label: Policy General
      bounds:
        x: 280
        y: 292
        width: 150
        height: 20
    - component_type: dropdown
      id: dropdown_product
      label: Product*
      value: Florida - Voluntary Homeowners (HO3) - American Integrity Insurance Group
      bounds:
        x: 280
        y: 324
        width: 400
        height: 36
    - component_type: input
      id: input_effective_date
      label: Effective Date*
      value: 06/20/2025
      bounds:
        x: 280
        y: 376
        width: 150
        height: 36
    - component_type: input
      id: input_producer_code
      label: 'Producer: Code*'
      value: AG8529A1
      bounds:
        x: 280
        y: 428
        width: 150
        height: 36
    - component_type: text
      id: text_producer_name
      label: HH Insurance Group, LLC
      bounds:
        x: 470
        y: 438
        width: 150
        height: 20
    - component_type: text
      id: title_prior_carrier
      label: Prior Carrier Details
      bounds:
        x: 280
        y: 480
        width: 150
        height: 20
    - component_type: dropdown
      id: dropdown_prior_carrier
      label: Prior Carrier*
      value: New Purchase
      bounds:
        x: 280
        y: 512
        width: 200
        height: 36
    - component_type: input
      id: input_prior_policy_expiration
      label: Prior Policy Expiration Date
      bounds:
        x: 700
        y: 512
        width: 200
        height: 36
    - component_type: text
      id: title_insured_info
      label: Insured Information
      bounds:
        x: 280
        y: 564
        width: 150
        height: 20
    - component_type: dropdown
      id: dropdown_entity_type
      label: Entity Type*
      value: Individual
      bounds:
        x: 280
        y: 596
        width: 200
        height: 36
    - component_type: input
      id: input_first_name
      label: First*
      value: Landon
      bounds:
        x: 500
        y: 596
        width: 150
        height: 36
    - component_type: input
      id: input_middle_name
      label: Middle
      bounds:
        x: 670
        y: 596
        width: 150
        height: 36
    - component_type: input
      id: input_last_name
      label: Last*
      value: Cassidy
      bounds:
        x: 840
        y: 596
        width: 150
        height: 36
    - component_type: input
      id: input_suffix
      label: Suffix
      bounds:
        x: 1010
        y: 596
        width: 100
        height: 36
    - component_type: input
      id: input_dob
      label: DOB*
      value: 05/20/1998
      bounds:
        x: 280
        y: 648
        width: 150
        height: 36
    - component_type: dropdown
      id: dropdown_insurance_score
      label: Insurance Score*
      value: Excellent (850-999)
      bounds:
        x: 500
        y: 648
        width: 200
        height: 36
    - component_type: input
      id: input_search_name
      label: Search Name*
      value: Landon Cassidy
      bounds:
        x: 280
        y: 700
        width: 200
        height: 36
    - component_type: link
      id: link_reset
      label: Reset
      bounds:
        x: 490
        y: 708
        width: 40
        height: 20
    - component_type: dropdown
      id: dropdown_primary_phone
      label: Primary Phone
      value: Select...
      bounds:
        x: 280
        y: 752
        width: 200
        height: 36
    - component_type: input
      id: input_email
      label: Email
      bounds:
        x: 500
        y: 752
        width: 200
        height: 36
    - component_type: checkbox
      id: checkbox_no_email
      label: No Email
      bounds:
        x: 720
        y: 760
        width: 80
        height: 20
    - component_type: text
      id: title_dwelling_info
      label: Dwelling Information
      bounds:
        x: 280
        y: 804
        width: 150
        height: 20
    - component_type: input
      id: input_lookup_address
      label: Lookup Address
      bounds:
        x: 280
        y: 836
        width: 200
        height: 36
    - component_type: input
      id: input_number
      label: Number*
      value: '4227'
      bounds:
        x: 500
        y: 836
        width: 80
        height: 36
    - component_type: input
      id: input_direction
      label: Direction
      bounds:
        x: 600
        y: 836
        width: 80
        height: 36
    - component_type: input
      id: input_street
      label: Street*
      value: 5th
      bounds:
        x: 700
        y: 836
        width: 100
        height: 36
    - component_type: dropdown
      id: dropdown_suffix
      label: Suffix
      value: Ave
      bounds:
        x: 820
        y: 836
        width: 80
        height: 36
    - component_type: input
      id: input_post_dir
      label: Post Dir
      value: S
      bounds:
        x: 920
        y: 836
        width: 80
        height: 36
    - component_type: dropdown
      id: dropdown_type
      label: Type
      bounds:
        x: 1020
        y: 836
        width: 80
        height: 36
    - component_type: checkbox
      id: checkbox_ignore_address_validation
      label: Ignore Address Validation
      bounds:
        x: 1120
        y: 820
        width: 150
        height: 20
    - component_type: input
      id: input_number_2
      label: Number
      bounds:
        x: 1120
        y: 836
        width: 80
        height: 36
    - component_type: input
      id: input_city
      label: City*
      value: St Petersburg
      bounds:
        x: 280
        y: 888
        width: 150
        height: 36
    - component_type: dropdown
      id: dropdown_county
      label: County*
      value: Pinellas
      bounds:
        x: 450
        y: 888
        width: 150
        height: 36
    - component_type: dropdown
      id: dropdown_state
      label: State*
      value: Florida
      bounds:
        x: 620
        y: 888
        width: 150
        height: 36
    - component_type: input
      id: input_zip
      label: Zip*
      value: 33711-1522
      bounds:
        x: 790
        y: 888
        width: 100
        height: 36
    - component_type: text
      id: text_address_verified
      label: Address Verified
      bounds:
        x: 910
        y: 896
        width: 100
        height: 20
    - component_type: link
      id: link_view_map
      label: View Map
      bounds:
        x: 1020
        y: 896
        width: 60
        height: 20
    - component_type: input
      id: input_latitude
      label: Latitude*
      value: '27.766685'
      bounds:
        x: 280
        y: 940
        width: 150
        height: 36
    - component_type: input
      id: input_longitude
      label: Longitude*
      value: "-82.690887"
      bounds:
        x: 450
        y: 940
        width: 150
        height: 36
    - component_type: dropdown
      id: dropdown_construction_type
      label: Construction Type*
      value: Masonry
      bounds:
        x: 280
        y: 992
        width: 150
        height: 36
    - component_type: dropdown
      id: dropdown_occupancy
      label: Occupancy*
      value: Owner Occupied
      bounds:
        x: 450
        y: 992
        width: 150
        height: 36
    - component_type: dropdown
      id: dropdown_months_occupied
      label: Months Occupied*
      value: 9 to 12 Months
      bounds:
        x: 620
        y: 992
        width: 150
        height: 36
    - component_type: dropdown
      id: dropdown_resided_less_than_2_years
      label: Has the Insured resided at the risk address for less than 2 years?*
      value: 'Yes'
      bounds:
        x: 280
        y: 1044
        width: 150
        height: 36
    - component_type: text
      id: title_prior_address
      label: Prior Address
      bounds:
        x: 280
        y: 1096
        width: 100
        height: 20
    - component_type: input
      id: input_prior_address_number
      label: Number*
      value: '18001'
      bounds:
        x: 280
        y: 1128
        width: 80
        height: 36
    - component_type: input
      id: input_prior_address_direction
      label: Direction
      bounds:
        x: 380
        y: 1128
        width: 80
        height: 36
    - component_type: input
      id: input_prior_address_street
      label: Street*
      value: Avalon
      bounds:
        x: 480
        y: 1128
        width: 100
        height: 36
    - component_type: dropdown
      id: dropdown_prior_address_suffix
      label: Suffix
      value: Ln
      bounds:
        x: 600
        y: 1128
        width: 80
        height: 36
    - component_type: input
      id: input_prior_address_post_dir
      label: Post Dir
      bounds:
        x: 700
        y: 1128
        width: 80
        height: 36
    - component_type: dropdown
      id: dropdown_prior_address_type
      label: Type
      bounds:
        x: 800
        y: 1128
        width: 80
        height: 36
    - component_type: input
      id: input_prior_address_number_2
      label: Number
      bounds:
        x: 900
        y: 1128
        width: 80
        height: 36
    - component_type: link
      id: link_verify_address
      label: Verify
      state: hovered
      bounds:
        x: 910
        y: 1188
        width: 80
        height: 20
    - component_type: text
      id: text_prior_address_verified
      label: Address
      bounds:
        x: 1000
        y: 1188
        width: 100
        height: 20
  footer:
  - component_type: container
    id: main_footer
    bounds:
      x: 0
      y: 1050
      width: 1920
      height: 30
    subcomponents:
    - component_type: text
      id: text_posting_date
      label: 5/29/2025
      bounds:
        x: 1650
        y: 1058
        width: 150
        height: 20