"""Test script for the multi-agent workflow."""

import asyncio
import logging
import httpx
from typing import Dict, Any

from config import AGENT_URLS


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("workflow_tester")


class WorkflowTester:
    """Test the multi-agent workflow."""
    
    def __init__(self):
        self.client = httpx.AsyncClient(timeout=300.0)
    
    async def test_agent_health(self, agent_name: str, agent_url: str) -> bool:
        """Test if an agent is healthy and responding."""
        try:
            response = await self.client.get(agent_url)
            response.raise_for_status()
            
            agent_card = response.json()
            logger.info(f"✓ {agent_name} is healthy - {agent_card.get('description', '')}")
            return True
            
        except Exception as e:
            logger.error(f"✗ {agent_name} health check failed: {str(e)}")
            return False
    
    async def test_all_agents_health(self) -> bool:
        """Test health of all agents."""
        logger.info("Testing agent health...")
        
        all_healthy = True
        for agent_name, agent_url in AGENT_URLS.items():
            healthy = await self.test_agent_health(agent_name, agent_url)
            all_healthy = all_healthy and healthy
        
        return all_healthy
    
    async def test_salesforce_agent(self) -> Dict[str, Any]:
        """Test Salesforce agent functionality."""
        logger.info("Testing Salesforce agent...")
        
        task_request = {
            "id": "test_salesforce",
            "message": {"role": "user", "parts": [{"text": "test"}]},
            "context": {
                "action": "get_household_data",
                "task_id": "08/19"
            }
        }
        
        try:
            response = await self.client.post(
                f"{AGENT_URLS['salesforce']}/tasks",
                json=task_request
            )
            response.raise_for_status()
            
            result = response.json()
            logger.info(f"✓ Salesforce agent test completed: {result.get('status')}")
            return result
            
        except Exception as e:
            logger.error(f"✗ Salesforce agent test failed: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    async def test_document_agent(self) -> Dict[str, Any]:
        """Test Document agent functionality."""
        logger.info("Testing Document agent...")
        
        task_request = {
            "id": "test_document",
            "message": {"role": "user", "parts": [{"text": "test"}]},
            "context": {
                "action": "extract_quote_number"
            }
        }
        
        try:
            response = await self.client.post(
                f"{AGENT_URLS['document']}/tasks",
                json=task_request
            )
            response.raise_for_status()
            
            result = response.json()
            logger.info(f"✓ Document agent test completed: {result.get('status')}")
            return result
            
        except Exception as e:
            logger.error(f"✗ Document agent test failed: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    async def test_american_integrity_agent(self, quote_number: str, salesforce_data: Dict[str, Any]) -> Dict[str, Any]:
        """Test American Integrity agent functionality."""
        logger.info("Testing American Integrity agent...")
        
        task_request = {
            "id": "test_american_integrity",
            "message": {"role": "user", "parts": [{"text": "test"}]},
            "context": {
                "action": "fill_underwriting_form",
                "quote_number": quote_number,
                "salesforce_data": salesforce_data
            }
        }
        
        try:
            response = await self.client.post(
                f"{AGENT_URLS['american_integrity']}/tasks",
                json=task_request
            )
            response.raise_for_status()
            
            result = response.json()
            logger.info(f"✓ American Integrity agent test completed: {result.get('status')}")
            return result
            
        except Exception as e:
            logger.error(f"✗ American Integrity agent test failed: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    async def test_planner_agent(self) -> Dict[str, Any]:
        """Test Planner agent orchestration."""
        logger.info("Testing Planner agent orchestration...")
        
        task_request = {
            "id": "test_workflow",
            "message": {"role": "user", "parts": [{"text": "08/19"}]},
            "context": {}
        }
        
        try:
            response = await self.client.post(
                f"{AGENT_URLS['planner']}/tasks",
                json=task_request
            )
            response.raise_for_status()
            
            result = response.json()
            logger.info(f"✓ Planner agent test completed: {result.get('status')}")
            return result
            
        except Exception as e:
            logger.error(f"✗ Planner agent test failed: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    async def run_full_workflow_test(self):
        """Run the complete workflow test."""
        logger.info("Starting full workflow test...")
        
        # Step 1: Check agent health
        if not await self.test_all_agents_health():
            logger.error("Some agents are not healthy. Aborting workflow test.")
            return
        
        # Step 2: Test individual agents
        logger.info("\n" + "="*50)
        logger.info("Testing individual agents...")
        
        # Test Salesforce agent
        salesforce_result = await self.test_salesforce_agent()
        
        # Test Document agent
        document_result = await self.test_document_agent()
        
        # Test American Integrity agent (with mock data)
        mock_salesforce_data = {"felony": False, "foreclosure": False}
        mock_quote_number = document_result.get("data", {}).get("quote_number", "TEST-12345")
        
        ai_result = await self.test_american_integrity_agent(mock_quote_number, mock_salesforce_data)
        
        # Step 3: Test full orchestration
        logger.info("\n" + "="*50)
        logger.info("Testing full workflow orchestration...")
        
        planner_result = await self.test_planner_agent()
        
        # Summary
        logger.info("\n" + "="*50)
        logger.info("WORKFLOW TEST SUMMARY")
        logger.info("="*50)
        logger.info(f"Salesforce Agent: {'✓' if salesforce_result.get('status') == 'success' else '✗'}")
        logger.info(f"Document Agent: {'✓' if document_result.get('status') == 'success' else '✗'}")
        logger.info(f"American Integrity Agent: {'✓' if ai_result.get('status') == 'success' else '✗'}")
        logger.info(f"Planner Agent: {'✓' if planner_result.get('status') == 'success' else '✗'}")
        
        overall_success = all([
            salesforce_result.get('status') == 'success',
            document_result.get('status') == 'success',
            ai_result.get('status') == 'success',
            planner_result.get('status') == 'success'
        ])
        
        logger.info(f"\nOverall Test Result: {'✓ PASSED' if overall_success else '✗ FAILED'}")
        
        return overall_success
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()


async def main():
    """Main test entry point."""
    tester = WorkflowTester()
    
    try:
        await tester.run_full_workflow_test()
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
    except Exception as e:
        logger.error(f"Test error: {str(e)}")
    finally:
        await tester.close()


if __name__ == "__main__":
    asyncio.run(main())
