"""American Integrity Agent - Handles American Integrity system login, search, and form filling."""

import asyncio
import logging
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from a2a.types import AgentSkill

from .base_agent import BaseAgent, TaskRequest, TaskResponse
from ..config import AMERICAN_INTEGRITY_CONFIG, MCP_CONFIG


class AmericanIntegrityAgent(BaseAgent):
    """
    American Integrity Agent handles:
    1. <PERSON><PERSON> to American Integrity system
    2. Search for quote number
    3. Fill underwriting questions form with Salesforce data
    4. Navigate through form sections
    """
    
    def __init__(self):
        skills = [
            AgentSkill(
                id="american_integrity_login",
                name="American Integrity Login",
                description="Login to American Integrity system with provided credentials",
                tags=["american-integrity", "authentication", "login"]
            ),
            AgentSkill(
                id="quote_search",
                name="Quote Search",
                description="Search for specific quote numbers in the system",
                tags=["american-integrity", "search", "quote"]
            ),
            Agent<PERSON><PERSON>(
                id="form_filling",
                name="Underwriting Form Filling",
                description="Fill underwriting questions and forms with provided data",
                tags=["american-integrity", "form-filling", "underwriting"]
            )
        ]
        
        super().__init__(
            name="AmericanIntegrityAgent",
            description="Handles American Integrity system login, search, and form filling using Playwright MCP",
            port=8004,
            skills=skills
        )
        
        self.mcp_toolset = None
    
    async def initialize(self):
        """Initialize the American Integrity agent with MCP toolset."""
        await super().initialize()
        
        # Initialize MCP toolset for Playwright
        from google.adk.tools.mcp_tool.mcp_toolset import (
            MCPToolset,
            StdioConnectionParams,
            StdioServerParameters,
        )
        
        try:
            self.mcp_toolset = MCPToolset(
                connection_params=StdioConnectionParams(
                    server_params=StdioServerParameters(
                        command=MCP_CONFIG["playwright_server_path"],
                        args=MCP_CONFIG["playwright_server_args"]
                    )
                )
            )
            # await self.mcp_toolset.initialize()
            self.logger.info("MCP Playwright toolset initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize MCP toolset: {str(e)}")
            raise
    
    async def process_task(self, request: TaskRequest) -> TaskResponse:
        """Process American Integrity-related tasks."""
        try:
            context = request.context or {}
            action = context.get("action", "")
            
            if action == "fill_underwriting_form":
                quote_number = context.get("quote_number", "")
                salesforce_data = context.get("salesforce_data", {})
                
                if not quote_number:
                    return TaskResponse(
                        id=request.id,
                        status="error",
                        error="No quote number provided"
                    )
                
                result = await self._fill_underwriting_form(quote_number, salesforce_data)
                
                return TaskResponse(
                    id=request.id,
                    status="success" if result else "error",
                    data={"form_completed": result} if result else None,
                    error="Failed to fill underwriting form" if not result else None
                )
            
            else:
                return TaskResponse(
                    id=request.id,
                    status="error",
                    error=f"Unknown action: {action}"
                )
                
        except Exception as e:
            self.logger.error(f"Error processing American Integrity task: {str(e)}")
            return TaskResponse(
                id=request.id,
                status="error",
                error=str(e)
            )
    
    async def _fill_underwriting_form(self, quote_number: str, salesforce_data: Dict[str, Any]) -> bool:
        """Fill the underwriting form with Salesforce data."""
        try:
            # Step 1: Login to American Integrity
            await self._login_to_american_integrity()
            
            # Step 2: Search for the quote
            await self._search_quote(quote_number)
            
            # Step 3: Navigate to underwriting questions
            await self._navigate_to_underwriting()
            
            # Step 4: Fill underwriting questions
            await self._fill_underwriting_questions(salesforce_data)
            
            # Step 5: Fill homeowners general information
            await self._fill_homeowners_info(salesforce_data)
            
            # Step 6: Save and complete
            await self._save_and_complete()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error filling underwriting form: {str(e)}")
            return False
    
    async def _login_to_american_integrity(self):
        """Login to American Integrity system."""
        try:
            # Navigate to American Integrity login page
            await self.mcp_toolset.call_tool(
                "playwright_navigate",
                {"url": AMERICAN_INTEGRITY_CONFIG["url"]}
            )
            
            # Wait for page to load
            await asyncio.sleep(3)
            
            # Check if login is required
            try:
                # Look for username field
                await self.mcp_toolset.call_tool(
                    "playwright_wait_for_selector",
                    {"selector": "input[name='username'], input[type='text']", "timeout": 5000}
                )
                
                # Fill username
                await self.mcp_toolset.call_tool(
                    "playwright_fill",
                    {
                        "selector": "input[name='username'], input[type='text']",
                        "value": AMERICAN_INTEGRITY_CONFIG["username"]
                    }
                )
                
                # Fill password
                await self.mcp_toolset.call_tool(
                    "playwright_fill",
                    {
                        "selector": "input[name='password'], input[type='password']",
                        "value": AMERICAN_INTEGRITY_CONFIG["password"]
                    }
                )
                
                # Click login button
                await self.mcp_toolset.call_tool(
                    "playwright_click",
                    {"selector": "button[type='submit'], input[type='submit'], button:has-text('Login')"}
                )
                
                # Wait for login to complete
                await asyncio.sleep(5)
                
                self.logger.info("Successfully logged into American Integrity")
                
            except:
                # Login might not be required or already logged in
                self.logger.info("Login not required or already logged in")
            
        except Exception as e:
            self.logger.error(f"Error logging into American Integrity: {str(e)}")
            raise
    
    async def _search_quote(self, quote_number: str):
        """Search for the specific quote number."""
        try:
            # Look for search field
            search_selectors = [
                "input[placeholder*='search']",
                "input[name*='search']",
                "input[type='search']",
                ".search-input",
                "#search"
            ]
            
            search_field_found = False
            for selector in search_selectors:
                try:
                    await self.mcp_toolset.call_tool(
                        "playwright_wait_for_selector",
                        {"selector": selector, "timeout": 2000}
                    )
                    
                    # Fill search field with quote number
                    await self.mcp_toolset.call_tool(
                        "playwright_fill",
                        {"selector": selector, "value": quote_number}
                    )
                    
                    # Press Enter or click search button
                    await self.mcp_toolset.call_tool(
                        "playwright_press",
                        {"selector": selector, "key": "Enter"}
                    )
                    
                    search_field_found = True
                    break
                    
                except:
                    continue
            
            if not search_field_found:
                raise Exception("Could not find search field")
            
            # Wait for search results
            await asyncio.sleep(3)
            
            # Click on the quote result
            await self.mcp_toolset.call_tool(
                "playwright_click",
                {"selector": f"a:has-text('{quote_number}'), tr:has-text('{quote_number}')"}
            )
            
            # Wait for quote to load
            await asyncio.sleep(3)
            
            self.logger.info(f"Successfully found and opened quote: {quote_number}")
            
        except Exception as e:
            self.logger.error(f"Error searching for quote: {str(e)}")
            raise
    
    async def _navigate_to_underwriting(self):
        """Navigate to the underwriting questions section."""
        try:
            # Look for underwriting section
            underwriting_selectors = [
                "a:has-text('Underwriting')",
                "button:has-text('Underwriting')",
                "[title*='Underwriting']",
                ".underwriting",
                "#underwriting"
            ]
            
            for selector in underwriting_selectors:
                try:
                    await self.mcp_toolset.call_tool(
                        "playwright_click",
                        {"selector": selector}
                    )
                    break
                except:
                    continue
            
            # Wait for underwriting page to load
            await asyncio.sleep(3)
            
            self.logger.info("Successfully navigated to underwriting section")
            
        except Exception as e:
            self.logger.error(f"Error navigating to underwriting: {str(e)}")
            raise

    async def _fill_underwriting_questions(self, salesforce_data: Dict[str, Any]):
        """Fill all underwriting questions based on Salesforce data."""
        try:
            # Map Salesforce data to underwriting questions
            answers = self._map_salesforce_to_answers(salesforce_data)

            # Fill each question
            for question_num, answer in answers.items():
                await self._fill_question(question_num, answer)
                await asyncio.sleep(0.5)  # Small delay between questions

            # Click Next Page button
            await self.mcp_toolset.call_tool(
                "playwright_click",
                {"selector": "button:has-text('Next Page'), button:has-text('Next')"}
            )

            await asyncio.sleep(2)

            self.logger.info("Successfully filled underwriting questions")

        except Exception as e:
            self.logger.error(f"Error filling underwriting questions: {str(e)}")
            raise

    def _map_salesforce_to_answers(self, salesforce_data: Dict[str, Any]) -> Dict[int, str]:
        """Map Salesforce data to underwriting question answers."""
        answers = {}

        # Question 1: Felony, insurance fraud, arson
        answers[1] = "Yes" if salesforce_data.get("felony", False) else "No"

        # Question 2: Foreclosure, repossession, bankruptcy
        answers[2] = "Yes" if salesforce_data.get("foreclosure", False) else "No"

        # Question 3: Fire or liability losses (check claims data)
        loss_type = salesforce_data.get("loss_type", "").lower()
        loss_date = salesforce_data.get("loss_date", "")
        fire_loss_recent = False
        if loss_type == "fire" and loss_date:
            try:
                loss_datetime = datetime.strptime(loss_date, "%Y-%m-%d")
                five_years_ago = datetime.now() - timedelta(days=5*365)
                fire_loss_recent = loss_datetime > five_years_ago
            except:
                pass
        answers[3] = "Yes" if fire_loss_recent else "No"

        # Question 4: Flood loss at location
        answers[4] = "Yes" if salesforce_data.get("flood_losses", False) else "No"

        # Question 5: Cancelled, declined, non-renewed
        answers[5] = "Yes" if salesforce_data.get("cancelled_coverage", False) else "No"

        # Question 6: More than 1 non-weather related losses (simplified)
        answers[6] = "No"  # Default as per instructions

        # Question 7: Sinkhole investigation
        answers[7] = "Yes" if salesforce_data.get("sinkhole_activity", False) else "No"

        # Question 8: Sinkhole claims
        answers[8] = "Yes" if salesforce_data.get("sinkhole_claims", False) else "No"

        # Question 9: Prior insurance
        currently_insured = salesforce_data.get("currently_insured", "").lower()
        if currently_insured in ["new purchase", "currently insured"]:
            answers[9] = "Yes"
        else:
            answers[9] = "No"

        # Question 10: Lapse in coverage
        answers[10] = "Yes" if salesforce_data.get("lapse_coverage", False) else "No"

        # Question 11: Own animals
        animal_count = salesforce_data.get("animal_count", 0)
        answers[11] = "Yes" if animal_count > 0 else "No"

        # Question 12: Exotic animals
        answers[12] = "Yes" if salesforce_data.get("exotic_animals", False) else "No"

        # Question 13: Recreational vehicles (ATV)
        answers[13] = "Yes" if salesforce_data.get("atv", False) else "No"

        # Question 14: Excessive liability exposures (simplified)
        answers[14] = "No"  # Default for now

        # Question 15: Occupy within 30 days
        home_vacant = salesforce_data.get("home_vacant", False)
        answers[15] = "No" if home_vacant else "Yes"

        # Question 16: Non-weather water losses
        answers[16] = "No"  # As per instructions

        # Question 17: Short-sale or foreclosure
        answers[17] = "Yes" if salesforce_data.get("foreclosure", False) else "No"

        # Question 18: Existing damage
        answers[18] = "Yes" if salesforce_data.get("existing_damage", False) else "No"

        # Question 19: Sinkhole activity disclosures
        answers[19] = "Yes" if salesforce_data.get("sinkhole_activity", False) else "No"

        # Question 20: Pool, hot tub, spa
        answers[20] = "Yes" if salesforce_data.get("pool", False) else "No"

        # Question 21: 3+ unrelated individuals
        answers[21] = "No"  # Default as per instructions

        # Question 22: Business activity
        answers[22] = "Yes" if salesforce_data.get("business_activity", False) else "No"

        # Question 23: Daycare services
        answers[23] = "Yes" if salesforce_data.get("daycare_services", False) else "No"

        # Question 24: Known sinkhole activity
        answers[24] = "Yes" if salesforce_data.get("sinkhole_activity", False) else "No"

        # Question 25: Vacant 30+ days prior to purchase
        answers[25] = "Yes" if salesforce_data.get("home_vacant", False) else "No"

        # Question 26: Special Flood Hazard Area
        answers[26] = "Yes" if salesforce_data.get("hazardous_flood_zone", False) else "No"

        # Question 27: Previously insured with American Integrity
        answers[27] = "Yes" if salesforce_data.get("previous_american_integrity", False) else "No"

        # Question 28: Lawsuit against insurance company
        answers[28] = "Yes" if salesforce_data.get("sued_carrier", False) else "No"

        # Question 29: Assignment of benefits claim lawsuit
        answers[29] = "Yes" if salesforce_data.get("assignment_benefits", False) else "No"

        return answers

    async def _fill_question(self, question_num: int, answer: str):
        """Fill a specific underwriting question."""
        try:
            # Look for the question by number or text
            question_selectors = [
                f"input[name*='question{question_num}']",
                f"select[name*='question{question_num}']",
                f"input[id*='q{question_num}']",
                f"select[id*='q{question_num}']"
            ]

            for selector in question_selectors:
                try:
                    # Check if it's a radio button, checkbox, or select
                    element_type = await self.mcp_toolset.call_tool(
                        "playwright_get_attribute",
                        {"selector": selector, "attribute": "type"}
                    )

                    if element_type.get("value") in ["radio", "checkbox"]:
                        # For radio buttons and checkboxes, look for the specific value
                        value_selector = f"{selector}[value*='{answer}']"
                        await self.mcp_toolset.call_tool(
                            "playwright_click",
                            {"selector": value_selector}
                        )
                    else:
                        # For select dropdowns or text inputs
                        await self.mcp_toolset.call_tool(
                            "playwright_select_option",
                            {"selector": selector, "value": answer}
                        )

                    self.logger.debug(f"Filled question {question_num} with answer: {answer}")
                    return

                except:
                    continue

            # If specific selectors don't work, try generic approach
            # Look for Yes/No radio buttons near question text
            try:
                if answer.lower() == "yes":
                    await self.mcp_toolset.call_tool(
                        "playwright_click",
                        {"selector": f"input[value='Yes']:nth-of-type({question_num})"}
                    )
                else:
                    await self.mcp_toolset.call_tool(
                        "playwright_click",
                        {"selector": f"input[value='No']:nth-of-type({question_num})"}
                    )
            except:
                self.logger.warning(f"Could not fill question {question_num}")

        except Exception as e:
            self.logger.error(f"Error filling question {question_num}: {str(e)}")
            # Continue with other questions even if one fails

    async def _fill_homeowners_info(self, salesforce_data: Dict[str, Any]):
        """Fill homeowners general information section."""
        try:
            # Dwelling Type
            dwelling_type = salesforce_data.get("dwelling_type", "")
            if dwelling_type:
                dwelling_mapping = {
                    "Single Family Detached": "Single Family",
                    "Duplex": "Duplex",
                    "Townhouse": "Rowhouse/Townhouse"
                }

                mapped_dwelling = dwelling_mapping.get(dwelling_type, dwelling_type)
                await self._fill_field("dwelling_type", mapped_dwelling)

            # Quality Grade
            quality_grade = salesforce_data.get("quality_grade", "")
            if quality_grade:
                quality_mapping = {
                    "Premium": "Premium",
                    "Custom": "Custom",
                    "Above Average": "Above Average",
                    "Standard": "Standard",
                    "Economy": "Economy"
                }

                mapped_quality = quality_mapping.get(quality_grade, quality_grade)
                await self._fill_field("quality_grade", mapped_quality)

            self.logger.info("Successfully filled homeowners general information")

        except Exception as e:
            self.logger.error(f"Error filling homeowners info: {str(e)}")
            raise

    async def _fill_field(self, field_name: str, value: str):
        """Fill a specific field by name."""
        try:
            field_selectors = [
                f"select[name*='{field_name}']",
                f"input[name*='{field_name}']",
                f"select[id*='{field_name}']",
                f"input[id*='{field_name}']"
            ]

            for selector in field_selectors:
                try:
                    await self.mcp_toolset.call_tool(
                        "playwright_select_option",
                        {"selector": selector, "value": value}
                    )
                    self.logger.debug(f"Filled field {field_name} with value: {value}")
                    return
                except:
                    continue

            self.logger.warning(f"Could not fill field {field_name}")

        except Exception as e:
            self.logger.error(f"Error filling field {field_name}: {str(e)}")

    async def _save_and_complete(self):
        """Save the form and complete the process."""
        try:
            # Click Save button
            save_selectors = [
                "button:has-text('Save')",
                "input[value='Save']",
                "button[type='submit']"
            ]

            for selector in save_selectors:
                try:
                    await self.mcp_toolset.call_tool(
                        "playwright_click",
                        {"selector": selector}
                    )
                    break
                except:
                    continue

            # Wait for save to complete
            await asyncio.sleep(3)

            # Click Next Page button until back to Policy section
            max_attempts = 5
            for _ in range(max_attempts):
                try:
                    await self.mcp_toolset.call_tool(
                        "playwright_click",
                        {"selector": "button:has-text('Next Page'), button:has-text('Next')"}
                    )
                    await asyncio.sleep(2)

                    # Check if we're back at Policy section
                    try:
                        await self.mcp_toolset.call_tool(
                            "playwright_wait_for_selector",
                            {"selector": "a:has-text('Policy'), .policy-section", "timeout": 2000}
                        )
                        break
                    except:
                        continue

                except:
                    break

            self.logger.info("Successfully saved and completed the form")

        except Exception as e:
            self.logger.error(f"Error saving and completing form: {str(e)}")
            raise


if __name__ == "__main__":
    agent = AmericanIntegrityAgent()
    asyncio.run(agent.initialize())
    agent.run()
