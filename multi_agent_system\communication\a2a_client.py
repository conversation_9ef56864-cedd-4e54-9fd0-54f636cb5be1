"""A2A Client for inter-agent communication."""

import asyncio
import logging
import httpx
from typing import Dict, Any, Optional
# from a2a.types import HTT<PERSON>lient
from a2a.client import A2AClient

from a2a.types import AgentCard


class A2AAgentClient:
    """Client for communicating with other agents using A2A protocol."""
    
    def __init__(self, agent_name: str):
        self.agent_name = agent_name
        self.logger = logging.getLogger(f"a2a_client.{agent_name}")
        self.a2a_client = None
        self.agent_cards: Dict[str, AgentCard] = {}
    
    async def initialize(self):
        """Initialize the A2A client."""
        try:
            # Initialize a2a client
            # self.a2a_client = Client()
            # await self.a2a_client.initialize()
            self.logger.info(f"A2A client initialized for {self.agent_name}")
        except Exception as e:
            self.logger.error(f"Failed to initialize A2A client: {str(e)}")
            raise
    
    async def discover_agent(self, agent_url: str) -> Optional[AgentCard]:
        """Discover an agent and get its card."""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(agent_url, timeout=10.0)
                response.raise_for_status()
                
                agent_card_data = response.json()
                agent_card = AgentCard(**agent_card_data)
                
                self.agent_cards[agent_card.name] = agent_card
                self.logger.info(f"Discovered agent: {agent_card.name}")
                
                return agent_card
                
        except Exception as e:
            self.logger.error(f"Failed to discover agent at {agent_url}: {str(e)}")
            return None
    
    async def send_task(self, agent_url: str, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """Send a task to another agent."""
        try:
            # For now, use HTTP communication as fallback
            # In a full implementation, this would use the a2a protocol
            
            task_request = {
                "id": task_data.get("id", ""),
                "message": {
                    "role": "user",
                    "parts": [{"text": str(task_data)}]
                },
                "context": task_data
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{agent_url}/tasks",
                    json=task_request,
                    timeout=300.0  # 5 minutes timeout
                )
                response.raise_for_status()
                
                result = response.json()
                self.logger.info(f"Task sent successfully to {agent_url}")
                
                return result
                
        except Exception as e:
            self.logger.error(f"Failed to send task to {agent_url}: {str(e)}")
            raise
    
    async def send_streaming_task(self, agent_url: str, task_data: Dict[str, Any]):
        """Send a streaming task to another agent (if supported)."""
        # This would implement streaming communication using A2A protocol
        # For now, fall back to regular task sending
        return await self.send_task(agent_url, task_data)
    
    async def get_agent_capabilities(self, agent_name: str) -> Optional[Dict[str, Any]]:
        """Get capabilities of a discovered agent."""
        agent_card = self.agent_cards.get(agent_name)
        if agent_card:
            return {
                "streaming": agent_card.capabilities.streaming,
                "push_notifications": agent_card.capabilities.push_notifications,
                "skills": [skill.model_dump() for skill in agent_card.skills]
            }
        return None
    
    async def close(self):
        """Close the A2A client."""
        try:
            if self.a2a_client:
                await self.a2a_client.close()
            self.logger.info(f"A2A client closed for {self.agent_name}")
        except Exception as e:
            self.logger.error(f"Error closing A2A client: {str(e)}")


class AgentRegistry:
    """Registry for managing agent discovery and communication."""
    
    def __init__(self):
        self.agents: Dict[str, str] = {}  # agent_name -> agent_url
        self.clients: Dict[str, A2AAgentClient] = {}
        self.logger = logging.getLogger("agent_registry")
    
    def register_agent(self, agent_name: str, agent_url: str):
        """Register an agent in the registry."""
        self.agents[agent_name] = agent_url
        self.logger.info(f"Registered agent {agent_name} at {agent_url}")
    
    async def get_client(self, client_name: str) -> A2AAgentClient:
        """Get or create an A2A client for communication."""
        if client_name not in self.clients:
            client = A2AAgentClient(client_name)
            await client.initialize()
            self.clients[client_name] = client
        
        return self.clients[client_name]
    
    async def discover_all_agents(self, client_name: str):
        """Discover all registered agents."""
        client = await self.get_client(client_name)
        
        for agent_name, agent_url in self.agents.items():
            await client.discover_agent(agent_url)
    
    async def send_task_to_agent(self, 
                                client_name: str, 
                                target_agent: str, 
                                task_data: Dict[str, Any]) -> Dict[str, Any]:
        """Send a task to a specific agent."""
        if target_agent not in self.agents:
            raise ValueError(f"Agent {target_agent} not registered")
        
        client = await self.get_client(client_name)
        agent_url = self.agents[target_agent]
        
        return await client.send_task(agent_url, task_data)
    
    def get_agent_url(self, agent_name: str) -> Optional[str]:
        """Get the URL of a registered agent."""
        return self.agents.get(agent_name)
    
    async def close_all_clients(self):
        """Close all A2A clients."""
        for client in self.clients.values():
            await client.close()
        self.clients.clear()


# Global registry instance
agent_registry = AgentRegistry()
