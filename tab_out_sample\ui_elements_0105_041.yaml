browser_component:
  tab_title: *********** - Guidewire
  url: ai.iscs.com/innovation
  address_bar_focused: false
  tab_state: idle
webpage:
  header:
    - type: container
      id: top_header
      bounds: {x: 0, y: 35, width: 1920, height: 100}
      children:
        - type: image
          id: logo_american_integrity
          bounds: {x: 15, y: 55, width: 150, height: 40}
          label: AMERICAN INTEGRITY INSURANCE GROUP
        - type: navigation
          id: main_nav
          bounds: {x: 1550, y: 55, width: 350, height: 30}
          children:
            - type: link
              id: nav_home
              bounds: {x: 1555, y: 60, width: 40, height: 20}
              label: Home
            - type: link
              id: nav_quote_policy
              bounds: {x: 1610, y: 60, width: 80, height: 20}
              label: Quote/Policy
              state: active
            - type: link
              id: nav_claims
              bounds: {x: 1705, y: 60, width: 50, height: 20}
              label: Claims
            - type: link
              id: nav_cabinets
              bounds: {x: 1770, y: 60, width: 60, height: 20}
              label: Cabinets
            - type: link
              id: nav_support
              bounds: {x: 1845, y: 60, width: 55, height: 20}
              label: Support
    - type: container
      id: search_bar_container
      bounds: {x: 15, y: 105, width: 300, height: 50}
      children:
        - type: input
          id: input_search
          bounds: {x: 15, y: 110, width: 250, height: 30}
          label: Search
          value: ''
        - type: button
          id: btn_search
          bounds: {x: 265, y: 110, width: 30, height: 30}
          label: Q
        - type: text
          id: text_advanced_search
          bounds: {x: 15, y: 145, width: 120, height: 15}
          label: 'ADVANCED SEARCH:'
        - type: link
          id: link_policy_search
          bounds: {x: 135, y: 145, width: 45, height: 15}
          label: POLICY
        - type: link
          id: link_claims_search
          bounds: {x: 190, y: 145, width: 50, height: 15}
          label: CLAIMS
    - type: container
      id: quote_details_header
      bounds: {x: 320, y: 105, width: 1580, height: 50}
      children:
        - type: text
          id: quote_label
          bounds: {x: 330, y: 110, width: 60, height: 30}
          label: QUOTE
        - type: text
          id: quote_number_label
          bounds: {x: 420, y: 110, width: 80, height: 15}
          label: Quote Number
        - type: text
          id: quote_number_value
          bounds: {x: 420, y: 125, width: 80, height: 15}
          label: ***********
        - type: text
          id: insured_label
          bounds: {x: 520, y: 110, width: 50, height: 15}
          label: Insured
        - type: text
          id: insured_value
          bounds: {x: 520, y: 125, width: 90, height: 15}
          label: Landon Cassidy
        - type: text
          id: product_label
          bounds: {x: 630, y: 110, width: 50, height: 15}
          label: Product
        - type: text
          id: product_value
          bounds: {x: 630, y: 125, width: 160, height: 15}
          label: Voluntary Homeowners (HO3)
        - type: text
          id: sub_type_label
          bounds: {x: 810, y: 110, width: 50, height: 15}
          label: Sub Type
        - type: text
          id: sub_type_value
          bounds: {x: 810, y: 125, width: 25, height: 15}
          label: HO3
        - type: text
          id: policy_term_label
          bounds: {x: 880, y: 110, width: 70, height: 15}
          label: Policy Term
        - type: text
          id: policy_term_value
          bounds: {x: 880, y: 125, width: 150, height: 15}
          label: 06/20/2025 - 06/20/2026
        - type: text
          id: producer_label
          bounds: {x: 1050, y: 110, width: 60, height: 15}
          label: Producer
        - type: link
          id: producer_value
          bounds: {x: 1050, y: 125, width: 140, height: 15}
          label: HH Insurance Group, LLC
        - type: text
          id: status_label
          bounds: {x: 1210, y: 110, width: 40, height: 15}
          label: Status
        - type: text
          id: status_value
          bounds: {x: 1210, y: 125, width: 60, height: 15}
          label: In Process
        - type: text
          id: premium_fees_label
          bounds: {x: 1290, y: 110, width: 100, height: 15}
          label: Premium + Fees
        - type: text
          id: premium_fees_value
          bounds: {x: 1290, y: 125, width: 65, height: 15}
          label: $17,776.90
  sidebar:
    - type: navigation
      id: left_sidebar
      bounds: {x: 0, y: 160, width: 310, height: 800}
      children:
        - type: dropdown
          id: dropdown_quote
          bounds: {x: 15, y: 170, width: 280, height: 30}
          label: Quote
        - type: list
          id: list_quote_sections
          bounds: {x: 15, y: 210, width: 280, height: 120}
          children:
            - type: link
              id: link_policy
              bounds: {x: 30, y: 220, width: 265, height: 30}
              label: Policy
              state: selected
            - type: link
              id: link_dwelling
              bounds: {x: 30, y: 250, width: 265, height: 30}
              label: Dwelling
            - type: badge
              id: badge_dwelling
              bounds: {x: 275, y: 255, width: 15, height: 15}
              label: '2'
            - type: link
              id: link_review
              bounds: {x: 30, y: 280, width: 265, height: 30}
              label: Review
        - type: list
          id: list_quote_actions
          bounds: {x: 15, y: 340, width: 280, height: 200}
          children:
            - type: link
              id: link_attachments
              bounds: {x: 15, y: 350, width: 280, height: 30}
              label: Attachments
            - type: link
              id: link_correspondence
              bounds: {x: 15, y: 380, width: 280, height: 30}
              label: Correspondence
            - type: link
              id: link_tasks
              bounds: {x: 15, y: 410, width: 280, height: 30}
              label: Tasks
            - type: link
              id: link_notes
              bounds: {x: 15, y: 440, width: 280, height: 30}
              label: Notes
            - type: link
              id: link_policy_file
              bounds: {x: 15, y: 470, width: 280, height: 30}
              label: Policy File
    - type: sidebar
      id: right_sidebar
      bounds: {x: 1870, y: 160, width: 50, height: 800}
      children:
        - type: button
          id: btn_summary
          bounds: {x: 1875, y: 170, width: 40, height: 40}
          label: SUMMARY
        - type: button
          id: btn_quick_qt
          bounds: {x: 1875, y: 230, width: 40, height: 40}
          label: WTBC/RET QUICK QT
        - type: button
          id: btn_new_quote
          bounds: {x: 1875, y: 290, width: 40, height: 40}
          label: NEW QUOTE
        - type: button
          id: btn_new_note
          bounds: {x: 1875, y: 350, width: 40, height: 40}
          label: NEW NOTE
        - type: button
          id: btn_new_attach
          bounds: {x: 1875, y: 410, width: 40, height: 40}
          label: NEW ATTACH...
        - type: button
          id: btn_new_task
          bounds: {x: 1875, y: 470, width: 40, height: 40}
          label: NEW TASK
  main_content:
    - type: container
      id: main_content_area
      bounds: {x: 310, y: 160, width: 1560, height: 800}
      children:
        - type: container
          id: action_buttons_top
          bounds: {x: 1100, y: 170, width: 750, height: 30}
          children:
            - type: button
              id: btn_next_page_top
              bounds: {x: 1105, y: 170, width: 100, height: 30}
              label: NEXT PAGE
            - type: button
              id: btn_save
              bounds: {x: 1215, y: 170, width: 70, height: 30}
              label: SAVE
            - type: button
              id: btn_print
              bounds: {x: 1295, y: 170, width: 70, height: 30}
              label: PRINT
            - type: button
              id: btn_create_application
              bounds: {x: 1375, y: 170, width: 150, height: 30}
              label: CREATE APPLICATION
            - type: button
              id: btn_discard_changes
              bounds: {x: 1535, y: 170, width: 150, height: 30}
              label: DISCARD CHANGES
              state: disabled
            - type: button
              id: btn_view_notes
              bounds: {x: 1695, y: 170, width: 100, height: 30}
              label: VIEW NOTES
            - type: button
              id: btn_delete
              bounds: {x: 1805, y: 170, width: 70, height: 30}
              label: DELETE
            - type: button
              id: btn_more
              bounds: {x: 1885, y: 170, width: 60, height: 30}
              label: '... MORE'
        - type: link
          id: link_return_to_home
          bounds: {x: 330, y: 210, width: 120, height: 20}
          label: < Return to Home
        - type: form
          id: form_policy_details
          bounds: {x: 330, y: 240, width: 1520, height: 700}
          children:
            - type: text
              id: title_prior_carrier
              bounds: {x: 340, y: 250, width: 150, height: 20}
              label: Prior Carrier Details
            - type: dropdown
              id: dropdown_prior_carrier
              bounds: {x: 340, y: 280, width: 200, height: 30}
              label: Prior Carrier*
              value: New Purchase
            - type: input
              id: input_prior_policy_expiration
              bounds: {x: 1000, y: 280, width: 200, height: 30}
              label: Prior Policy Expiration Date
              value: ''
            - type: text
              id: title_insured_info
              bounds: {x: 340, y: 340, width: 150, height: 20}
              label: Insured Information
            - type: dropdown
              id: dropdown_entity_type
              bounds: {x: 340, y: 370, width: 200, height: 30}
              label: Entity Type*
              value: Individual
            - type: text
              id: text_individual
              bounds: {x: 340, y: 405, width: 100, height: 20}
              label: Individual
            - type: input
              id: input_first_name
              bounds: {x: 340, y: 440, width: 200, height: 30}
              label: First*
              value: Landon
            - type: input
              id: input_middle_name
              bounds: {x: 560, y: 440, width: 200, height: 30}
              label: Middle
              value: ''
            - type: input
              id: input_last_name
              bounds: {x: 780, y: 440, width: 200, height: 30}
              label: Last*
              value: Cassidy
            - type: input
              id: input_suffix
              bounds: {x: 1000, y: 440, width: 100, height: 30}
              label: Suffix
              value: ''
            - type: input
              id: input_dob
              bounds: {x: 340, y: 480, width: 200, height: 30}
              label: DOB*
              value: 05/20/1998
            - type: dropdown
              id: dropdown_insurance_score
              bounds: {x: 560, y: 480, width: 200, height: 30}
              label: Insurance Score*
              value: Excellent (850-899)
              state: hovered
            - type: input
              id: input_search_name
              bounds: {x: 340, y: 520, width: 200, height: 30}
              label: Search Name*
              value: Landon Cassidy
            - type: link
              id: link_reset
              bounds: {x: 550, y: 525, width: 40, height: 20}
              label: Reset
            - type: dropdown
              id: dropdown_primary_phone
              bounds: {x: 340, y: 560, width: 200, height: 30}
              label: Primary Phone
              value: Select...
            - type: input
              id: input_email
              bounds: {x: 340, y: 600, width: 200, height: 30}
              label: Email
              value: ''
            - type: checkbox
              id: checkbox_no_email
              bounds: {x: 560, y: 600, width: 100, height: 30}
              label: No Email
            - type: text
              id: title_dwelling_info
              bounds: {x: 340, y: 640, width: 150, height: 20}
              label: Dwelling Information
            - type: text
              id: text_lookup_address
              bounds: {x: 340, y: 670, width: 120, height: 20}
              label: Lookup Address
            - type: input
              id: input_dwelling_number
              bounds: {x: 340, y: 700, width: 100, height: 30}
              label: Number*
              value: '4227'
            - type: input
              id: input_dwelling_direction
              bounds: {x: 450, y: 700, width: 100, height: 30}
              label: Direction
              value: ''
            - type: input
              id: input_dwelling_street
              bounds: {x: 560, y: 700, width: 150, height: 30}
              label: Street*
              value: 5th
            - type: dropdown
              id: dropdown_dwelling_suffix
              bounds: {x: 720, y: 700, width: 100, height: 30}
              label: Suffix
              value: Ave
            - type: dropdown
              id: dropdown_dwelling_post_dir
              bounds: {x: 830, y: 700, width: 100, height: 30}
              label: Post Dir
              value: ''
            - type: dropdown
              id: dropdown_dwelling_type
              bounds: {x: 940, y: 700, width: 100, height: 30}
              label: Type
              value: ''
            - type: input
              id: input_dwelling_unit_number
              bounds: {x: 1050, y: 700, width: 100, height: 30}
              label: Number
              value: ''
            - type: checkbox
              id: checkbox_ignore_address_validation
              bounds: {x: 1170, y: 670, width: 200, height: 20}
              label: Ignore Address Validation
            - type: input
              id: input_dwelling_city
              bounds: {x: 340, y: 740, width: 150, height: 30}
              label: City*
              value: St Petersburg
            - type: dropdown
              id: dropdown_dwelling_county
              bounds: {x: 500, y: 740, width: 150, height: 30}
              label: County*
              value: Pinellas
            - type: dropdown
              id: dropdown_dwelling_state
              bounds: {x: 660, y: 740, width: 150, height: 30}
              label: State*
              value: Florida
            - type: input
              id: input_dwelling_zip
              bounds: {x: 820, y: 740, width: 150, height: 30}
              label: Zip*
              value: 33711-1522
            - type: text
              id: text_address_verified
              bounds: {x: 830, y: 780, width: 120, height: 20}
              label: Address Verified
            - type: link
              id: link_view_map
              bounds: {x: 950, y: 780, width: 60, height: 20}
              label: View Map
            - type: input
              id: input_latitude
              bounds: {x: 340, y: 780, width: 150, height: 30}
              label: Latitude*
              value: '27.766685'
            - type: input
              id: input_longitude
              bounds: {x: 500, y: 780, width: 150, height: 30}
              label: Longitude*
              value: '-82.690887'
            - type: dropdown
              id: dropdown_construction_type
              bounds: {x: 340, y: 820, width: 150, height: 30}
              label: Construction Type*
              value: Masonry
            - type: dropdown
              id: dropdown_occupancy
              bounds: {x: 500, y: 820, width: 150, height: 30}
              label: Occupancy*
              value: Owner Occupied
            - type: dropdown
              id: dropdown_months_occupied
              bounds: {x: 660, y: 820, width: 150, height: 30}
              label: Months Occupied*
              value: 9 to 12 Months
            - type: text
              id: text_resided_less_than_2_years
              bounds: {x: 340, y: 860, width: 400, height: 20}
              label: Has the insured resided at the risk address for less than 2 years?*
            - type: dropdown
              id: dropdown_resided_less_than_2_years
              bounds: {x: 750, y: 860, width: 100, height: 30}
              value: 'Yes'
            - type: text
              id: title_prior_address
              bounds: {x: 340, y: 900, width: 100, height: 20}
              label: Prior Address
            - type: text
              id: text_address
              bounds: {x: 340, y: 930, width: 60, height: 20}
              label: Address
            - type: input
              id: input_prior_number
              bounds: {x: 340, y: 960, width: 100, height: 30}
              label: Number*
              value: '18001'
            - type: input
              id: input_prior_direction
              bounds: {x: 450, y: 960, width: 100, height: 30}
              label: Direction
              value: ''
            - type: input
              id: input_prior_street
              bounds: {x: 560, y: 960, width: 150, height: 30}
              label: Street*
              value: Avalon
            - type: dropdown
              id: dropdown_prior_suffix
              bounds: {x: 720, y: 960, width: 100, height: 30}
              label: Suffix
              value: Ln
            - type: input
              id: input_prior_post_dir
              bounds: {x: 830, y: 960, width: 100, height: 30}
              label: Post Dir
              value: ''
            - type: dropdown
              id: dropdown_prior_type
              bounds: {x: 940, y: 960, width: 100, height: 30}
              label: Type
              value: ''
            - type: input
              id: input_prior_unit_number
              bounds: {x: 1050, y: 960, width: 100, height: 30}
              label: Number
              value: ''
            - type: input
              id: input_prior_city
              bounds: {x: 340, y: 1000, width: 150, height: 30}
              label: City*
              value: Tampa
            - type: dropdown
              id: dropdown_prior_county
              bounds: {x: 500, y: 1000, width: 150, height: 30}
              label: County*
              value: Hillsborough
            - type: dropdown
              id: dropdown_prior_state
              bounds: {x: 660, y: 1000, width: 150, height: 30}
              label: State*
              value: Florida
            - type: input
              id: input_prior_zip
              bounds: {x: 820, y: 1000, width: 150, height: 30}
              label: Zip*
              value: 33647-3102
            - type: text
              id: text_prior_address_verified
              bounds: {x: 830, y: 1040, width: 120, height: 20}
              label: Address Verified
            - type: link
              id: link_prior_view_map
              bounds: {x: 950, y: 1040, width: 60, height: 20}
              label: View Map
        - type: button
          id: btn_next_page_bottom
          bounds: {x: 330, y: 1090, width: 100, height: 30}
          label: Next Page
  footer:
    - type: container
      id: footer_container
      bounds: {x: 0, y: 1140, width: 1920, height: 40}
      children:
        - type: image
          id: logo_guidewire
          bounds: {x: 340, y: 1150, width: 150, height: 20}
          label: Powered by GUIDEWIRE
        - type: text
          id: text_environment
          bounds: {x: 550, y: 1150, width: 150, height: 20}
          label: 'Environment : PROD AIUG'
        - type: text
          id: text_current_logon
          bounds: {x: 720, y: 1150, width: 150, height: 20}
          label: 'Current Logon : AG8529('
        - type: link
          id: link_sign_out
          bounds: {x: 870, y: 1150, width: 60, height: 20}
          label: Sign Out)
        - type: text
          id: text_posting_date
          bounds: {x: 950, y: 1150, width: 150, height: 20}
          label: 'Posting Date : 05/23/2025'