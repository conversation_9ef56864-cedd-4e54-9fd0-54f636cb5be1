browser_component:
  tab_title: *********** - Guidewire
  url: ai.iscs.com/innovation
  address_bar_focused: false
  tab_state: idle
webpage:
  header:
  - type: container
    id: header_bar
    bounds: {x: 0, y: 35, width: 1920, height: 85}
    children:
    - type: image
      id: logo_american_integrity
      label: AMERICAN INTEGRITY
      bounds: {x: 20, y: 45, width: 150, height: 30}
    - type: input
      id: input_search
      label: Search
      bounds: {x: 20, y: 85, width: 150, height: 30}
      value: ''
    - type: button
      id: btn_search
      label: null
      bounds: {x: 170, y: 85, width: 30, height: 30}
    - type: text
      id: text_advanced_search
      label: 'ADVANCED SEARCH:'
      bounds: {x: 210, y: 90, width: 120, height: 20}
    - type: link
      id: link_policy_search
      label: POLICY
      bounds: {x: 340, y: 90, width: 50, height: 20}
      state: active
    - type: link
      id: link_claims_search
      label: CLAIMS
      bounds: {x: 400, y: 90, width: 50, height: 20}
    - type: navigation
      id: main_nav
      bounds: {x: 1450, y: 80, width: 450, height: 40}
      children:
      - type: link
        id: nav_home
        label: Home
        bounds: {x: 1455, y: 90, width: 40, height: 20}
      - type: link
        id: nav_quote_policy
        label: Quote/Policy
        bounds: {x: 1515, y: 90, width: 80, height: 20}
        state: active
      - type: link
        id: nav_claims
        label: Claims
        bounds: {x: 1615, y: 90, width: 50, height: 20}
      - type: link
        id: nav_cabinets
        label: Cabinets
        bounds: {x: 1685, y: 90, width: 60, height: 20}
      - type: link
        id: nav_support
        label: Support
        bounds: {x: 1765, y: 90, width: 60, height: 20}
  navigation:
  - type: container
    id: left_sidebar_nav
    bounds: {x: 0, y: 120, width: 220, height: 780}
    children:
    - type: text
      id: text_quote_title
      label: Quote
      bounds: {x: 10, y: 130, width: 200, height: 30}
    - type: list
      id: quote_menu
      bounds: {x: 10, y: 170, width: 200, height: 300}
      children:
      - type: link
        id: link_policy
        label: Policy
        bounds: {x: 10, y: 175, width: 200, height: 30}
        state: active
      - type: link
        id: link_dwelling
        label: Dwelling
        bounds: {x: 10, y: 205, width: 200, height: 30}
        children:
        - type: badge
          id: badge_dwelling
          label: '2'
          bounds: {x: 190, y: 210, width: 15, height: 15}
      - type: link
        id: link_review
        label: Review
        bounds: {x: 10, y: 235, width: 200, height: 30}
      - type: link
        id: link_attachments
        label: Attachments
        bounds: {x: 10, y: 265, width: 200, height: 30}
      - type: link
        id: link_correspondence
        label: Correspondence
        bounds: {x: 10, y: 295, width: 200, height: 30}
      - type: link
        id: link_tasks
        label: Tasks
        bounds: {x: 10, y: 325, width: 200, height: 30}
      - type: link
        id: link_notes
        label: Notes
        bounds: {x: 10, y: 355, width: 200, height: 30}
      - type: link
        id: link_policy_file
        label: Policy File
        bounds: {x: 10, y: 385, width: 200, height: 30}
  main_content:
  - type: container
    id: quote_summary_header
    bounds: {x: 230, y: 125, width: 1650, height: 60}
    children:
    - type: text
      id: text_quote_label
      label: QUOTE
      bounds: {x: 240, y: 135, width: 60, height: 30}
    - type: container
      id: quote_details_grid
      bounds: {x: 320, y: 130, width: 1500, height: 50}
      children:
      - type: text
        id: label_quote_number
        label: Quote Number
        bounds: {x: 325, y: 135, width: 100, height: 15}
      - type: text
        id: value_quote_number
        label: ***********
        bounds: {x: 325, y: 155, width: 100, height: 15}
      - type: text
        id: label_insured
        label: Insured
        bounds: {x: 445, y: 135, width: 100, height: 15}
      - type: text
        id: value_insured
        label: Landon Cassidy
        bounds: {x: 445, y: 155, width: 100, height: 15}
      - type: text
        id: label_product
        label: Product
        bounds: {x: 565, y: 135, width: 180, height: 15}
      - type: text
        id: value_product
        label: Voluntary Homeowners (HO3)
        bounds: {x: 565, y: 155, width: 180, height: 15}
      - type: text
        id: label_sub_type
        label: Sub Type
        bounds: {x: 765, y: 135, width: 50, height: 15}
      - type: text
        id: value_sub_type
        label: HO3
        bounds: {x: 765, y: 155, width: 50, height: 15}
      - type: text
        id: label_policy_term
        label: Policy Term
        bounds: {x: 835, y: 135, width: 150, height: 15}
      - type: text
        id: value_policy_term
        label: 06/20/2025 - 06/20/2026
        bounds: {x: 835, y: 155, width: 150, height: 15}
      - type: text
        id: label_producer
        label: Producer
        bounds: {x: 1005, y: 135, width: 150, height: 15}
      - type: link
        id: link_producer
        label: HH Insurance Group, LLC
        bounds: {x: 1005, y: 155, width: 150, height: 15}
      - type: text
        id: label_status
        label: Status
        bounds: {x: 1175, y: 135, width: 80, height: 15}
      - type: text
        id: value_status
        label: In Process
        bounds: {x: 1175, y: 155, width: 80, height: 15}
      - type: text
        id: label_premium_fees
        label: Premium + Fees
        bounds: {x: 1275, y: 135, width: 100, height: 15}
      - type: text
        id: value_premium_fees
        label: $17,776.90
        bounds: {x: 1275, y: 155, width: 100, height: 15}
  - type: container
    id: main_actions_bar
    bounds: {x: 230, y: 185, width: 1650, height: 40}
    children:
    - type: link
      id: link_return_to_home
      label: < Return to Home
      bounds: {x: 240, y: 195, width: 120, height: 20}
    - type: button
      id: btn_next_page_top
      label: NEXT PAGE
      bounds: {x: 1330, y: 190, width: 90, height: 30}
    - type: button
      id: btn_save
      label: SAVE
      bounds: {x: 1430, y: 190, width: 60, height: 30}
    - type: button
      id: btn_print
      label: PRINT
      bounds: {x: 1500, y: 190, width: 60, height: 30}
    - type: button
      id: btn_create_application
      label: CREATE APPLICATION
      bounds: {x: 1570, y: 190, width: 150, height: 30}
    - type: button
      id: btn_discard_changes
      label: DISCARD CHANGES
      bounds: {x: 1730, y: 190, width: 140, height: 30}
      state: disabled
    - type: button
      id: btn_more
      label: '... MORE'
      bounds: {x: 1820, y: 190, width: 60, height: 30}
  - type: form
    id: policy_details_form
    bounds: {x: 230, y: 230, width: 1650, height: 700}
    children:
    - type: section
      id: section_prior_carrier
      label: Prior Carrier Details
      bounds: {x: 240, y: 240, width: 1630, height: 80}
      children:
      - type: dropdown
        id: dropdown_prior_carrier
        label: Prior Carrier*
        bounds: {x: 250, y: 280, width: 200, height: 30}
        value: New Purchase
      - type: input
        id: input_prior_policy_expiration
        label: Prior Policy Expiration Date
        bounds: {x: 800, y: 280, width: 200, height: 30}
        value: ''
    - type: section
      id: section_insured_info
      label: Insured Information
      bounds: {x: 240, y: 330, width: 1630, height: 280}
      children:
      - type: dropdown
        id: dropdown_entity_type
        label: Entity Type*
        bounds: {x: 250, y: 360, width: 200, height: 30}
        value: Individual
      - type: text
        id: text_entity_type_value
        label: Individual
        bounds: {x: 250, y: 395, width: 200, height: 20}
      - type: input
        id: input_first_name
        label: First*
        bounds: {x: 500, y: 410, width: 150, height: 30}
        value: Landon
      - type: input
        id: input_middle_name
        label: Middle
        bounds: {x: 660, y: 410, width: 150, height: 30}
        value: ''
      - type: input
        id: input_last_name
        label: Last*
        bounds: {x: 820, y: 410, width: 150, height: 30}
        value: Cassidy
      - type: input
        id: input_suffix
        label: Suffix
        bounds: {x: 980, y: 410, width: 100, height: 30}
        value: ''
      - type: input
        id: input_dob
        label: DOB*
        bounds: {x: 250, y: 450, width: 150, height: 30}
        value: 05/20/1998
      - type: dropdown
        id: dropdown_insurance_score
        label: Insurance Score*
        bounds: {x: 500, y: 450, width: 200, height: 30}
        value: Excellent (850-899)
      - type: input
        id: input_search_name
        label: Search Name*
        bounds: {x: 250, y: 490, width: 200, height: 30}
        value: Landon Cassidy
      - type: link
        id: link_reset_search_name
        label: Reset
        bounds: {x: 460, y: 495, width: 40, height: 20}
      - type: dropdown
        id: dropdown_primary_phone
        label: Primary Phone
        bounds: {x: 250, y: 530, width: 200, height: 30}
        value: Select...
      - type: input
        id: input_email
        label: Email
        bounds: {x: 250, y: 570, width: 200, height: 30}
        value: ''
      - type: checkbox
        id: chk_no_email
        label: No Email
        bounds: {x: 500, y: 570, width: 100, height: 30}
    - type: section
      id: section_dwelling_info
      label: Dwelling Information
      bounds: {x: 240, y: 610, width: 1630, height: 200}
      children:
      - type: checkbox
        id: chk_ignore_address_validation
        label: Ignore Address Validation
        bounds: {x: 890, y: 615, width: 200, height: 20}
      - type: input
        id: input_lookup_address
        label: Lookup Address
        bounds: {x: 250, y: 640, width: 150, height: 30}
        value: ''
      - type: input
        id: input_dwelling_number
        label: Number*
        bounds: {x: 500, y: 640, width: 80, height: 30}
        value: '4227'
      - type: input
        id: input_dwelling_direction
        label: Direction
        bounds: {x: 590, y: 640, width: 80, height: 30}
        value: ''
      - type: input
        id: input_dwelling_street
        label: Street*
        bounds: {x: 680, y: 640, width: 120, height: 30}
        value: 5th
      - type: dropdown
        id: dropdown_dwelling_suffix
        label: Suffix
        bounds: {x: 810, y: 640, width: 80, height: 30}
        value: Ave
      - type: dropdown
        id: dropdown_dwelling_post_dir
        label: Post Dir
        bounds: {x: 900, y: 640, width: 80, height: 30}
        value: ''
      - type: dropdown
        id: dropdown_dwelling_type
        label: Type
        bounds: {x: 990, y: 640, width: 80, height: 30}
        value: ''
      - type: input
        id: input_dwelling_unit_number
        label: Number
        bounds: {x: 1080, y: 640, width: 80, height: 30}
        value: ''
      - type: input
        id: input_dwelling_city
        label: City*
        bounds: {x: 250, y: 680, width: 120, height: 30}
        value: St Petersburg
      - type: dropdown
        id: dropdown_dwelling_county
        label: County*
        bounds: {x: 380, y: 680, width: 120, height: 30}
        value: Pinellas
      - type: dropdown
        id: dropdown_dwelling_state
        label: State*
        bounds: {x: 510, y: 680, width: 120, height: 30}
        value: Florida
      - type: input
        id: input_dwelling_zip
        label: Zip*
        bounds: {x: 640, y: 680, width: 120, height: 30}
        value: 33711-1522
      - type: text
        id: text_dwelling_address_verified
        label: Address Verified
        bounds: {x: 810, y: 685, width: 100, height: 20}
      - type: link
        id: link_dwelling_view_map
        label: View Map
        bounds: {x: 920, y: 685, width: 60, height: 20}
      - type: input
        id: input_latitude
        label: Latitude*
        bounds: {x: 250, y: 720, width: 120, height: 30}
        value: '27.766685'
      - type: input
        id: input_longitude
        label: Longitude*
        bounds: {x: 500, y: 720, width: 120, height: 30}
        value: '-82.690887'
      - type: dropdown
        id: dropdown_construction_type
        label: Construction Type*
        bounds: {x: 250, y: 760, width: 150, height: 30}
        value: Masonry
      - type: dropdown
        id: dropdown_occupancy
        label: Occupancy*
        bounds: {x: 500, y: 760, width: 150, height: 30}
        value: Owner Occupied
      - type: dropdown
        id: dropdown_months_occupied
        label: Months Occupied*
        bounds: {x: 800, y: 760, width: 150, height: 30}
        value: 0 to 12 Months
    - type: section
      id: section_prior_address
      label: Prior Address
      bounds: {x: 240, y: 800, width: 1630, height: 150}
      children:
      - type: text
        id: text_resided_less_than_2_years
        label: Has the Insured resided at the risk address for less than 2 years?*
        bounds: {x: 250, y: 810, width: 400, height: 20}
      - type: dropdown
        id: dropdown_resided_less_than_2_years
        label: null
        bounds: {x: 660, y: 810, width: 80, height: 30}
        value: 'Yes'
      - type: input
        id: input_prior_address
        label: Address
        bounds: {x: 250, y: 850, width: 150, height: 30}
        value: ''
      - type: input
        id: input_prior_number
        label: Number*
        bounds: {x: 500, y: 850, width: 80, height: 30}
        value: '18001'
      - type: input
        id: input_prior_direction
        label: Direction
        bounds: {x: 590, y: 850, width: 80, height: 30}
        value: ''
      - type: input
        id: input_prior_street
        label: Street*
        bounds: {x: 680, y: 850, width: 120, height: 30}
        value: Avalon
      - type: dropdown
        id: dropdown_prior_suffix
        label: Suffix
        bounds: {x: 810, y: 850, width: 80, height: 30}
        value: Ln
      - type: dropdown
        id: dropdown_prior_post_dir
        label: Post Dir
        bounds: {x: 900, y: 850, width: 80, height: 30}
        value: ''
      - type: dropdown
        id: dropdown_prior_type
        label: Type
        bounds: {x: 990, y: 850, width: 80, height: 30}
        value: ''
      - type: input
        id: input_prior_unit_number
        label: Number
        bounds: {x: 1080, y: 850, width: 80, height: 30}
        value: ''
      - type: input
        id: input_prior_city
        label: City*
        bounds: {x: 250, y: 890, width: 120, height: 30}
        value: Tampa
      - type: dropdown
        id: dropdown_prior_county
        label: County*
        bounds: {x: 380, y: 890, width: 120, height: 30}
        value: Hillsborough
      - type: dropdown
        id: dropdown_prior_state
        label: State*
        bounds: {x: 510, y: 890, width: 120, height: 30}
        value: Florida
      - type: input
        id: input_prior_zip
        label: Zip*
        bounds: {x: 640, y: 890, width: 120, height: 30}
        value: 33647-3102
      - type: text
        id: text_prior_address_verified
        label: Address Verified
        bounds: {x: 810, y: 895, width: 100, height: 20}
      - type: link
        id: link_prior_view_map
        label: View Map
        bounds: {x: 920, y: 895, width: 60, height: 20}
  - type: button
    id: btn_next_page_bottom
    label: Next Page
    bounds: {x: 10, y: 880, width: 100, height: 35}
    state: hovered
  sidebar:
  - type: container
    id: right_sidebar_actions
    bounds: {x: 1880, y: 120, width: 40, height: 800}
    children:
    - type: button
      id: btn_summary
      label: SUMMARY
      bounds: {x: 1885, y: 130, width: 30, height: 50}
    - type: button
      id: btn_quick_qt
      label: WT/BC/RET QUICK QT
      bounds: {x: 1885, y: 190, width: 30, height: 50}
    - type: button
      id: btn_new_quote
      label: NEW QUOTE
      bounds: {x: 1885, y: 250, width: 30, height: 50}
    - type: button
      id: btn_new_note
      label: NEW NOTE
      bounds: {x: 1885, y: 310, width: 30, height: 50}
    - type: button
      id: btn_new_attach
      label: NEW ATTACH...
      bounds: {x: 1885, y: 370, width: 30, height: 50}
    - type: button
      id: btn_new_task
      label: NEW TASK
      bounds: {x: 1885, y: 430, width: 30, height: 50}
  footer:
  - type: container
    id: footer_bar
    bounds: {x: 0, y: 930, width: 1920, height: 40}
    children:
    - type: text
      id: text_powered_by
      label: Powered by
      bounds: {x: 800, y: 940, width: 60, height: 20}
    - type: image
      id: logo_guidewire
      label: GUIDEWIRE
      bounds: {x: 870, y: 940, width: 100, height: 20}
    - type: text
      id: text_environment
      label: 'Environment : PROD AIIG'
      bounds: {x: 1000, y: 940, width: 150, height: 20}
    - type: text
      id: text_current_logon
      label: 'Current Logon : AG8529f'
      bounds: {x: 1160, y: 940, width: 150, height: 20}
    - type: link
      id: link_sign_out
      label: (Sign Out)
      bounds: {x: 1320, y: 940, width: 70, height: 20}
    - type: text
      id: text_posting_date
      label: 'Posting Date : 05/23/2025'
      bounds: {x: 1400, y: 940, width: 150, height: 20}