"""Document Agent - Handles Google Drive PDF access and quote number extraction."""

import asyncio
import logging
import re
from typing import Dict, Any, Optional
from a2a.types import AgentSkill

from .base_agent import BaseAgent, TaskRequest, TaskResponse
from ..config import GOOGLE_DRIVE_CONFIG, MCP_CONFIG


class DocumentAgent(BaseAgent):
    """
    Document Agent handles:
    1. Access Google Drive folder
    2. Open specific PDF file
    3. Extract quote number from PDF content
    """
    
    def __init__(self):
        skills = [
            AgentSkill(
                id="google_drive_access",
                name="Google Drive Access",
                description="Access and navigate Google Drive folders and files",
                tags=["google-drive", "file-access", "navigation"]
            ),
            AgentSkill(
                id="pdf_processing",
                name="PDF Processing",
                description="Open and extract content from PDF files",
                tags=["pdf", "document-processing", "text-extraction"]
            ),
            AgentSkill(
                id="quote_extraction",
                name="Quote Number Extraction",
                description="Extract quote numbers from document content using pattern matching",
                tags=["extraction", "pattern-matching", "quote-number"]
            )
        ]
        
        super().__init__(
            name="DocumentAgent",
            description="Handles Google Drive PDF access and quote number extraction using Playwright MCP",
            port=8003,
            skills=skills
        )
        
        self.mcp_toolset = None
    
    async def initialize(self):
        """Initialize the Document agent with MCP toolset."""
        await super().initialize()
        
        # Initialize MCP toolset for Playwright
        from google.adk.tools.mcp_tool.mcp_toolset import (
            MCPToolset,
            StdioConnectionParams,
            StdioServerParameters,
        )
        
        try:
            self.mcp_toolset = MCPToolset(
                connection_params=StdioConnectionParams(
                    server_params=StdioServerParameters(
                        command=MCP_CONFIG["playwright_server_path"],
                        args=MCP_CONFIG["playwright_server_args"]
                    )
                )
            )
            # await self.mcp_toolset.initialize()
            self.logger.info("MCP Playwright toolset initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize MCP toolset: {str(e)}")
            raise
    
    async def process_task(self, request: TaskRequest) -> TaskResponse:
        """Process document-related tasks."""
        try:
            context = request.context or {}
            action = context.get("action", "")
            
            if action == "extract_quote_number":
                quote_number = await self._extract_quote_number()
                
                if quote_number:
                    return TaskResponse(
                        id=request.id,
                        status="success",
                        data={"quote_number": quote_number},
                        message=f"Successfully extracted quote number: {quote_number}"
                    )
                else:
                    return TaskResponse(
                        id=request.id,
                        status="error",
                        error="Failed to extract quote number from PDF"
                    )
            
            else:
                return TaskResponse(
                    id=request.id,
                    status="error",
                    error=f"Unknown action: {action}"
                )
                
        except Exception as e:
            self.logger.error(f"Error processing document task: {str(e)}")
            return TaskResponse(
                id=request.id,
                status="error",
                error=str(e)
            )
    
    async def _extract_quote_number(self) -> Optional[str]:
        """Extract quote number from the PDF file."""
        try:
            # Step 1: Navigate to Google Drive folder
            await self._navigate_to_google_drive()
            
            # Step 2: Open the PDF file
            await self._open_pdf_file()
            
            # Step 3: Extract quote number from PDF content
            quote_number = await self._find_quote_number()
            
            return quote_number
            
        except Exception as e:
            self.logger.error(f"Error extracting quote number: {str(e)}")
            return None
    
    async def _navigate_to_google_drive(self):
        """Navigate to the Google Drive folder."""
        try:
            # Open Google Drive folder in new tab
            await self.mcp_toolset.call_tool(
                "playwright_navigate",
                {"url": GOOGLE_DRIVE_CONFIG["url"]}
            )
            
            # Wait for page to load
            await asyncio.sleep(3)
            
            self.logger.info("Successfully navigated to Google Drive folder")
            
        except Exception as e:
            self.logger.error(f"Error navigating to Google Drive: {str(e)}")
            raise
    
    async def _open_pdf_file(self):
        """Open the specific PDF file."""
        try:
            # Look for the PDF file by name
            pdf_selector = f"[title*='{GOOGLE_DRIVE_CONFIG['pdf_name']}']"
            
            # Wait for the file to be visible
            await asyncio.sleep(2)
            
            # Click on the PDF file
            await self.mcp_toolset.call_tool(
                "playwright_click",
                {"selector": pdf_selector}
            )
            
            # Wait for PDF to load
            await asyncio.sleep(5)
            
            self.logger.info(f"Successfully opened PDF file: {GOOGLE_DRIVE_CONFIG['pdf_name']}")
            
        except Exception as e:
            self.logger.error(f"Error opening PDF file: {str(e)}")
            raise
    
    async def _find_quote_number(self) -> Optional[str]:
        """Find and extract the quote number from the PDF content."""
        try:
            # Get the page content
            page_content = await self.mcp_toolset.call_tool(
                "playwright_get_text",
                {"selector": "body"}
            )
            
            content_text = page_content.get("text", "")
            
            # Look for quote number patterns
            quote_patterns = [
                r"quote\s*(?:number|#|no\.?)\s*:?\s*([A-Z0-9\-]+)",
                r"quote\s*([A-Z0-9\-]+)",
                r"Q-\d+",
                r"QTE-\d+",
                r"QUOTE\s*([A-Z0-9\-]+)"
            ]
            
            for pattern in quote_patterns:
                matches = re.findall(pattern, content_text, re.IGNORECASE)
                if matches:
                    quote_number = matches[0] if isinstance(matches[0], str) else matches[0][0]
                    self.logger.info(f"Found quote number: {quote_number}")
                    return quote_number
            
            # If no pattern matches, try to find any alphanumeric sequence that looks like a quote
            # This is a fallback approach
            potential_quotes = re.findall(r"\b[A-Z]{1,3}-?\d{4,8}\b", content_text)
            if potential_quotes:
                quote_number = potential_quotes[0]
                self.logger.info(f"Found potential quote number: {quote_number}")
                return quote_number
            
            self.logger.warning("No quote number found in PDF content")
            return None
            
        except Exception as e:
            self.logger.error(f"Error finding quote number: {str(e)}")
            return None


if __name__ == "__main__":
    agent = DocumentAgent()
    asyncio.run(agent.initialize())
    agent.run()
