"""Script to run individual agents for testing."""

import asyncio
import sys
import argparse
import logging

from agents.planner_agent import PlannerAgent
from agents.salesforce_agent import SalesforceAgent
from agents.document_agent import DocumentAgent
from agents.american_integrity_agent import AmericanIntegrityAgent


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("single_agent_runner")


AGENTS = {
    "planner": PlannerAgent,
    "salesforce": SalesforceAgent,
    "document": DocumentAgent,
    "american_integrity": AmericanIntegrityAgent
}


async def run_agent(agent_name: str):
    """Run a single agent."""
    if agent_name not in AGENTS:
        logger.error(f"Unknown agent: {agent_name}")
        logger.info(f"Available agents: {list(AGENTS.keys())}")
        return
    
    try:
        # Create and initialize the agent
        agent_class = AGENTS[agent_name]
        agent = agent_class()
        
        logger.info(f"Initializing {agent_name} agent...")
        await agent.initialize()
        
        logger.info(f"Starting {agent_name} agent server on port {agent.port}")
        agent.run()
        
    except KeyboardInterrupt:
        logger.info(f"{agent_name} agent stopped by user")
    except Exception as e:
        logger.error(f"Error running {agent_name} agent: {str(e)}")
        raise


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Run a single agent for testing")
    parser.add_argument(
        "agent",
        choices=list(AGENTS.keys()),
        help="Name of the agent to run"
    )
    
    args = parser.parse_args()
    
    try:
        asyncio.run(run_agent(args.agent))
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
    except Exception as e:
        logger.error(f"Application error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
