import cv2
import numpy as np

def detect_x_in_images(template_path, image_paths, threshold=0.6):
    """
    Enhanced X detection with multi-scale matching and preprocessing
    """
    # Load template image
    template = cv2.imread(template_path, 0)
    if template is None:
        raise ValueError("Could not load template image")
    
    # Preprocess template - extract X shape better
    template = preprocess_image(template)
    
    results = []
    
    for img_path in image_paths:
        img = cv2.imread(img_path, 0)
        if img is None:
            print(f"Could not load image: {img_path}")
            results.append(False)
            continue
        
        # Try multiple detection methods
        detected = detect_x_multiscale(img, template, threshold)
        results.append(detected)
        
        print(f"Image: {img_path.split('/')[-1]} | Detected: {detected}")
    
    return results

def preprocess_image(img):
    """Enhance image for better X detection"""
    # Apply threshold to get clean binary image
    _, binary = cv2.threshold(img, 127, 255, cv2.THRESH_BINARY)
    
    # Remove noise
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
    binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
    
    return binary

def detect_x_multiscale(img, template, threshold):
    """Multi-scale template matching for X detection"""
    img_processed = preprocess_image(img)
    
    # Try different scales
    scales = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0]
    max_confidence = 0
    
    for scale in scales:
        # Resize template
        width = int(template.shape[1] * scale)
        height = int(template.shape[0] * scale)
        
        if width < 5 or height < 5 or width > img.shape[1] or height > img.shape[0]:
            continue
            
        scaled_template = cv2.resize(template, (width, height))
        
        # Template matching
        result = cv2.matchTemplate(img_processed, scaled_template, cv2.TM_CCOEFF_NORMED)
        _, max_val, _, _ = cv2.minMaxLoc(result)
        
        max_confidence = max(max_confidence, max_val)
    
    # Also try with original grayscale (without preprocessing)
    result = cv2.matchTemplate(img, template, cv2.TM_CCOEFF_NORMED)
    _, max_val, _, _ = cv2.minMaxLoc(result)
    max_confidence = max(max_confidence, max_val)
    
    print(f"  Max confidence: {max_confidence:.3f}")
    return max_confidence >= threshold

def detect_x_shape_features(img, min_area=10, max_area=1000):
    """Alternative method: detect X by shape features"""
    # Preprocess
    processed = preprocess_image(img)
    
    # Find contours
    contours, _ = cv2.findContours(processed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    for contour in contours:
        area = cv2.contourArea(contour)
        if min_area < area < max_area:
            # Check if contour resembles an X shape
            # Simple check: bounding box aspect ratio close to 1:1
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = w / h if h > 0 else 0
            
            if 0.7 <= aspect_ratio <= 1.3:  # Nearly square
                return True
    
    return False

def combined_x_detection(template_path, image_paths, threshold=0.5):
    """Combined approach using both template matching and shape detection"""
    template = cv2.imread(template_path, 0)
    if template is None:
        raise ValueError("Could not load template image")
    
    template = preprocess_image(template)
    results = []
    
    for img_path in image_paths:
        img = cv2.imread(img_path, 0)
        if img is None:
            results.append(False)
            continue
        
        # Method 1: Multi-scale template matching
        method1 = detect_x_multiscale(img, template, threshold)
        
        # Method 2: Shape-based detection
        method2 = detect_x_shape_features(img)
        
        # Combine results (either method detects X)
        detected = method1 or method2
        results.append(detected)
        
        print(f"Image: {img_path.split('/')[-1]} | Template: {method1} | Shape: {method2} | Final: {detected}")
    
    return results

# Example usage
if __name__ == "__main__":
    # Paths to your images
    template_image = r"C:\Users\<USER>\Downloads\images.png"  # Your X template
    test_images = [
        r"E:\loveable_AI\bunch\Image_Detection_opencv\input_images\Screenshot 2025-09-02 162944.png",
        r"E:\loveable_AI\bunch\Image_Detection_opencv\input_images\Screenshot 2025-09-02 163244.png",
        r"E:\loveable_AI\bunch\Image_Detection_opencv\input_images\Screenshot 2025-09-02 215500.png",
        r"E:\loveable_AI\bunch\Image_Detection_opencv\input_images\Screenshot 2025-09-02 215516.png"
    ]
    
   # Try the improved detection
    print("=== Multi-scale Template Matching ===")
    detections1 = detect_x_in_images(template_image, test_images, threshold=0.4)
    
    print("\n=== Combined Detection Method ===")
    detections2 = combined_x_detection(template_image, test_images, threshold=0.4)
    
    print(f"\nFinal Results: {detections2}")


