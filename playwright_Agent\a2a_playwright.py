import asyncio
import logging
from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from pydantic import BaseModel
import uuid
from a2a.types import Agent<PERSON>ard, AgentCapabilities,AgentSkill  # From a2a-python SDK
# from a2a.types import A2AServer  # Assuming HTTP extra for server utils
from agent import PlaywrightAgent  # Import from the provided agent.py

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Playwright A2A Server")


skill = AgentSkill(
    id="browser_automation",
    name="Browser Automation",
    description="Use Playwright to browse pages, extract data, fill forms",
    tags=["browser", "automation", "web-scraping"]
    # optionally you can add examples, inputModes, outputModes
)


# Define AgentCard using a2a-sdk
AGENT_CARD = AgentCard(
    name="PlaywrightAgent",
    description="Browser automation agent using Playwright for tasks like Salesforce data extraction and form filling.",
    url="http://localhost:8000",  # Adjust to your server URL
    version="1.0",
    defaultInputModes=["text"],
    defaultOutputModes=["text"],
    capabilities=AgentCapabilities(
        streaming=False,
        pushNotifications=False
    ),
    skills=[skill]
)

class TaskRequest(BaseModel):
    id: str
    message: dict  # {'role': 'user', 'parts': [{'text': str}]}

# Initialize the agent once (async context)
agent = PlaywrightAgent()

@app.on_event("startup")
async def startup_event():
    await agent.initialize()

@app.on_event("shutdown")
async def shutdown_event():
    await agent.cleanup()

@app.get("/.well-known/agent.json")
async def get_agent_card():
    return AGENT_CARD.dict()  # Serialize to JSON

@app.post("/tasks/send")
async def handle_task(request: Request):
    try:
        task_data = await request.json()
        task_id = task_data.get("id", str(uuid.uuid4()))
        user_message = task_data.get("message", {}).get("parts", [{}])[0].get("text", "")
        
        if not user_message:
            raise HTTPException(status_code=400, detail="Invalid task message")
        
        # Invoke the agent asynchronously
        result = await agent.invoke_user_input(user_message)
        
        if not result.get("status"):
            raise HTTPException(status_code=500, detail=result.get("message", "Agent error"))
        
        agent_reply = result.get("data", "No response")
        
        # A2A-compliant response
        response = {
            "id": task_id,
            "status": {"state": "completed"},
            "messages": [
                task_data.get("message", {}),
                {
                    "role": "agent",
                    "parts": [{"text": agent_reply}]
                }
            ]
        }
        return response
    
    except Exception as e:
        logger.error(f"Task error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)