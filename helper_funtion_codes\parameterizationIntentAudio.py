import json
import os
from datetime import datetime
import re
import asyncio
import aiofiles
from typing import Dict, <PERSON><PERSON>, Any
import google.generativeai as genai
from playwright_Agent.agent import PlaywrightAgent
import sys
import aiohttp


class AsyncVideoFrameAnalyzer:
    """An async class for analyzing video frame data and generating parameterized JSON outputs."""
    
    def __init__(self, api_key: str = None):
        """
        Initialize the AsyncVideoFrameAnalyzer.
        
        Args:
            api_key (str, optional): Google GenAI API key. If None, expects GOOGLE_API_KEY env var.
        """
        self.frame_id = None  # Will be set when processing specific frame
        self.model = None
        self.agent = PlaywrightAgent()
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.incVal = 0
        
        if api_key:
            genai.configure(api_key=api_key)
        else:
            # Expects GOOGLE_API_KEY environment variable or manual configuration
            genai.configure(api_key=os.getenv('GOOGLE_API_KEY', 'GOOGLE_API_KEY'))
        
        self.model = genai.GenerativeModel('gemini-1.5-flash')
    
    async def load_json_file(self, file_path: str) -> Dict[str, Any]:
        """
        Asynchronously load JSON data from a file.
        
        Args:
            file_path (str): Path to the JSON file.
            
        Returns:
            dict: Loaded JSON data or error dict.
        """
        try:
            async with aiofiles.open(file_path, 'r') as file:
                content = await file.read()
                return json.loads(content)
        except FileNotFoundError:
            return {"error": f"File not found: {file_path}"}
        except json.JSONDecodeError as e:
            return {"error": f"Invalid JSON in file {file_path}: {e}"}
    
    def extract_frame_id_from_context(self, context_data: Dict[str, Any]) -> str:
        """
        Extract frame ID from context data or file names.

        Args:
            context_data (dict): Context data containing file information

        Returns:
            str: Frame ID (e.g., "0000", "0001")
        """
        # Try to extract from file names in the context
        if isinstance(context_data, list) and len(context_data) > 0:
            first_item = context_data[0]
            if isinstance(first_item, dict) and 'file_details' in first_item:
                filename = first_item['file_details'].get('file_name', '')
                # Extract frame ID from ui_elements_XXXX.yaml or ui_diff_XXXX_to_YYYY.yaml
                import re
                pattern = r'(?:ui_elements_|ui_diff_)(\d{4})'
                match = re.search(pattern, filename)
                if match:
                    return match.group(1)

        return "0000"  # Default frame ID

    async def save_json_file(self, data: Dict[str, Any], filename: str, frame_id: str = None) -> Dict[str, Any]:
        """
        Asynchronously save data to a JSON file with frame-based naming.

        Args:
            data (dict): Data to save.
            filename (str): Base name of the file.
            frame_id (str): Frame ID to use in filename

        Returns:
            dict: Success status or error dict.
        """
        try:


            # timestamp_folder = datetime.now().strftime("%Y-%m-%d_%H%M%S")

        # Create full folder path
            folder_path = os.path.join(os.getcwd(), self.timestamp)
            os.makedirs(folder_path, exist_ok=True)

            # Use frame_id if provided, otherwise extract from data or use default
            if frame_id is None:
                frame_id = self.extract_frame_id_from_context(data)

            # Create filename with frame ID
            name_parts = filename.rsplit('.', 1)
            if len(name_parts) == 2:
                base_name, extension = name_parts
                final_filename = f"{base_name}_.{extension}"
            else:
                final_filename = f"{filename}_"

            # Save to current directory (no subfolder)
            file_path = os.path.join(folder_path, final_filename)

            # Save JSON asynchronously
            async with aiofiles.open(file_path, 'w') as file:
                await file.write(json.dumps(data, indent=2))
            return {"success": f"File saved: {final_filename}"}
        except Exception as e:
            return {"error": f"Failed to save {final_filename}: {e}"}
    
    def extract_json_from_response(self, response_text: str) -> str:
        """
        Extract JSON from response text that may contain markdown code blocks.
        
        Args:
            response_text (str): Response text from the AI model.
            
        Returns:
            str: Extracted JSON text.
        """
        # Remove markdown code block markers
        # Look for ```json...``` or ```...``` patterns
        json_pattern = r'```(?:json)?\s*(.*?)\s*```'
        match = re.search(json_pattern, response_text, re.DOTALL)
        
        if match:
            json_text = match.group(1).strip()
        else:
            # If no code blocks found, use the entire response
            json_text = response_text.strip()
        
        return json_text
    
    async def process_with_genai(self, prompt: str) -> Dict[str, Any]:
        """
        Asynchronously process data with Google GenAI using the given prompt.
        
        Args:
            prompt (str): The prompt to send to the AI model.
            
        Returns:
            dict: Parsed JSON response from the model or error dict.
        """
        if not self.model:
            return {"error": "Model not initialized. Please provide a valid API key."}
        
        # Run the AI model call in a thread pool to avoid blocking
        loop = asyncio.get_event_loop()
        
        try:
            response = await loop.run_in_executor(
                None, 
                self.model.generate_content, 
                prompt
            )
            print("Response received from AI model")
            
            # Extract JSON from the response text
            json_text = self.extract_json_from_response(response.text)
            print("response json text >>> ",json_text)
            print("incVal",self.incVal)
            self.incVal = self.incVal + 1

            # Parse JSON in executor to avoid blocking
            parsed_json = await loop.run_in_executor(
                None,
                json.loads,
                json_text
            )
            
            return parsed_json
            
        except json.JSONDecodeError as e:
            return {"error": f"Error parsing JSON: {e}"}
        except Exception as e:
            return {"error": f"Error in AI processing: {e}"}
    
    def generate_analysis_prompt(self, frame_data: Dict[str, Any]) -> str:
        """
        Generate the analysis prompt for video frame data.
        
        Args:
            frame_data (dict): Video frame data to analyze.
            
        Returns:
            str: Formatted analysis prompt.
        """
        audio_json ={
    "language": "en",
    "word_timeline": {
        "1.0": [
            "Quick",
            "video",
            "here"
        ],
        "2.0": [
            "for",
            "how",
            "to"
        ],
        "3.0": [
            "locate",
            "an"
        ],
        "4.0": [
            "American"
        ],
        "5.0": [
            "Integrity"
        ],
        "6.0": [
            "quote."
        ],
        "7.0": [
            "So",
            "I'm",
            "at",
            "the",
            "American"
        ],
        "8.0": [
            "Integrity",
            "homepage."
        ],
        "10.0": [
            "And",
            "let's"
        ],
        "11.0": [
            "say",
            "I",
            "wanted",
            "to",
            "find"
        ],
        "12.0": [
            "this"
        ],
        "13.0": [
            "quote",
            "here."
        ],
        "14.0": [
            "I",
            "would",
            "highlight"
        ],
        "15.0": [
            "this",
            "quote"
        ],
        "16.0": [
            "number",
            "and",
            "then",
            "I'm",
            "going"
        ],
        "17.0": [
            "to",
            "copy",
            "that."
        ],
        "19.0": [
            "I'm",
            "going",
            "to",
            "paste"
        ],
        "20.0": [
            "it",
            "in",
            "this",
            "search"
        ],
        "21.0": [
            "bar",
            "up",
            "here."
        ],
        "23.0": [
            "And"
        ],
        "24.0": [
            "then",
            "when",
            "I",
            "hit",
            "search"
        ],
        "25.0": [
            "it",
            "will",
            "take",
            "me,",
            "if",
            "I'm"
        ],
        "26.0": [
            "using",
            "that",
            "quote",
            "number,"
        ],
        "27.0": [
            "it",
            "will",
            "take",
            "me",
            "right"
        ],
        "28.0": [
            "into",
            "the",
            "quote."
        ],
        "29.0": [
            "So",
            "this",
            "is",
            "where"
        ],
        "30.0": [
            "I"
        ],
        "31.0": [
            "was",
            "editing"
        ],
        "32.0": [
            "and",
            "making",
            "changes."
        ],
        "33.0": [
            "This",
            "is",
            "the",
            "first"
        ],
        "35.0": [
            "page"
        ],
        "36.0": [
            "of"
        ],
        "37.0": [
            "a",
            "few.",
            "We've",
            "got",
            "policy,"
        ],
        "38.0": [
            "dwelling,"
        ],
        "39.0": [
            "and",
            "then"
        ],
        "40.0": [
            "review."
        ],
        "41.0": [
            "And",
            "so",
            "we"
        ],
        "42.0": [
            "can",
            "move"
        ],
        "43.0": [
            "through",
            "our",
            "quote",
            "here."
        ],
        "47.0": [
            "And",
            "then"
        ],
        "48.0": [
            "when",
            "the",
            "quote"
        ],
        "49.0": [
            "is",
            "filled",
            "out,"
        ],
        "50.0": [
            "you",
            "don't"
        ],
        "51.0": [
            "even",
            "have",
            "to",
            "be"
        ],
        "52.0": [
            "on",
            "the",
            "last",
            "page."
        ],
        "53.0": [
            "You",
            "can",
            "just",
            "hit",
            "the",
            "create"
        ],
        "54.0": [
            "application"
        ],
        "55.0": [
            "button"
        ],
        "56.0": [
            "up",
            "here",
            "to",
            "turn",
            "it"
        ],
        "57.0": [
            "into",
            "an",
            "application."
        ]
    },
    "success": True
}
        return f"""
You are an expert AI assistant specializing in multi-modal process discovery and documentation. Your primary function is to analyze user interaction recordings composed of:

1. **YAML Analysis Data**: UI state snapshots with timestamps and detailed UI descriptions
2. **Audio Timeline Data**: Word-by-word transcription with precise timestamps

## Input Data Available:

**YAML Analysis Data:**
{json.dumps(frame_data)}

**Audio Timeline Data:**  
{json.dumps(audio_json, indent=2)}

## Core Integration Strategy

**Time Synchronization Rules:**
- Map `time_in_seconds` from YAML analysis to corresponding `word_timeline` entries from audio data
- When YAML analysis has gaps, use audio timeline to infer missing user actions
- When audio timeline lacks technical details, use YAML analysis UI descriptions to fill context
- Create seamless step progression by merging both data sources chronologically

**Data Compensation Logic:**
- If YAML analysis shows UI state at time X but no explicit action, check audio timeline around time X for user narration explaining the action
- If audio mentions an action but YAML analysis doesn't capture the UI change, create a step based on audio context with estimated UI interaction details
- Prioritize explicit UI changes from YAML analysis, but enhance with audio context for user intent

## Critical Analysis Rules

**Browser Navigation Detection:**
1. **First-time URL appearance**: When a URL appears for the first time in YAML analysis, treat as new tab navigation
2. **Explicit tab switches**: Only record tab switches when `ai_analysis` explicitly mentions "switched from tab" or "user switched"
3. **Context changes**: When UI completely changes between YAML entries, combine with audio timeline to determine if it's navigation or interaction

**Step Creation Priority:**
1. Browser navigation actions (new tabs, tab switches, URL changes)
2. Audio-narrated user intentions and planned actions
3. UI element interactions (clicks, typing, selections)
4. Form submissions and workflow completions

**Audio-UI Synchronization:**
- Match audio timestamps to YAML `time_in_seconds` with ±2 second tolerance
- Use audio narration to explain UI changes that appear sudden in YAML analysis
- Extract user intent from audio even when UI analysis is purely descriptive

##  Step Documentation

For each step, include:
- **Audio Context**: Direct quotes from audio timeline explaining user intent
- **Timestamp Correlation**: Show both YAML timing and audio timing
- **Gap Compensation**: Note when audio fills YAML gaps or vice versa
- **Multi-modal Validation**: Cross-reference audio description with UI state changes

**Step Format Template:**
```json
{{
  "step_number": X,
  "action": "Clear description combining UI change with user intent from audio",
  "details": {{
    "target_element": "UI element from YAML analysis or inferred from audio",
    "input_value": "Actual value from YAML or described in audio",
    "cursor_position": [x, y],
    "page_url": "URL from YAML analysis",
    "audio_context": "Direct quote from audio timeline explaining this action",
    "timestamp": "YAML_time seconds (UI analysis), audio_start-audio_end seconds (audio)"
  }}
}}
```

## Output Requirements

Return **ONLY** a valid JSON object with this exact structure:

```json
{{
  "intent": "High-level goal derived from complete audio narration",
  "action_summary": "Comprehensive summary combining UI changes with audio explanation",
  "steps": [
    {{
      "step_number": 1,
      "action": "Clear description including browser navigation and specific values entered",
      "details": {{
        "target_element": "UI element interacted with (browser_tab, address_bar, button, etc.)",
        "input_value": "Actual value entered by user (if applicable)",
        "cursor_position": [x, y],
        "page_url": "URL when action occurred (must include correct protocol)",
        "audio_context": "Direct quote from audio timeline explaining this action",
        "timestamp": "YAML_time seconds (UI analysis), audio_start-audio_end seconds (audio)"
      }}
    }}
  ],

}}
```

## Key Enhancement Features

1. **Temporal Fusion**: Merge timestamped data sources for complete timeline
2. **Context Enrichment**: Use audio narration to explain UI state changes  
3. **Intent Extraction**: Derive user goals from audio descriptions
4. **Gap Bridging**: Fill missing information using complementary data source
5. **Validation Cross-check**: Verify actions against both UI and audio evidence

**Critical Success Factors:**
- Every step must be supported by evidence from at least one data source
- Audio context should explain WHY actions were taken, not just WHAT
- UI analysis should provide precise technical details of interactions
- Timeline must be chronologically consistent across both data sources
- Missing data in one source should be compensated by the other source

Now analyze the provided YAML analysis data and audio timeline data to create comprehensive multi-modal process documentation following the enhanced integration strategy.
"""
    
    def generate_parameterization_prompt(self, result_json: Dict[str, Any]) -> str:
        """
        Generate the parameterization prompt for the result JSON.
        
        Args:
            result_json (dict): The result JSON to parameterize.
            
        Returns:
            str: Formatted parameterization prompt.
        """
        return f"""
        You are a JSON templating assistant. Your job: take a valid JSON document as input and replace specific *data values* with parameter placeholders (using `<<name>>` mustache-style). Do NOT change the JSON structure (keys, arrays, numeric types), only replace string values that match the rules below. Output must be valid JSON and nothing else.

        INPUT
        - You will be given one JSON object as input (the full object will be provided after this instruction).
        - Example fields to parameterize: emails, passwords, visible user inputs (brand names, product types, model names, statuses), page URLs, notification texts, and any user-supplied strings inside `action` or `details`. Cursor positions, element ids, step_number (integers) should be preserved unless explicitly requested.

        REPLACEMENT RULES (apply in this priority)
        1. **Email replacement**
        - Replace any string matching an email regex with `<<email>>`.
        - Regex: `\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{{2,}}\b`.
        - If multiple *distinct* emails appear, name them `<<email1>>`, `<<email2>>`, ... in order of appearance and reflect that in the parameters mapping.

        2. **Password replacement**
        - Replace any password value that is explicitly a user password with `<<password>>`.
        - If multiple passwords present, name them `<<password1>>`, `<<password2>>`, ...
        - If masked (e.g., `****`), leave unchanged.

        3. **User input / dropdown / text field values — CONTEXT-AWARE NAMING**
        - Identify the related UI element from `details.target_element` or `action` text.
        - Create the placeholder name from the element label or name in lower_snake_case.
            Examples:
            - `"Dropdown: Brand"` → `<<brand>>`
            - `"Dropdown: Product Type"` → `<<product_type>>`
            - `"Text Input: Model Name"` → `<<model_name>>`
            - `"Dropdown: Status"` → `<<status>>`
            - `"Button: Submit"` → `<<submit_button_label>>`
        - If the same element name appears multiple times with different values, suffix with `_1`, `_2` as needed.


        4. **General rule for other visible user-supplied strings**
        - For any other literal string that is clearly user-supplied, base the placeholder name on the UI element name if available, otherwise use a descriptive short name.
        - Always prefer meaningful placeholder names tied to UI context over generic numbered names.

        NAMING / CONSISTENCY
        - Always use lower_snake_case inside `<<>>`.
        - Reuse the same placeholder for identical exact string values (case-sensitive).
        - Number placeholders only when the same field appears multiple times with different values.
        - Avoid meaningless names like `brand_name2`; always derive from the UI element name or label.

        OUTPUT FORMAT (exact)
        Return a JSON object with two top-level fields:
        1. `"parameterized_json"` — the original JSON but with replacements made.
        2. `"parameters"` — an object mapping placeholder names to original values.

        DO NOT OUTPUT ANY EXTRA TEXT. Return valid JSON only.

        PROCESS THE FOLLOWING JSON INPUT NOW. Produce only the two-field JSON described above.
        """ + json.dumps(result_json)

    
    async def analyze_video_frames(self, frame_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Asynchronously analyze video frame data and generate structured JSON output.
        
        Args:
            frame_data (dict): Video frame data to analyze.
            
        Returns:
            dict: Analysis result JSON.
        """
        analysis_prompt = self.generate_analysis_prompt(frame_data)
        return await self.process_with_genai(analysis_prompt)
    
    async def parameterize_result(self, result_json: Dict[str, Any]) -> Dict[str, Any]:
        """
        Asynchronously parameterize the analysis result JSON.
        
        Args:
            result_json (dict): The analysis result to parameterize.
            
        Returns:
            dict: Success result with (parameterized_json, parameters) or error dict.
        """
        parameterization_prompt = self.generate_parameterization_prompt(result_json)
        parameterized_result = await self.process_with_genai(parameterization_prompt)
        
        if "error" in parameterized_result:
            return parameterized_result
        
        parameterized_json = parameterized_result.get('parameterized_json', {})
        parameters = parameterized_result.get('parameters', {})
        
        return (parameterized_json, parameters)
    
    # def generate_agent_creation_prompt(self, parameterized_json: Dict[str, Any], parameters: Dict[str, Any]) -> str:
    #     """
    #     Generate the agent creation prompt for creating agentCreationJson.
        
    #     Args:
    #         parameterized_json (dict): The parameterized JSON from earlier flow.
    #         parameters (dict): The parameters from earlier flow.
            
    #     Returns:
    #         str: Formatted agent creation prompt.
    #     """
    # # Escape the JSON strings properly for embedding in the prompt
    #     # parameterized_json_str = json.dumps(parameterized_json, indent=2).replace('\\', '\\\\').replace('"', '\\"')
    #     # parameters_str = json.dumps(parameters, indent=2).replace('\\', '\\\\').replace('"', '\\"')
    #     parameterized_json_str = json.dumps(json.dumps(parameterized_json, separators=(',', ':')), separators=(',', ':'))[1:-1]
    #     parameters_str = json.dumps(json.dumps(parameters, separators=(',', ':')), separators=(',', ':'))[1:-1]
    
    #     return f"""
    # You are an AI assistant specialized in creating agent configuration JSON based on user intent analysis. Your task is to analyze the provided parameterized JSON and parameters to create a comprehensive agent creation configuration.

    # INPUT DATA:
    # - Parameterized JSON: {json.dumps(parameterized_json, indent=2)}
    # - Parameters: {json.dumps(parameters, indent=2)}

    # ANALYSIS REQUIREMENTS:
    # 1. **Analyze the user intent** from the parameterized JSON to understand:
    # - What type of workflow/process is being automated
    # - What website/application is involved
    # - What the main action/goal is

    # 2. **Generate Agent Details** based on the analysis:
    # - **name**: Create a concise, descriptive name (e.g., "SalesforceLoginAgent", "EquipmentManagementAgent") without spaces and in camel or pascal case
    # - **description**: A suffix that is "You are a **Playwright automation agent** integrated with **MCP (Model Context Protocol)**,Your job is to" + A clear description of what the agent does
    # - **server_endpoint**: A lowercase, Hypen-separated endpoint name based on the agent name
    # - **instructions**: A comprehensive instruction set with the FULL parameterized JSON and parameters embedded

    # 3. **Generate Card Details** based on the same analysis:
    # - **name**: Same as agent name or user-friendly version
    # - **description**: User-facing description of the agent's capabilities

    # 4. **Generate Category Details** based on the same analysis:
    # - **name**: Same as agent name but with an different kind
    # - **description**:  as agent description of the agent's capabilities

    # INSTRUCTION CREATION RULES:
    #     1. **Read the incoming user request**.
    #     2. **Extract the following parameters** (case-insensitive):

    #     ```
    #     salutation, first_name, last_name, company
    #     ```
    #     3. **Merge extracted values** into the template JSON by replacing placeholders `<<salutation>>`, `<<first_name>>`, `<<last_name>>`, `<<company>>`.
    #     4. **Keep Salesforce URLs and login credentials static** as in the template.
    #     5. **Pass the final JSON to the Playwright MCP tool** for execution, ensuring each step is performed in sequence.

    #     CRITICAL: Ensure all quotes and special characters in the instructions field are properly escaped for JSON formatting. Escape double quotes as \" and newlines as \\n if necessary to ensure valid JSON.

    #     INSTRUCTION TEMPLATE (use proper JSON string escaping):
    #     "You are a Playwright automation agent. Your role is to follow the instructions in Parameterized JSON exactly, filling in placeholders with values from Parameters. 1. First, read the incoming user request. 2. Extract values for the relevant keys (case-insensitive). 3. Check if the user has mentioned any value that corresponds to a key or any value that matched the key for eg: My name is Dhanush in the user instruction then the <<first_name>> fill with the value from the user instruction which replaces the value for <<first_name>> from the Parameters. 4.So merge the updated Parameters values into Parameterized JSON by replacing all placeholders if it something provided in the user instruction then replace the value with that. 5. The final output must be a fully resolved JSON object containing the updated intent and steps for execution."
    #     the parameterized JSON is {parameterized_json_str}
    #     the parameters is {parameters_str}
    #     ### Example User Instruction Query

    #     ```
    #     Create a new Salesforce lead with salutation Mr., first name John, last name Doe, company Acme Corp.
    #     ```

    #     **Extracted values:**

    #     \"salutation\": \"Mr.\",
    #     \"first_name\": \"John\",
    #     \"last_name\": \"Doe\",
    #     \"company\": \"Acme Corp\"
        

    #     **Agent Action:**

    #     * Merge extracted values into placeholders if mentioned otherwise use values from the parameters.
    #     * Execute each step in sequence using Playwright MCP.

    #     ```

    # OUTPUT REQUIREMENTS:
    # Return ONLY a valid JSON object with this exact structure (no markdown, no explanation):

    # {{
    # "agent": {{
    #     "name": "<generated_name>",
    #     "description": "<generated_description>",
    #     "instructions": "<properly_escaped_instruction_text>",
    #     "model": "gemini/gemini-1.0-flash-exp",
    #     "tools": [],
    #     "mcp_tools": ["6895c920819b03d4d1279b71"],
    #     "server_endpoint": "<generated_endpoint>",
    #     "agent_type": "sub_agent"
    # }},
    # "card": {{
    #     "name": "<generated_name>",
    #     "description": "<generated_card_description>",
    #     "url": "",
    #     "version": "1.0.0",
    #     "skills": []
    # }},
    # "category":{{
    #     "name": "<generated_category_name>",
    #     "description": "<generated_category_description>"
    # }}
    # }}

    # IMPORTANT: 
    # 1. Ensure all string values are properly quoted and escaped
    # 2. No unescaped newlines in JSON strings
    # 3. All quotes within string values must be escaped with backslashes
    # 4. The instructions field should be a single line JSON string with proper escaping

    # Now analyze the provided data and generate the agent creation JSON.
    # """

    
    def generate_agent_creation_prompt(self, parameterized_json: Dict[str, Any], parameters: Dict[str, Any]) -> str:
        """
        Produce a robust agent-creation prompt string. The returned string contains:
        - human-readable pretty JSON copies for context
        - a single-line instruction template that the assistant should embed (already prepared but still needs escaping in final agent)
        - compact escaped JSON blobs for embedding in the instructions field
        """

    # Pretty (human readable) for the INPUT DATA section
        parameterized_json_pretty = json.dumps(parameterized_json, indent=2)
        parameters_pretty = json.dumps(parameters, indent=2)

        # Compact forms for embedding
        parameterized_json_compact = json.dumps(parameterized_json, separators=(',', ':'))
        parameters_compact = json.dumps(parameters, separators=(',', ':'))

        # Escaping for embedding inside a JSON string value:
        # - Escape backslashes first to avoid double-escape issues
        def json_string_escape(s: str) -> str:
            return s.replace('\\', '\\\\').replace('"', '\\"').replace('\n', '\\n')

        parameterized_json_compact_escaped = json_string_escape(parameterized_json_compact)
        parameters_compact_escaped = json_string_escape(parameters_compact)

        # Compute extraction keys: union of parameter keys and placeholders found in parameterized_json
        # Placeholder patterns: {word}, <<word>>, {{word}}, or {word.with.dots}
        placeholder_pattern = re.compile(r'<<\s*([^>\s]+)\s*>>|{\{\s*([^}\s]+)\s*}}|{\s*([^}\s]+)\s*}')
        found = set()
        param_keys = set(k.lower() for k in parameters.keys())
        for m in placeholder_pattern.finditer(json.dumps(parameterized_json)):
            for g in m.groups():
                if g:
                    found.add(g.lower())
        extraction_keys = sorted(found.union(param_keys))

        extraction_keys_str = ', '.join(extraction_keys) if extraction_keys else '<no keys detected>'

        # Build the prompt by filling the template (use the multi-line template from earlier)
        prompt = f"""
You are an AI assistant specialized in creating agent configuration JSON for Playwright + MCP automation based on a parameterized intent description.

INPUT DATA (human readable):
- Parameterized JSON:
{parameterized_json_pretty}
- Parameters:
{parameters_pretty}

CONTEXT & GOAL:
Analyze the parameterized JSON and the provided Parameters to produce a complete, production-ready agent creation JSON. The resulting JSON MUST follow the exact output schema specified below (agent, card, category). The most important field is "instructions" inside the agent; that must be a single-line JSON-safe string suitable for embedding as a JSON value (i.e., all double quotes escaped as \\\" and newlines escaped as \\n). No markdown or extra text is allowed in the final output — only the specified JSON object.

ANALYSIS REQUIREMENTS:
1. Determine these things from the parameterized JSON:
   - What application/website is being automated.
   - What the high-level intent / goal is.
   - What placeholders exist in the parameterized JSON.
   - Which parameters are required, optional, and any static values.

2. Extraction & Merging Rules to encode in the instructions:
   - Extraction keys: {extraction_keys_str}.
   - Matching is case-insensitive; keys may be referenced with spaces, underscores, hyphens or common synonyms.
   - Priority: user-supplied values in the incoming request override Parameters. Otherwise use Parameters.
   - Supported input patterns: key:value, key = value, free-text patterns, quoted values. Choose the last explicit mention when conflicting.
   - If a placeholder has no final value, set it to empty string ("") in the merged JSON.

3. Instruction text rules:
   - The instructions string must:
     * Describe the exact mapping algorithm (as above).
     * Include the FULL parameterized JSON and Parameters in compact escaped form: parameterized JSON (compact escaped) = "{parameterized_json_compact_escaped}" and parameters (compact escaped) = "{parameters_compact_escaped}".
     * Instruct to produce a fully-resolved JSON by replacing placeholders and then pass the merged JSON to Playwright MCP for execution.
     * Provide a short example mapping (one-liner) inside the instruction.

4. Escaping requirements (CRITICAL):
   - The generated agent creation JSON must be valid JSON.
   - The 'instructions' value must be a single-line JSON string with escaped quotes and newlines.

INSTRUCTION TEMPLATE (the assistant should produce a single-line escaped string based on this template when filling the 'instructions' field):
"You are a Playwright automation agent integrated with MCP (Model Context Protocol). Your job: parse the incoming user request, extract values for the following keys (case-insensitive): {extraction_keys_str}. Support patterns key:value, key = value, free-text mentions, and quoted values. User-supplied values override Parameters. Merge values into the parameterized JSON placeholders. If a placeholder remains unset after extraction and parameters, set it to an empty string. Then send the fully-merged JSON to the Playwright MCP tool for execution. Parameterized JSON (compact, escaped): {parameterized_json_compact_escaped}. Parameters (compact, escaped): {parameters_compact_escaped}. Example: User query \"Create a new Salesforce lead with salutation Mr., first name John, last name Doe, company Acme Corp.\" → extracted salutation=Mr., first_name=John, last_name=Doe, company=Acme Corp."

OUTPUT REQUIREMENTS:
Return ONLY one JSON object with this exact schema:

{{
  "agent": {{
    "name": "<generated_name>",
    "description": "<generated_description>",
    "instructions": "<single-line, JSON-escaped-instruction-string>",
    "model": "gemini/gemini-1.0-flash-exp",
    "tools": [],
    "mcp_tools": ["6895c920819b03d4d1279b71"],
    "server_endpoint": "<generated_endpoint>",
    "agent_type": "sub_agent"
  }},
  "card": {{
    "name": "<generated_name>",
    "description": "<generated_card_description>",
    "url": "",
    "version": "1.0.0",
    "skills": []
  }},
  "category": {{
    "name": "<generated_category_name>",
    "description": "<generated_category_description>"
  }}
}}

IMPORTANT:
1. Name rules: use camelCase or PascalCase (no spaces).
2. server_endpoint: lowercase, hyphen-separated.
3. The "instructions" field must be safe for direct embedding — escape double quotes as \\\" and newlines as \\n.
4. No unescaped newlines in JSON string values.
5. Use defaults from Parameters only if no user-supplied value exists.

Now analyze the provided input data and generate the agent creation JSON.
"""

    # trim leading whitespace and return
        return prompt.strip()
    
    async def create_agent_configuration(self, parameterized_json: Dict[str, Any], parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Asynchronously create agent configuration JSON based on parameterized results.
        
        Args:
            parameterized_json (dict): The parameterized JSON from analysis.
            parameters (dict): The parameters from analysis.
            
        Returns:
            dict: Agent configuration JSON or error dict.
        """
        agent_creation_prompt = self.generate_agent_creation_prompt(parameterized_json, parameters)
        return await self.process_with_genai(agent_creation_prompt)


    async def process_video_analysis(self, input_json_path: str, isIntent: str = None) -> Dict[str, Any]:
            """
            Complete async video analysis pipeline: load data, analyze, parameterize, and save results.
            
            Args:
                input_json_path (str): Path to input JSON file containing video frame data.
                isIntent (str): Optional parameter to control processing flow
                
            Returns:
                dict: Success result with data or error dict.
            """
            print("Starting video analysis pipeline...")
            
            # Load the raw JSON data
            print("Loading frame data...")
            frame_data = await self.load_json_file(input_json_path)
            if "error" in frame_data:
                return frame_data
            
            # Analyze video frames
            print("Analyzing video frames...")
            result_json = await self.analyze_video_frames(frame_data)
            if "error" in result_json:
                return result_json
            
            # Extract frame ID for consistent naming
            frame_id = self.extract_frame_id_from_context(frame_data)

            # Save result and start parameterization concurrently
            print("Saving initial results and parameterizing...")
            save_result_task = asyncio.create_task(
                self.save_json_file(result_json, 'user_intent.json', frame_id)
            )
            
            parameterize_task = asyncio.create_task(
                self.parameterize_result(result_json)
            )
            
            # Wait for both tasks to complete
            save_result = await save_result_task
            parameterize_result = await parameterize_task
            
            if "error" in save_result:
                return save_result
            
            if "error" in parameterize_result:
                return parameterize_result
            
            parameterized_json, parameters = parameterize_result
            
            # Save parameterized results concurrently
            print("Saving parameterized results...")
            save_tasks = [
                asyncio.create_task(self.save_json_file(parameterized_json, 'user_intent_param.json', frame_id)),
                asyncio.create_task(self.save_json_file(parameters, 'intent_parameters.json', frame_id))
            ]
            
            save_results = await asyncio.gather(*save_tasks)
            for save_result in save_results:
                if "error" in save_result:
                    return save_result
            
            print(f"Processing complete. Results saved to 'user_intent_{frame_id}.json', 'user_intent_param_{frame_id}.json', and 'intent_parameters_{frame_id}.json'")
            print(f"Processing complete. Results saved with frame ID: {frame_id}", isIntent)
            
            # if isIntent == "active":
            #     print("comes play")
            #     # Return just the intent analysis without invoking the agent
            #     try:
            #         # result_json is already a dict from the AI model, no need to parse
            #         if isinstance(result_json, dict):
            #             return result_json
            #         elif isinstance(result_json, str):
            #             return json.loads(result_json)
            #         else:
            #             return {"status": False, "error_stage": "intent_parsing", "error_message": "Unexpected result_json type"}
            #     except json.JSONDecodeError:
            #         return {"status": False, "error_stage": "intent_parsing", "error_message": "Failed to parse intent response as JSON"}
            # print("comes after check")
            # Send original response to agent (already async)


                        # Create agent configuration
            print("Creating agent configuration...")
            # agent_config_result = await self.create_agent_configuration(parameterized_json, parameters)
            # print("agent_config_result",agent_config_result)
            # if "error" in agent_config_result:
            #     return agent_config_result
            
            # if "category" not in agent_config_result:
            #     return agent_config_result
            
            # category_json = {"category": agent_config_result["category"]}
            # print("category_json",category_json)
            # # # Step 2: Remove category from original
            # data_without_category = {k: v for k, v in agent_config_result.items() if k != "category"}
            # print("data_without_category ",data_without_category)
            # # Save agent configuration
            # save_agent_config_task = asyncio.create_task(
            #     self.save_json_file(data_without_category, 'agentCreationJson.json', frame_id)
            # )
            
            # agent_save_result = await save_agent_config_task
            # if "error" in agent_save_result:
            #     return agent_save_result
            
            # print(f"Agent configuration saved to 'agentCreationJson_{frame_id}.json'")

            #     # Send agent configuration to API
            # print("Sending agent configuration to API...")
            # api_result = await self.send_agent_config_to_api(data_without_category)
            # print("api result",api_result)
            # if "error" in api_result:
            #     print(f"Warning: Failed to send agent config to API: {api_result['error']}")
            #     # You can choose whether to return the error or continue processing
            #     # For now, we'll log the warning and continue
            # else:
            #     print("Agent configuration successfully sent to API")

            # category_api_result = await self.send_agent_category_to_api(category_json["category"])
            # if "error" in category_api_result:
            #     print(f"Warning: Failed to send agent category to API: {category_api_result['error']}")
            #     # You can choose whether to return the error or continue processing
            #     # For now, we'll log the warning and continue
            # else:
            #     print("Agent category successfully sent to API")


            if isinstance(result_json, dict):
                finalresp = await self.agent.invoke_user_input(f"""{json.dumps(result_json)}""")
            else:
                finalresp = await self.agent.invoke_user_input(f"""{result_json}""")
            print("fina",finalresp)
            # If the agent returned a structured error, propagate it
            if isinstance(finalresp, dict) and not finalresp.get("status", True):
                return {"status": False, "error_stage": "playwright_agent", "error_message": finalresp.get("data")}

            # Ensure finalresp is JSON if it's a string
            while isinstance(finalresp, str):
                try:
                    finalresp = json.loads(finalresp)
                    break
                except json.JSONDecodeError:
                    break


            return finalresp

        
            # return {
            #     "success": True,
            #     "result_json": result_json,
            #     "parameterized_json": parameterized_json,
            #     "parameters": parameters
            # }

    async def send_agent_config_to_api(self, agent_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Send agent configuration to the specified API endpoint.
        
        Args:
            agent_config (Dict[str, Any]): The agent configuration to send
            
        Returns:
            Dict[str, Any]: Success result or error dict
        """
        api_url = "https://email-ai-test.comorin.co/api/agent-a2a-server/agents/"
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    api_url,
                    json=agent_config,
                    headers={
                        'Content-Type': 'application/json',
                        'user-id' : '6898dea1b3d293adf07b2a97'
                        # Add any additional headers if needed (e.g., Authorization)
                        # 'Authorization': 'Bearer your-token-here'
                    },
                    timeout=aiohttp.ClientTimeout(total=30)  # 30 second timeout
                ) as response:
                    
                    # Check if request was successful
                    if response.status >= 200 and response.status < 300:
                        response_data = await response.json()
                        return {
                            "success": True,
                            "status_code": response.status,
                            "response_data": response_data
                        }
                    else:
                        error_text = await response.text()
                        return {
                            "error": f"API request failed with status {response.status}: {error_text}",
                            "status_code": response.status
                        }
                        
        except asyncio.TimeoutError:
            return {"error": "API request timed out after 30 seconds"}
        except aiohttp.ClientError as e:
            return {"error": f"Network error occurred: {str(e)}"}
        except Exception as e:
            return {"error": f"Unexpected error occurred: {str(e)}"} 
        
    
    async def send_agent_category_to_api(self, agent_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Send agent category to the specified API endpoint.
        
        Args:
            agent_config (Dict[str, Any]): The agent configuration to send
            
        Returns:
            Dict[str, Any]: Success result or error dict
        """
        api_url = "https://email-ai-test.comorin.co/api/agent-creation/a2a/categories"
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    api_url,
                    json=agent_config,
                    headers={
                        'Content-Type': 'application/json',
                        'user-id' : '6898dea1b3d293adf07b2a97'
                        # Add any additional headers if needed (e.g., Authorization)
                        # 'Authorization': 'Bearer your-token-here'
                    },
                    timeout=aiohttp.ClientTimeout(total=30)  # 30 second timeout
                ) as response:
                    
                    # Check if request was successful
                    if response.status >= 200 and response.status < 300:
                        response_data = await response.json()
                        return {
                            "success": True,
                            "status_code": response.status,
                            "response_data": response_data
                        }
                    else:
                        error_text = await response.text()
                        return {
                            "error": f"API request failed with status {response.status}: {error_text}",
                            "status_code": response.status
                        }
                        
        except asyncio.TimeoutError:
            return {"error": "API request timed out after 30 seconds"}
        except aiohttp.ClientError as e:
            return {"error": f"Network error occurred: {str(e)}"}
        except Exception as e:
            return {"error": f"Unexpected error occurred: {str(e)}"} 
        
    async def process_multiple_files(self, input_paths: list) -> list:
        """
        Process multiple video analysis files concurrently.
        
        Args:
            input_paths (list): List of paths to input JSON files.
            
        Returns:
            list: List of tuples containing (result_json, parameterized_json, parameters) for each file.
        """
        print(f"Processing {len(input_paths)} files concurrently...")
        
        tasks = [
            asyncio.create_task(self.process_video_analysis(path))
            for path in input_paths
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle any exceptions
        successful_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                print(f"Error processing file {input_paths[i]}: {result}")
            else:
                successful_results.append(result)
        
        print(f"Successfully processed {len(successful_results)} out of {len(input_paths)} files")
        return successful_results


async def main(input_json_path,isIntent=None):
    """Main async function demonstrating usage of the AsyncVideoFrameAnalyzer class."""
    # # Path to input JSON file
    # if not input_json_path:
    #         if len(sys.argv) < 2:
    #             print("Usage: python parameterization.py <input_json_path>")
    #             return None
    #         input_json_path = sys.argv[1]

    print("call comes >>>>")
    # Initialize analyzer
    analyzer = AsyncVideoFrameAnalyzer()
    print("pathy",input_json_path)
    # Process single file
    result = await analyzer.process_video_analysis(input_json_path,isIntent)
    print("result")
    if result.get("success"):
        print("Analysis completed successfully!")
        return result["result_json"]
    else:
        print(f"Analysis failed: {result.get('error', 'Unknown error')}")
        return result


if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python parameterizationIntent.py <input_json_path>")
        sys.exit(1)  # Exit with error code

    path_arg = sys.argv[1]
    asyncio.run(main(path_arg))
